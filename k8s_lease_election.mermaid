sequenceDiagram
    participant C1 as Controller-1
    participant API as K8s API Server
    participant Lease as Lease Resource
    participant C2 as Controller-2

    Note over C1,C2: 初始化阶段
    C1->>API: 创建 Lease 锁对象<br/>(name: election-name, namespace: controller-ns)
    C2->>API: 尝试获取相同 Lease 对象

    rect rgb(200, 230, 240)
        Note over C1,C2: Leader 选举循环
        C1->>API: Try Acquire Lease<br/>LeaseDuration: 15s<br/>RenewDeadline: 10s<br/>RetryPeriod: 2s
        C2->>API: Try Acquire Lease
        API->>Lease: Check & Update
        
        alt 成功获取租约
            Lease-->>C1: Success (Became Leader)
            Lease-->>C2: Failed (Became Follower)
            
            Note over C1: OnStartedLeading Callback
            
            loop 租约续期 (每2秒)
                C1->>API: Renew Lease
                API->>Lease: Update renewTime
            end
        end
    end

    rect rgb(255, 220, 220)
        Note over C1,C2: 故障转移场景
        Note over C1: Leader 故障
        C1-xAPI: 续租失败
        Note over Lease: Lease 过期
        C2->>API: 获取 Lease
        API->>Lease: Check & Update
        Lease-->>C2: Success (New Leader)
        Note over C2: OnStartedLeading Callback
    end

    rect rgb(220, 255, 220)
        Note over C1,C2: 优雅退出
        C1->>API: Release Lease<br/>(ReleaseOnCancel: true)
        API->>Lease: Clear Leader
        C2->>API: Acquire Lease
        Lease-->>C2: Success (New Leader)
    end