name: build cli

on:
  push:
    branches:
      - main
      - v6.[1-9]
      - v7.[1-9]
      - 'feature-**'
    paths:
      - 'cli/**'
    tags:
      - '*'
  workflow_dispatch:
    inputs:
      ref:
        description: "Why trigger?"
        required: true
        type: string

jobs:
  build_cli:
    name: build cli
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@master
        with:
          go-version: 1.24.x

      - name: Set up GOPATH env
        run: echo "GOPATH=$(go env GOPATH)" >> "$GITHUB_ENV"

      - name: Install Protoc
        uses: arduino/setup-protoc@v1
        with:
          version: '3.6.1'
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout github.com/gogo/protobuf
        uses: actions/checkout@v3
        with:
          repository: 'gogo/protobuf'
          path: "protobuf"
          ref: 'v1.3.2'
          fetch-depth: 1

      - name: Move github.com/gogo/protobuf to $GOPATH/src
        run: |
          mkdir -p "${{ env.GOPATH }}/src/github.com/gogo"
          mv protobuf "${{ env.GOPATH }}/src/github.com/gogo/protobuf"

      - name: build cli
        run: |
          sudo apt-get install tmpl
          pip install ujson

          cd cli
          go install github.com/gogo/protobuf/protoc-gen-gofast
          go install github.com/gogo/protobuf/proto
          go install github.com/gogo/protobuf/jsonpb
          go install github.com/gogo/protobuf/protoc-gen-gogo
          go install github.com/gogo/protobuf/gogoproto
          go install github.com/golang/protobuf/protoc-gen-go
          go install golang.org/x/tools/cmd/stringer@v0.29.0

          CGO_ENABLED=0 GOOS=linux GOARCH=amd64 make -e BINARY_SUFFIX=.linux-amd64 -e BRANCH=${{ github.ref_name }}
          CGO_ENABLED=0 GOOS=linux GOARCH=arm64 make -e BINARY_SUFFIX=.linux-arm64 -e BRANCH=${{ github.ref_name }}
          CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 make -e BINARY_SUFFIX=.darwin-amd64 -e BRANCH=${{ github.ref_name }}
          CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 make -e BINARY_SUFFIX=.darwin-arm64 -e BRANCH=${{ github.ref_name }}
          cd bin/
          sha256sum * > deepflow-agent.sha256sum.txt

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: deepflow-cli
          path: ./cli/bin/*

      - uses: manyuanrong/setup-ossutil@v2.0
        with:
          endpoint: "oss-accelerate.aliyuncs.com"
          access-key-id: "${{ secrets.ALIYUN_OSS_ACCESS_KEY }}"
          access-key-secret: "${{ secrets.ALIYUN_OSS_SECRETS_KEY }}"

      - name: set env
        run: |
          echo "IMAGE_TAG_PREFIX=${{ github.ref_name }}"|sed 's|=main$|=latest|' >> $GITHUB_ENV
          echo "IMAGE_TAG=$(git rev-list --count HEAD)" >> $GITHUB_ENV

      - name: upload cli
        run: |
          ossutil cp -rf cli/bin/deepflow-ctl.linux-amd64 oss://deepflow-ce/bin/ctl/${{ env.IMAGE_TAG_PREFIX }}/linux/amd64/deepflow-ctl
          ossutil cp -rf cli/bin/deepflow-ctl.linux-arm64 oss://deepflow-ce/bin/ctl/${{ env.IMAGE_TAG_PREFIX }}/linux/arm64/deepflow-ctl
          ossutil cp -rf cli/bin/deepflow-ctl.darwin-amd64 oss://deepflow-ce/bin/ctl/${{ env.IMAGE_TAG_PREFIX }}/darwin/amd64/deepflow-ctl
          ossutil cp -rf cli/bin/deepflow-ctl.darwin-arm64 oss://deepflow-ce/bin/ctl/${{ env.IMAGE_TAG_PREFIX }}/darwin/arm64/deepflow-ctl

      - name: upload cli stable artifacts
        if: "startsWith(github.ref, 'refs/tags/')"
        run: |
          ossutil cp -rf cli/bin/deepflow-ctl.linux-amd64 oss://deepflow-ce/bin/ctl/stable/linux/amd64/deepflow-ctl
          ossutil cp -rf cli/bin/deepflow-ctl.linux-arm64 oss://deepflow-ce/bin/ctl/stable/linux/arm64/deepflow-ctl
          ossutil cp -rf cli/bin/deepflow-ctl.darwin-amd64 oss://deepflow-ce/bin/ctl/stable/darwin/amd64/deepflow-ctl
          ossutil cp -rf cli/bin/deepflow-ctl.darwin-arm64 oss://deepflow-ce/bin/ctl/stable/darwin/arm64/deepflow-ctl

      # - name: Prepare for upload package
      #   shell: bash
      #   run: |
      #     sha256sum dist/* > deepflow-agent.sha256sum.txt

      # - name: Release and upload packages
      #   uses: softprops/action-gh-release@v1
      #   if: startsWith(github.ref, 'refs/tags/')
      #   env:
      #     GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      #   with:
      #     files: |
      #       deepflow-agent.sha256sum.txt
      #       dist/*
