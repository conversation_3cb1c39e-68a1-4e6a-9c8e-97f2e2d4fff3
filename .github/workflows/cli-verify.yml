name: verify cli

on:
  pull_request:
    paths:
      - 'cli/**'

jobs:
  verify_cli:
    name: verify cli
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@master
        with:
          go-version: 1.24.x

      - name: Set up GOPATH env
        run: echo "GOPATH=$(go env GOPATH)" >> "$GITHUB_ENV"

      - name: Install Protoc
        uses: arduino/setup-protoc@v1
        with:
          version: '3.6.1'
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout github.com/gogo/protobuf
        uses: actions/checkout@v3
        with:
          repository: 'gogo/protobuf'
          path: "protobuf"
          ref: 'v1.3.2'
          fetch-depth: 1

      - name: Move github.com/gogo/protobuf to $GOPATH/src
        run: |
          mkdir -p "${{ env.GOPATH }}/src/github.com/gogo"
          mv protobuf "${{ env.GOPATH }}/src/github.com/gogo/protobuf"

      - name: verify cli
        run: |
          sudo apt-get install tmpl
          pip install ujson

          cd cli
          go install github.com/gogo/protobuf/protoc-gen-gofast
          go install github.com/gogo/protobuf/proto
          go install github.com/gogo/protobuf/jsonpb
          go install github.com/gogo/protobuf/protoc-gen-gogo
          go install github.com/gogo/protobuf/gogoproto
          go install github.com/golang/protobuf/protoc-gen-go
          go install golang.org/x/tools/cmd/stringer@v0.29.0

          go mod tidy
          go fmt ./...
          git diff
          go fmt ./...; [[ -z $(git status -s --ignore-submodule) ]] || exit -1
          make
