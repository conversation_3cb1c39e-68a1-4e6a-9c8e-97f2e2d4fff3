on:
  push:
    branches:
      - main
      - v6.[1-9]
      - v7.[1-9]
      - 'feature-**'
    paths:
      - 'agent/**'
      - 'message/**'
    tags:
      - '*'
  workflow_dispatch:
    inputs:
      ref:
        description: "Why trigger?"
        required: true
        type: string

name: build agent

env:
  IMAGE: "deepflow-agent"

jobs:
  build_agent:
    name: build agent
    runs-on: "cirun-aws-amd64-32c--${{ github.run_id }}"
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      # - name: install docker
      #   run: |
      #     sudo systemctl stop unattended-upgrades
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: Build  deepflow agent
        uses: docker/build-push-action@v2
        with:
          context: .
          push: false
          file: agent/docker/dockerfile-build
          platforms: linux/amd64
          outputs: type=local,dest=./agent/output/
          build-args: |
            GITHUB_REF_NAME=${{ github.ref_name }}

      - name: Check dynamic dependencies
        run: |
          required_libs=()
          in_arm_section=false
          while IFS= read -r line; do
              if [[ "$line" =~ ^\#\ X86\ Dynamic\ Libraries ]]; then
                  in_arm_section=true
                  continue
              fi

              if [[ "$line" =~ ^\#\  ]] && [[ "$in_arm_section" = true ]]; then
                  break
              fi

              if [[ "$in_arm_section" = true ]] && [[ "$line" =~ \*\*([^\*]+)\*\* ]]; then
                  required_libs+=("${BASH_REMATCH[1]}")
              fi
          done < agent/README.md
          dynamic_libs=$(ldd agent/output/target/release/deepflow-agent | awk '{print $1}' | xargs -n1 basename)
          echo "All dynamic dependency: ${dynamic_libs}"
          for lib in $dynamic_libs; do
              if [[ ! " ${required_libs[@]} " =~ " ${lib} " ]]; then
                  echo "Warning: Do not introduce any additional dynamic library dependencies!: ${lib}"
                  exit 1
              fi
          done

      - name: Package code build results
        run: |
          tar cvf agent-artifact-amd64.tar agent/output/target/release/deepflow-agent agent/output/target/release/deepflow-agent-ctl agent/output/src/ebpf/deepflow-ebpfctl
          curl -o agent/output/target/release/ecapture https://deepflow-ce.oss-cn-beijing.aliyuncs.com/pkg/compile/x86_64/ecapture
          chmod a+x agent/output/target/release/ecapture

      - name: Archive code build results
        uses: actions/upload-artifact@v4
        with:
          name: agent build results amd64
          path: |
            agent-artifact-amd64.tar

      - name: set env
        run: |
          echo "IMAGE_TAG_PREFIX=${{ github.ref_name }}"|sed 's|=main$|=latest|' >> $GITHUB_ENV
          echo "IMAGE_TAG=$(git rev-list --count HEAD)" >> $GITHUB_ENV

      - name: build rpm
        run: |
          cd agent
          rpmbuild -bb pkg/centos/deepflow-agent.spec -D '_rpmdir .' --buildroot $(pwd)/.rpmbuild
          zip -r -q artifacts-rpm.zip x86_64/*.rpm

      - name: build deb
        run: |
          cd agent
          mkdir -p pkg/debian/systemd/usr/sbin/
          mkdir -p pkg/debian/systemd/usr/bin/
          mkdir -p pkg/debian/systemd/usr/lib/x86_64-linux-gnu/
          cp -af output/target/release/deepflow-agent pkg/debian/systemd/usr/sbin/
          cp -af output/target/release/ecapture pkg/debian/systemd/usr/bin/
          cp -af docker/require/x86_64/libpcap.so.1 pkg/debian/systemd/usr/lib/x86_64-linux-gnu/
          mkdir -p pkg/debian/systemd/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/systemd/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/systemd/etc/deepflow-agent.yaml.sample
          mkdir -p pkg/debian/systemd/etc/systemd/system/
          cp -af pkg/deepflow-agent.service pkg/debian/systemd/etc/systemd/system/
          sed -i "s/Version.*/Version: 1.0-${{ env.IMAGE_TAG }}/g" pkg/debian/systemd/DEBIAN/control
          dpkg-deb -Zxz --no-uniform-compression -b pkg/debian/systemd x86_64/deepflow-agent-1.0-${{ env.IMAGE_TAG }}.systemd.deb
          mkdir -p pkg/debian/upstart/usr/sbin/
          mkdir -p pkg/debian/upstart/usr/bin/
          mkdir -p pkg/debian/upstart/usr/lib/x86_64-linux-gnu/
          cp -af output/target/release/deepflow-agent pkg/debian/upstart/usr/sbin/
          cp -af output/target/release/ecapture pkg/debian/upstart/usr/bin/
          cp -af docker/require/x86_64/libpcap.so.1 pkg/debian/upstart/usr/lib/x86_64-linux-gnu/
          mkdir -p pkg/debian/upstart/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/upstart/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/upstart/etc/deepflow-agent.yaml.sample
          mkdir -p pkg/debian/upstart/etc/init/
          cp -af pkg/deepflow-agent.conf pkg/debian/upstart/etc/init/
          sed -i "s/Version.*/Version: 1.0-${{ env.IMAGE_TAG }}/g" pkg/debian/upstart/DEBIAN/control
          dpkg-deb -Zxz --no-uniform-compression -b pkg/debian/upstart x86_64/deepflow-agent-1.0-${{ env.IMAGE_TAG }}.upstart.deb
          zip -r -q artifacts-deb.zip x86_64/*.deb

      - name: build binary package
        run: |
          mkdir -p agent/bin-package
          cp -raf agent/output/target/release/deepflow-agent agent/bin-package/
          cd agent/bin-package/
          tar -czvf deepflow-agent.tar.gz *

      - name: build TenCentBlueKing package
        run: |
          cp -raf agent/output/target/release/deepflow-agent agent/pkg/TencentBlueKing/plugins_linux_x86_64/deepflow-agent/bin/
          cd agent/pkg/TencentBlueKing/
          sed -i "s/RELEASE_VERSION/${{ env.IMAGE_TAG_PREFIX }}/g" plugins_linux_x86_64/deepflow-agent/project.yaml
          tar -czvf deepflow-agent.tgz *

      - uses: manyuanrong/setup-ossutil@v2.0
        with:
          endpoint: "oss-accelerate.aliyuncs.com"
          access-key-id: "${{ secrets.ALIYUN_OSS_ACCESS_KEY }}"
          access-key-secret: "${{ secrets.ALIYUN_OSS_SECRETS_KEY }}"

      - name: upload agent artifacts
        run: |
          ossutil cp -rf agent/artifacts-rpm.zip oss://deepflow-ce/rpm/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/amd64/deepflow-agent-rpm.zip
          ossutil cp -rf agent/artifacts-deb.zip oss://deepflow-ce/deb/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/amd64/deepflow-agent-deb.zip
          ossutil cp -rf agent/output/target/release/deepflow-agent-ctl oss://deepflow-ce/bin/ctl/${{ env.IMAGE_TAG_PREFIX }}/linux/amd64/deepflow-agent-ctl
          ossutil cp -rf agent/bin-package/deepflow-agent.tar.gz  oss://deepflow-ce/bin/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/amd64/deepflow-agent.tar.gz
          ossutil cp -rf agent/pkg/TencentBlueKing/deepflow-agent.tgz oss://deepflow-ce/tencentblueking/agent/${{ env.IMAGE_TAG_PREFIX }}/deepflow-agent.tgz

      - name: upload agent stable artifacts
        if: "startsWith(github.ref, 'refs/tags/')"
        run: |
          ossutil cp -rf agent/artifacts-rpm.zip oss://deepflow-ce/rpm/agent/stable/linux/amd64/deepflow-agent-rpm.zip
          ossutil cp -rf agent/artifacts-deb.zip oss://deepflow-ce/deb/agent/stable/linux/amd64/deepflow-agent-deb.zip
          ossutil cp -rf agent/output/target/release/deepflow-agent-ctl oss://deepflow-ce/bin/ctl/stable/linux/amd64/deepflow-agent-ctl
          ossutil cp -rf agent/bin-package/deepflow-agent.tar.gz  oss://deepflow-ce/bin/agent/stable/linux/amd64/deepflow-agent.tar.gz
          ossutil cp -rf agent/pkg/TencentBlueKing/deepflow-agent.tgz oss://deepflow-ce/tencentblueking/agent/stable/deepflow-agent.tgz

  build_agent_arm64:
    name: build agent arm64
    runs-on: "cirun-aws-arm64-32c--${{ github.run_id }}"
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      # - name: install docker
      #   run: |
      #     sudo systemctl stop unattended-upgrades
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version
      # - name: Setup tmate session
      #   uses: mxschmitt/action-tmate@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: Build  deepflow agent
        uses: docker/build-push-action@v2
        with:
          context: .
          push: false
          file: agent/docker/dockerfile-build-aarch64
          platforms: linux/arm64
          outputs: type=local,dest=./agent/output/
          build-args: |
            GITHUB_REF_NAME=${{ github.ref_name }}

      - name: Check dynamic dependencies
        run: |
          required_libs=()
          in_arm_section=false
          while IFS= read -r line; do
              if [[ "$line" =~ ^\#\ ARM\ Dynamic\ Libraries ]]; then
                  in_arm_section=true
                  continue
              fi

              if [[ "$line" =~ ^\#\  ]] && [[ "$in_arm_section" = true ]]; then
                  break
              fi

              if [[ "$in_arm_section" = true ]] && [[ "$line" =~ \*\*([^\*]+)\*\* ]]; then
                  required_libs+=("${BASH_REMATCH[1]}")
              fi
          done < agent/README.md
          dynamic_libs=$(ldd agent/output/target/release/deepflow-agent | awk '{print $1}' | xargs -n1 basename)
          echo "All dynamic dependency: ${dynamic_libs}"
          for lib in $dynamic_libs; do
              if [[ ! " ${required_libs[@]} " =~ " ${lib} " ]]; then
                  echo "Warning: Do not introduce any additional dynamic library dependencies!: ${lib}"
                  exit 1
              fi
          done

      - name: Package code build results
        run: |
          tar cvf agent-artifact-arm64.tar agent/output/target/release/deepflow-agent agent/output/target/release/deepflow-agent-ctl agent/output/src/ebpf/deepflow-ebpfctl
          curl -o agent/output/target/release/ecapture https://deepflow-ce.oss-cn-beijing.aliyuncs.com/pkg/compile/aarch64/ecapture
          chmod a+x agent/output/target/release/ecapture

      - name: Archive code build results
        uses: actions/upload-artifact@v4
        with:
          name: agent build results arm64
          path: |
            agent-artifact-arm64.tar

      - name: set env
        run: |
          echo "IMAGE_TAG_PREFIX=${{ github.ref_name }}"|sed 's|=main$|=latest|' >> $GITHUB_ENV
          echo "IMAGE_TAG=$(git rev-list --count HEAD)" >> $GITHUB_ENV

      - name: build rpm
        run: |
          cd agent
          rpmbuild -bb pkg/centos/deepflow-agent-arm64.spec -D '_rpmdir .' --buildroot $(pwd)/.rpmbuild
          zip -r -q artifacts-rpm.zip aarch64/*.rpm

      - name: build deb
        run: |
          cd agent
          mkdir -p pkg/debian/systemd/usr/sbin/
          mkdir -p pkg/debian/systemd/usr/bin/
          mkdir -p pkg/debian/systemd/lib/aarch64-linux-gnu/
          cp -af output/target/release/deepflow-agent pkg/debian/systemd/usr/sbin/
          cp -af output/target/release/ecapture pkg/debian/systemd/usr/bin/
          cp -af docker/require/aarch64/libpcap.so.1 pkg/debian/systemd/lib/aarch64-linux-gnu/
          mkdir -p pkg/debian/systemd/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/systemd/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/systemd/etc/deepflow-agent.yaml.sample
          mkdir -p pkg/debian/systemd/etc/systemd/system/
          cp -af pkg/deepflow-agent.service pkg/debian/systemd/etc/systemd/system/
          sed -i "s/Version.*/Version: 1.0-${{ env.IMAGE_TAG }}/g" pkg/debian/systemd/DEBIAN/control
          dpkg-deb  -Zxz --no-uniform-compression -b pkg/debian/systemd aarch64/deepflow-agent-1.0-${{ env.IMAGE_TAG }}.systemd.deb
          mkdir -p pkg/debian/upstart/usr/sbin/
          mkdir -p pkg/debian/upstart/usr/bin/
          mkdir -p pkg/debian/upstart/lib/aarch64-linux-gnu/
          cp -af output/target/release/deepflow-agent pkg/debian/upstart/usr/sbin/
          cp -af output/target/release/ecapture pkg/debian/upstart/usr/bin/
          cp -af docker/require/aarch64/libpcap.so.1 pkg/debian/upstart/lib/aarch64-linux-gnu/
          mkdir -p pkg/debian/upstart/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/upstart/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/upstart/etc/deepflow-agent.yaml.sample
          mkdir -p pkg/debian/upstart/etc/init/
          cp -af pkg/deepflow-agent.conf pkg/debian/upstart/etc/init/
          sed -i "s/Version.*/Version: 1.0-${{ env.IMAGE_TAG }}/g" pkg/debian/upstart/DEBIAN/control
          dpkg-deb  -Zxz --no-uniform-compression -b pkg/debian/upstart aarch64/deepflow-agent-1.0-${{ env.IMAGE_TAG }}.upstart.deb
          zip -r -q artifacts-deb.zip aarch64/*.deb

      - name: build binary package
        run: |
          mkdir -p agent/bin-package
          cp -raf agent/output/target/release/deepflow-agent agent/bin-package/
          cd agent/bin-package/
          tar -czvf deepflow-agent.tar.gz *

      - name: Arm64 package results
        run: |
          tar cvf package-artifact-arm64.tar  agent/artifacts-rpm.zip agent/artifacts-deb.zip agent/bin-package/deepflow-agent.tar.gz agent/output/target/release/deepflow-agent-ctl

      - name: Archive Arm64 package results
        uses: actions/upload-artifact@v4
        with:
          name: agent arm64 package results
          path: |
            package-artifact-arm64.tar

  build_agent_image:
    name: build agent image
    needs:
    - build_agent
    - build_agent_arm64
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      # - name: install docker
      #   run: |
      #     sudo systemctl stop unattended-upgrades
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Download code build results
        uses: actions/download-artifact@v4
        with:
          name: agent build results arm64
          path: .

      - name: Download code build results
        uses: actions/download-artifact@v4
        with:
          name: agent build results amd64
          path: .

      - name: Download code build results
        uses: actions/download-artifact@v4
        with:
          name: agent arm64 package results
          path: .

      - name: Unpack code build results
        run: |
          mkdir -p x86_64
          mkdir -p aarch64
          tar xvf agent-artifact-amd64.tar -C x86_64
          tar xvf agent-artifact-arm64.tar -C aarch64
          tar xvf package-artifact-arm64.tar
          curl -o x86_64/agent/output/target/release/ecapture https://deepflow-ce.oss-cn-beijing.aliyuncs.com/pkg/compile/x86_64/ecapture
          chmod a+x x86_64/agent/output/target/release/ecapture
          curl -o aarch64/agent/output/target/release/ecapture https://deepflow-ce.oss-cn-beijing.aliyuncs.com/pkg/compile/aarch64/ecapture
          chmod a+x aarch64/agent/output/target/release/ecapture

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GHCR_PUSH_TOKEN }}"

      - name: Log in to Docker Registry
        uses: docker/login-action@v2
        with:
          username: "deepflowce"
          password: "${{ secrets.REGISTRY_PASS }}"

      - name: Log in to ALIYUN HongKong Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "registry.cn-hongkong.aliyuncs.com"
          username: "${{ secrets.REGISTRY_ALIYUN_USER }}"
          password: "${{ secrets.REGISTRY_PASS }}"

      - name: set env
        run: |
          echo "IMAGE_TAG_PREFIX=${{ github.ref_name }}"|sed 's|=main$|=latest|' >> $GITHUB_ENV
          echo "IMAGE_TAG=$(git rev-list --count HEAD)" >> $GITHUB_ENV
      - name: qemu workaround
        run: docker run --rm --privileged multiarch/qemu-user-static --reset -p yes -c yes
      - name: Build and push deepflow agent images to ghcr and dockerhub
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          file: agent/docker/dockerfile
          platforms: linux/amd64,linux/arm64
          tags: |
            "ghcr.io/${{ github.repository_owner }}/deepflow-ce/${{ env.IMAGE }}:${{ env.IMAGE_TAG_PREFIX }}-${{ env.IMAGE_TAG }}"
            "ghcr.io/${{ github.repository_owner }}/deepflow-ce/${{ env.IMAGE }}:${{ env.IMAGE_TAG_PREFIX }}"
            "deepflowce/${{ env.IMAGE }}:${{ env.IMAGE_TAG_PREFIX }}"

      - name: Build and push deepflow agent images to hongkong aliyun
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          file: agent/docker/dockerfile
          platforms: linux/amd64,linux/arm64
          tags: |
            "registry.cn-hongkong.aliyuncs.com/deepflow-ce/${{ env.IMAGE }}:${{ env.IMAGE_TAG_PREFIX }}-${{ env.IMAGE_TAG }}"
            "registry.cn-hongkong.aliyuncs.com/deepflow-ce/${{ env.IMAGE }}:${{ env.IMAGE_TAG_PREFIX }}"

      - name: Log in to ALIYUN Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "registry.cn-beijing.aliyuncs.com"
          username: "${{ secrets.REGISTRY_ALIYUN_USER }}"
          password: "${{ secrets.REGISTRY_PASS }}"

      - name: Build and push deepflow agent images to beijing aliyun
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          file: agent/docker/dockerfile
          platforms: linux/amd64,linux/arm64
          tags: |
            "registry.cn-beijing.aliyuncs.com/deepflow-ce/${{ env.IMAGE }}:${{ env.IMAGE_TAG_PREFIX }}-${{ env.IMAGE_TAG }}"
            "registry.cn-beijing.aliyuncs.com/deepflow-ce/${{ env.IMAGE }}:${{ env.IMAGE_TAG_PREFIX }}"

      - uses: manyuanrong/setup-ossutil@v2.0
        with:
          endpoint: "oss-accelerate.aliyuncs.com"
          access-key-id: "${{ secrets.ALIYUN_OSS_ACCESS_KEY }}"
          access-key-secret: "${{ secrets.ALIYUN_OSS_SECRETS_KEY }}"

      - name: upload agent artifacts
        run: |
          ossutil cp -rf agent/artifacts-rpm.zip oss://deepflow-ce/rpm/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/arm64/deepflow-agent-rpm.zip
          ossutil cp -rf agent/artifacts-deb.zip oss://deepflow-ce/deb/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/arm64/deepflow-agent-deb.zip
          ossutil cp -rf agent/output/target/release/deepflow-agent-ctl oss://deepflow-ce/bin/ctl/${{ env.IMAGE_TAG_PREFIX }}/linux/arm64/deepflow-agent-ctl
          ossutil cp -rf agent/bin-package/deepflow-agent.tar.gz  oss://deepflow-ce/bin/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/arm64/deepflow-agent.tar.gz

      - name: upload agent stable artifacts
        if: "startsWith(github.ref, 'refs/tags/')"
        run: |
          ossutil cp -rf agent/artifacts-rpm.zip oss://deepflow-ce/rpm/agent/stable/linux/arm64/deepflow-agent-rpm.zip
          ossutil cp -rf agent/artifacts-deb.zip oss://deepflow-ce/deb/agent/stable/linux/arm64/deepflow-agent-deb.zip
          ossutil cp -rf agent/output/target/release/deepflow-agent-ctl oss://deepflow-ce/bin/ctl/stable/linux/arm64/deepflow-agent-ctl
          ossutil cp -rf agent/bin-package/deepflow-agent.tar.gz  oss://deepflow-ce/bin/agent/stable/linux/arm64/deepflow-agent.tar.gz

  build_agent_static_link:
    name: build agent static link
    runs-on: "cirun-aws-amd64-32c--${{ github.run_id }}"
    if: "startsWith(github.ref, 'refs/tags/')"
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      # - name: install docker
      #   run: |
      #     sudo systemctl stop unattended-upgrades
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: Build  deepflow agent
        uses: docker/build-push-action@v2
        with:
          context: .
          push: false
          file: agent/docker/dockerfile-build-static-link
          platforms: linux/amd64
          outputs: type=local,dest=./agent/output/
          build-args: |
            GITHUB_REF_NAME=${{ github.ref_name }}

      - name: set env
        run: |
          echo "IMAGE_TAG_PREFIX=${{ github.ref_name }}"|sed 's|=main$|=latest|' >> $GITHUB_ENV
          echo "IMAGE_TAG=$(git rev-list --count HEAD)" >> $GITHUB_ENV

      - name: build rpm
        run: |
          cd agent
          rpmbuild -bb pkg/centos/deepflow-agent-static-link.spec -D '_rpmdir .' --buildroot $(pwd)/.rpmbuild
          zip -r -q artifacts-rpm.zip x86_64/*.rpm

      - name: build deb
        run: |
          cd agent
          mkdir -p pkg/debian/systemd/usr/sbin/
          cp -af output/target/x86_64-unknown-linux-musl/release/deepflow-agent pkg/debian/systemd/usr/sbin/
          mkdir -p pkg/debian/systemd/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/systemd/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/systemd/etc/deepflow-agent.yaml.sample
          mkdir -p pkg/debian/systemd/etc/systemd/system/
          cp -af pkg/deepflow-agent.service pkg/debian/systemd/etc/systemd/system/
          sed -i "s/Version.*/Version: 1.0-${{ env.IMAGE_TAG }}/g" pkg/debian/systemd/DEBIAN/control
          dpkg-deb -Zxz --no-uniform-compression -b pkg/debian/systemd x86_64/deepflow-agent-1.0-${{ env.IMAGE_TAG }}.systemd.deb
          mkdir -p pkg/debian/upstart/usr/sbin/
          cp -af output/target/x86_64-unknown-linux-musl/release/deepflow-agent pkg/debian/upstart/usr/sbin/
          mkdir -p pkg/debian/upstart/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/upstart/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/upstart/etc/deepflow-agent.yaml.sample
          mkdir -p pkg/debian/upstart/etc/init/
          cp -af pkg/deepflow-agent.conf pkg/debian/upstart/etc/init/
          sed -i "s/Version.*/Version: 1.0-${{ env.IMAGE_TAG }}/g" pkg/debian/upstart/DEBIAN/control
          dpkg-deb -Zxz --no-uniform-compression -b pkg/debian/upstart x86_64/deepflow-agent-1.0-${{ env.IMAGE_TAG }}.upstart.deb
          zip -r -q artifacts-deb.zip x86_64/*.deb

      - name: build binary package
        run: |
          mkdir -p agent/bin-package
          cp -raf agent/output/target/x86_64-unknown-linux-musl/release/deepflow-agent agent/bin-package/
          cd agent/bin-package/
          tar -czvf deepflow-agent.tar.gz *

      - name: build TenCentBlueKing package
        run: |
          cp -raf agent/output/target/x86_64-unknown-linux-musl/release/deepflow-agent agent/pkg/TencentBlueKing/plugins_linux_x86_64/deepflow-agent/bin/
          cd agent/pkg/TencentBlueKing/
          sed -i "s/RELEASE_VERSION/${{ env.IMAGE_TAG_PREFIX }}/g" plugins_linux_x86_64/deepflow-agent/project.yaml
          tar -czvf deepflow-agent.tgz *

      - uses: manyuanrong/setup-ossutil@v2.0
        with:
          endpoint: "oss-accelerate.aliyuncs.com"
          access-key-id: "${{ secrets.ALIYUN_OSS_ACCESS_KEY }}"
          access-key-secret: "${{ secrets.ALIYUN_OSS_SECRETS_KEY }}"

      - name: upload agent artifacts
        run: |
          ossutil cp -rf agent/artifacts-rpm.zip oss://deepflow-ce/rpm/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/static-link/amd64/deepflow-agent-rpm.zip
          ossutil cp -rf agent/artifacts-deb.zip oss://deepflow-ce/deb/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/static-link/amd64/deepflow-agent-deb.zip
          ossutil cp -rf agent/bin-package/deepflow-agent.tar.gz  oss://deepflow-ce/bin/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/static-link/amd64/deepflow-agent.tar.gz
          ossutil cp -rf agent/pkg/TencentBlueKing/deepflow-agent.tgz oss://deepflow-ce/tencentblueking/agent/static-link/${{ env.IMAGE_TAG_PREFIX }}/deepflow-agent.tgz

      - name: upload agent stable artifacts
        run: |
          ossutil cp -rf agent/artifacts-rpm.zip oss://deepflow-ce/rpm/agent/stable/linux/static-link/amd64/deepflow-agent-rpm.zip
          ossutil cp -rf agent/artifacts-deb.zip oss://deepflow-ce/deb/agent/stable/linux/static-link/amd64/deepflow-agent-deb.zip
          ossutil cp -rf agent/bin-package/deepflow-agent.tar.gz  oss://deepflow-ce/bin/agent/stable/linux/static-link/amd64/deepflow-agent.tar.gz
          ossutil cp -rf agent/pkg/TencentBlueKing/deepflow-agent.tgz oss://deepflow-ce/tencentblueking/agent/static-link/stable/deepflow-agent.tgz

  build_agent_arm64_static_link:
    name: build agent arm64 static link
    runs-on: "cirun-aws-arm64-32c--${{ github.run_id }}"
    if: "startsWith(github.ref, 'refs/tags/')"
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      # - name: install docker
      #   run: |
      #     sudo systemctl stop unattended-upgrades
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version
      # - name: Setup tmate session
      #   uses: mxschmitt/action-tmate@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: Build  deepflow agent
        uses: docker/build-push-action@v2
        with:
          context: .
          push: false
          file: agent/docker/dockerfile-build-aarch64-static-link
          platforms: linux/arm64
          outputs: type=local,dest=./agent/output/
          build-args: |
            GITHUB_REF_NAME=${{ github.ref_name }}

      - name: set env
        run: |
          echo "IMAGE_TAG_PREFIX=${{ github.ref_name }}"|sed 's|=main$|=latest|' >> $GITHUB_ENV
          echo "IMAGE_TAG=$(git rev-list --count HEAD)" >> $GITHUB_ENV

      - name: build rpm
        run: |
          cd agent
          rpmbuild -bb pkg/centos/deepflow-agent-arm64-static-link.spec -D '_rpmdir .' --buildroot $(pwd)/.rpmbuild
          zip -r -q artifacts-rpm.zip aarch64/*.rpm

      - name: build deb
        run: |
          cd agent
          mkdir -p pkg/debian/systemd/usr/sbin/
          cp -af output/target/aarch64-unknown-linux-musl/release/deepflow-agent pkg/debian/systemd/usr/sbin/
          mkdir -p pkg/debian/systemd/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/systemd/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/systemd/etc/deepflow-agent.yaml.sample
          mkdir -p pkg/debian/systemd/etc/systemd/system/
          cp -af pkg/deepflow-agent.service pkg/debian/systemd/etc/systemd/system/
          sed -i "s/Version.*/Version: 1.0-${{ env.IMAGE_TAG }}/g" pkg/debian/systemd/DEBIAN/control
          dpkg-deb  -Zxz --no-uniform-compression -b pkg/debian/systemd aarch64/deepflow-agent-1.0-${{ env.IMAGE_TAG }}.systemd.deb
          mkdir -p pkg/debian/upstart/usr/sbin/
          cp -af output/target/aarch64-unknown-linux-musl/release/deepflow-agent pkg/debian/upstart/usr/sbin/
          mkdir -p pkg/debian/upstart/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/upstart/etc/
          cp -af config/deepflow-agent.yaml pkg/debian/upstart/etc/deepflow-agent.yaml.sample
          mkdir -p pkg/debian/upstart/etc/init/
          cp -af pkg/deepflow-agent.conf pkg/debian/upstart/etc/init/
          sed -i "s/Version.*/Version: 1.0-${{ env.IMAGE_TAG }}/g" pkg/debian/upstart/DEBIAN/control
          dpkg-deb  -Zxz --no-uniform-compression -b pkg/debian/upstart aarch64/deepflow-agent-1.0-${{ env.IMAGE_TAG }}.upstart.deb
          zip -r -q artifacts-deb.zip aarch64/*.deb

      - name: build binary package
        run: |
          mkdir -p agent/bin-package
          cp -raf agent/output/target/aarch64-unknown-linux-musl/release/deepflow-agent agent/bin-package/
          cd agent/bin-package/
          tar -czvf deepflow-agent.tar.gz *

      - name: Arm64 package results
        run: |
          tar cvf package-artifact-arm64.tar  agent/artifacts-rpm.zip agent/artifacts-deb.zip agent/bin-package/deepflow-agent.tar.gz agent/output/target/aarch64-unknown-linux-musl/release/deepflow-agent-ctl

      - name: Archive Arm64 package results
        uses: actions/upload-artifact@v4
        with:
          name: agent arm64 package results static link
          path: |
            package-artifact-arm64.tar

  upload_arm64_artifact:
    name: upload arm64 artifact
    runs-on: ubuntu-latest
    if: "startsWith(github.ref, 'refs/tags/')"
    needs:
    - build_agent_arm64_static_link
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      - name: Download code build results
        uses: actions/download-artifact@v4
        with:
          name: agent arm64 package results static link
          path: .

      - name: Unpack code build results
        run: |
          mkdir -p x86_64
          mkdir -p aarch64
          tar xvf package-artifact-arm64.tar

      - uses: manyuanrong/setup-ossutil@v2.0
        with:
          endpoint: "oss-accelerate.aliyuncs.com"
          access-key-id: "${{ secrets.ALIYUN_OSS_ACCESS_KEY }}"
          access-key-secret: "${{ secrets.ALIYUN_OSS_SECRETS_KEY }}"

      - name: upload agent artifacts
        run: |
          ossutil cp -rf agent/artifacts-rpm.zip oss://deepflow-ce/rpm/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/static-link/arm64/deepflow-agent-rpm.zip
          ossutil cp -rf agent/artifacts-deb.zip oss://deepflow-ce/deb/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/static-link/arm64/deepflow-agent-deb.zip
          ossutil cp -rf agent/bin-package/deepflow-agent.tar.gz  oss://deepflow-ce/bin/agent/${{ env.IMAGE_TAG_PREFIX }}/linux/static-link/arm64/deepflow-agent.tar.gz

      - name: upload agent stable artifacts
        run: |
          ossutil cp -rf agent/artifacts-rpm.zip oss://deepflow-ce/rpm/agent/stable/linux/static-link/arm64/deepflow-agent-rpm.zip
          ossutil cp -rf agent/artifacts-deb.zip oss://deepflow-ce/deb/agent/stable/linux/static-link/arm64/deepflow-agent-deb.zip
          ossutil cp -rf agent/bin-package/deepflow-agent.tar.gz  oss://deepflow-ce/bin/agent/stable/linux/static-link/arm64/deepflow-agent.tar.gz
