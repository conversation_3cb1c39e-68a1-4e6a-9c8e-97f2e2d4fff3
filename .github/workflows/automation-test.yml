name: automation test

on:
  workflow_dispatch:
    inputs:
      DEEPFLOW_SERVER_IMAGE_TAG:
        description: 'server image tag'
        required: true
        default: 'latest'
      DEEPFLOW_AGENT_IMAGE_TAG:
        description: 'agent image tag'
        required: true
        default: 'latest'
      WORKER_NUMBER:
        description: 'Worker number, recommend 1-12'
        required: false
        default: '6'
      DF_ENV_NUMBER:
        description: 'Deepflow env number, recommend 1-3'
        required: false
        default: '1'
      TEST_CASE:
        description: |
          case name, support: 
          [basic, querier, cloud_k8s, cloud_workloadv, upgrade]
        required: false
        default: 'basic'
      DF_TEST_BRANCH:
        description: 'df-test branch'
        required: false
        default: 'master'
      DEBUG:
        description: 'reserve case environment at end of the use case, 1 means reserve'
        required: false
        default: '0'

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 200
    steps:
    - name: Check Server And Agent Image
      run: |
        server_tag="${{ github.event.inputs.DEEPFLOW_SERVER_IMAGE_TAG }}"
        server=$(skopeo inspect docker://registry.cn-hongkong.aliyuncs.com/deepflow-ce/deepflow-server:${server_tag} 2>&1 | grep "Error parsing image name" || :)
        if [ -n "$server" ]; then
          echo "Get Server Image Failed: "
          echo "$server"
          exit 1
        fi
        agent_tag="${{ github.event.inputs.DEEPFLOW_AGENT_IMAGE_TAG }}"
        agent=$(skopeo inspect docker://registry.cn-hongkong.aliyuncs.com/deepflow-ce/deepflow-agent:${agent_tag} 2>&1 | grep "Error parsing image name" || :)
        if [ -n "$agent" ]; then
          echo "Get Agent Image Failed: "
          echo "$agent"
          exit 1
        fi
        exit 0

    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: 3.8
    - name: Install Pip
      run: |
        python -m ensurepip --default-pip
        python -m pip install --upgrade pip
    - name: Install Sio
      run: |
        pip install -r automation_test/requirements.txt
    - name: set environment vars
      run: |
        echo "LC_ALL=en_US.utf8" >> $GITHUB_ENV
        echo "WORKER_NUMBER=${{ github.event.inputs.WORKER_NUMBER }}" >> $GITHUB_ENV
        echo "DF_ENV_NUMBER=${{ github.event.inputs.DF_ENV_NUMBER }}" >> $GITHUB_ENV
        echo "TEST_CASE=${{ github.event.inputs.TEST_CASE }}" >> $GITHUB_ENV
        echo "DEEPFLOW_SERVER_IMAGE_TAG=${{ github.event.inputs.DEEPFLOW_SERVER_IMAGE_TAG }}" >> $GITHUB_ENV
        echo "DEEPFLOW_AGENT_IMAGE_TAG=${{ github.event.inputs.DEEPFLOW_AGENT_IMAGE_TAG }}" >> $GITHUB_ENV
        echo "DF_TEST_BRANCH=${{ github.event.inputs.DF_TEST_BRANCH }}" >> $GITHUB_ENV
        echo "DEBUG=${{ github.event.inputs.DEBUG }}" >> $GITHUB_ENV
        echo "AUTOMATION_TEST_TOKEN=${{ secrets.AUTOMATION_TEST_TOKEN }}" >> $GITHUB_ENV
        echo "AUTOMATION_TEST_PUBLIC_URL=${{ secrets.AUTOMATION_TEST_PUBLIC_URL }}" >> $GITHUB_ENV

    # - name: Setup Debug Session
    #   uses: csexton/debugger-action@master
    - name: run client
      run: |
        export PYTHONUNBUFFERED=1
        python3 automation_test/ws_client.py
  
