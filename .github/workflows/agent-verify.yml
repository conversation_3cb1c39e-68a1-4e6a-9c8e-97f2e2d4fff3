on:
   pull_request:
      paths:
        - 'agent/**'
        - 'message/**'

name: verify agent

jobs:
  verify_agent:
    name: verify agent
    runs-on: "cirun-aws-amd64-32c--${{ github.run_id }}"
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      # - name: install docker
      #   run: |
      #     sudo systemctl stop unattended-upgrades
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: verify agent
        run: |
          docker run --rm  -t  --privileged --workdir /deepflow/  -v $(pwd):"/deepflow/"  --entrypoint "bash" ghcr.io/deepflowio/rust-build:1.31 "-c" "\
          source /opt/rh/devtoolset-11/enable && \
          cp agent/docker/rust-proxy-config /usr/local/cargo/config && \
          cd  agent/src/ebpf && \
          make clean && \
          make rust-sample && \
          make test && \
          make tools && \
          cd ../../ && \
          RUSTFLAGS='-D warnings -C force-frame-pointers=yes' cargo check --all-targets && \
          cargo fmt --all -- --check"

  verify_agent_arm64:
    name: verify agent arm64
    runs-on: "cirun-aws-arm64-32c--${{ github.run_id }}"
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0

      # - name: install docker
      #   run: |
      #     sudo systemctl stop unattended-upgrades
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: verify agent
        run: |
          docker run --rm  -t  --privileged --workdir /deepflow/  -v $(pwd):"/deepflow/"  --entrypoint "bash" ghcr.io/deepflowio/rust-build:1.31-arm64 "-c" "\
          source /opt/rh/devtoolset-8/enable && \
          cd  agent/src/ebpf && \
          make clean && \
          make rust-sample && \
          make test && \
          make tools && \
          cd ../../ && \
          RUSTFLAGS='-D warnings' cargo check && \
          cargo fmt --all -- --check"

