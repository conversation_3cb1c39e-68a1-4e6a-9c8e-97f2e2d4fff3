on: 
  push:
    branches:
      - package-rust-env
    paths:
      - 'agent/docker/DockerfileToFix.build'
      - 'agent/docker/DockerfileToFix-aarch64.build'

name: build agent env

jobs:
  build_agent_env:
    name: build agent env
    runs-on: "cirun-aws-amd64-32c--${{ github.run_id }}"
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      # - name: install docker
      #   run: |
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          install: true

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io" 
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GHCR_PUSH_TOKEN }}" 

      - name: Log in to ALIYUN Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "${{ secrets.REGISTRY_ALIYUN_ADDR }}"
          username: "${{ secrets.REGISTRY_ALIYUN_USER }}" 
          password: "${{ secrets.REGISTRY_PASS }}"

      - name: Build and push deepflow agent env images
        uses: docker/build-push-action@v2
        with:
          context: agent
          push: true 
          file: agent/docker/DockerfileToFix.build
          platforms: linux/amd64
          tags: |
            "ghcr.io/${{ github.repository_owner }}/rust-build:1.19"
            "ghcr.io/${{ github.repository_owner }}/rust-build:latest"
            "${{ secrets.REGISTRY_ALIYUN_ADDR }}/public/rust-build:1.19"
            "${{ secrets.REGISTRY_ALIYUN_ADDR }}/public/rust-build:latest"

  build_agent_env_arm64:
    name: build agent env arm64
    runs-on: "cirun-aws-arm64-32c--${{ github.run_id }}"
    steps:

      - name: Checkout
        uses: actions/checkout@v3
      # - name: install docker
      #   run: |
      #     curl -fsSL https://get.docker.com | bash

      - name: docker version
        run: |
          docker version

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          install: true

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "ghcr.io" 
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GHCR_PUSH_TOKEN }}" 

      - name: Log in to ALIYUN Docker Registry
        uses: docker/login-action@v2
        with:
          registry: "${{ secrets.REGISTRY_ALIYUN_ADDR }}"
          username: "${{ secrets.REGISTRY_ALIYUN_USER }}" 
          password: "${{ secrets.REGISTRY_PASS }}"

      - name: Build and push deepflow agent env image
        uses: docker/build-push-action@v2
        with:
          context: agent
          push: true 
          file: agent/docker/DockerfileToFix-aarch64.build
          platforms: linux/arm64
          tags: |
            "ghcr.io/${{ github.repository_owner }}/rust-build:1.19-arm64"
            "ghcr.io/${{ github.repository_owner }}/rust-build:latest-arm64"
            "${{ secrets.REGISTRY_ALIYUN_ADDR }}/public/rust-build:1.19-arm64"
            "${{ secrets.REGISTRY_ALIYUN_ADDR }}/public/rust-build:latest-arm64"
