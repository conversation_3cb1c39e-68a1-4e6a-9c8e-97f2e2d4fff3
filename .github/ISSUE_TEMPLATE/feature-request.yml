#
# Copyright (c) 2022 Yunshan Networks
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: DeepFlow feature request
description: Suggest an idea for this project
title: "[FR] "
labels: [ "feature" ]
body:
  - type: markdown
    attributes:
      value: |
        <img src="https://github.com/deepflowio/deepflow/blob/main/docs/deepflow-logo.png?raw=true" alt="DeepFlow logo" height="90px" align="right" />

        Thank you for finding the time to propose new feature!

        We really appreciate the community efforts to improve DeepFlow.

  - type: checkboxes
    attributes:
      label: Search before asking
      description: >
        Please make sure to search in the [issues](https://github.com/deepflowio/deepflow/issues?q=is%3Aissue) first to see
        whether the same feature was requested already.
      options:
        - label: >
            I had searched in the [issues](https://github.com/deepflowio/deepflow/issues?q=is%3Aissue) and found no similar
            feature requirement.
          required: true

  - type: textarea
    attributes:
      label: Description
      description: A short description of your feature

  - type: textarea
    attributes:
      label: Use case
      description: What do you want to happen?
      placeholder: >
        Rather than telling us how you might implement this feature, try to take a
        step back and describe what you are trying to achieve.

  - type: textarea
    attributes:
      label: Related issues
      description: Is there currently another issue associated with this?

  - type: checkboxes
    attributes:
      label: Are you willing to submit a PR?
      description: >
        This is absolutely not required, but we are happy to guide you in the contribution process
        especially if you already have a good understanding of how to implement the feature.
        DeepFlow is a totally community-driven project and we love to bring new contributors in.
      options:
        - label: Yes I am willing to submit a PR!

  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: The Code of Conduct helps create a safe space for everyone. We require
        that everyone agrees to it.
      options:
        - label: |
            I agree to follow this project's [Code of Conduct](https://github.com/deepflowio/deepflow/blob/main/CODE_OF_CONDUCT.md)
          required: true

  - type: markdown
    attributes:
      value: "Thanks for completing our form!"
