sequenceDiagram
    participant MC as Master Controller
    participant SC as Slave Controller
    participant DB as MetaDB
    participant Vtap as Vtaps
    participant K8s as K8s API Server

    rect rgb(200, 230, 240)
        Note over MC,K8s: 1. 健康检查阶段
        loop 每隔健康检查间隔
            MC->>MC: 执行控制器健康检查
            MC->>DB: 查询所有控制器状态
            
            alt 控制器异常
                MC->>DB: 更新控制器状态为异常
                Note over MC: 记录异常持续时间
                
                alt 异常持续超过阈值
                    MC->>DB: 更新控制器状态为Exception
                    MC->>MC: 触发采集器重新分配
                end
            end
        end
    end

    rect rgb(255, 220, 220)
        Note over MC,Vtap: 2. 采集器重分配阶段
        MC->>DB: 获取异常控制器关联的采集器
        MC->>DB: 获取可用控制器列表
        
        loop 对每个需要重分配的采集器
            MC->>MC: 基于负载均衡算法选择新控制器
            MC->>DB: 更新采集器的控制器分配
            MC->>Vtap: 推送新的控制器配置
        end
    end

    rect rgb(220, 255, 220)
        Note over MC,K8s: 3. Master故障转移
        Note over MC: Master故障
        MC-xK8s: 租约续期失败
        K8s->>SC: 获得Leader租约
        SC->>SC: 启动Master功能
        
        par 并行执行Master功能初始化
            SC->>SC: 启动资源ID管理器
            SC->>SC: 启动标签记录器
            SC->>SC: 启动各类检查器
            SC->>SC: 启动License管理
            SC->>SC: 启动资源清理
        end
    end

    rect rgb(200, 255, 200)
        Note over SC,Vtap: 4. 数据同步与恢复
        SC->>DB: 同步控制器状态
        SC->>DB: 同步采集器分配
        SC->>Vtap: 重新建立采集器连接
        Note over SC: 恢复正常服务
    end