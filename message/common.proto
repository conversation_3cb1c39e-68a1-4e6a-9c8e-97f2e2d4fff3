syntax = "proto2";

package common;
option go_package = "github.com/deepflowio/deepflow/message/common";

// TODO: To be deleted in the future
enum TridentType {
    TT_UNKNOWN = 0;
    TT_PROCESS = 1;                        // Agent in KVM
    TT_VM = 2;                             // Agent in a dedicated VM on ESXi
    TT_PUBLIC_CLOUD = 3;                   // Agent in Cloud host (VM)
    // _ = 4;                              // --deprecated--
    TT_PHYSICAL_MACHINE = 5;               // Agent in Cloud host (BM), or legacy host
    TT_DEDICATED_PHYSICAL_MACHINE = 6;     // Agent in a dedicated host to receive mirror traffic
    TT_HOST_POD = 7;                       // Agent in K8s Node (Cloud BM, or legacy host)
    TT_VM_POD = 8;                         // Agent in K8s Node (Cloud VM)
    TT_TUNNEL_DECAPSULATION = 9;           // Agent in a dedicated host to decap tunnel traffic
    TT_HYPER_V_COMPUTE = 10;               // Agent in Hyper-V Compute Node
    TT_HYPER_V_NETWORK = 11;               // Agent in Hyper-V Network Node
    TT_K8S_SIDECAR = 12;                   // Agent in K8s POD
}

message KubernetesAPIInfo {
    optional string type = 1;
    optional string info = 2;
    optional bytes compressed_info = 3;
}
