syntax = "proto3";

package metric;

// rust 不支持以下import和option，暂时删除，go客户端使用时需要加回
// import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// option (gogoproto.unmarshaler_all) = true;
// option (gogoproto.marshaler_all) = true;
// option (gogoproto.sizer_all) = true;

option go_package = "pb";

message MiniField {
    bytes ip = 1;
    bytes ip1 = 2;
    uint32 global_thread_id = 3;
    uint32 is_ipv6 = 4;
    int32 l3_epc_id = 5;
    int32 l3_epc_id1 = 6;

    uint64 mac = 7;
    uint64 mac1 = 8;

    uint32 direction = 9;
    uint32 tap_side = 10;
    uint32 protocol = 11;
    uint32 acl_gid = 12;

    uint32 server_port = 13; // tunnel_ip_id also uses this field
    uint32 vtap_id = 14;
    uint64 tap_port = 15;
    uint32 tap_type = 16;
    uint32 l7_protocol = 17;

    // Deprecated in v6.4.9: uint32 tag_type = 18;
    // Deprecated in v6.4.9: uint32 tag_value = 19;

    uint32 gpid = 20;
    uint32 gpid1 = 21;

    uint32 signal_source = 22;
    string app_service = 23;
    string app_instance = 24;
    string endpoint = 25;
    // Deprecated in v6.4.1: uint32 netns_id = 26;
    uint32 pod_id = 27;
    uint32 biz_type = 28;
}

message MiniTag {
    MiniField field = 1;
    uint64    code = 2;
}

message Meter {
    uint32     meter_id = 1;
    FlowMeter  flow = 2;
    UsageMeter usage = 3;
    AppMeter   app = 4;
}

message Document {
    uint32  timestamp = 1;
    MiniTag tag = 2;
    Meter   meter = 3;
    uint32  flags = 4;
}

// flow meter
message FlowMeter {
    Traffic     traffic = 1;
    Latency     latency= 2;
    Performance performance = 3;
    Anomaly     anomaly = 4;
    FlowLoad    flow_load = 5;
}

message Traffic {
    uint64 packet_tx = 1;
    uint64 packet_rx = 2;
    uint64 byte_tx = 3;
    uint64 byte_rx = 4;
    uint64 l3_byte_tx = 5;
    uint64 l3_byte_rx = 6;
    uint64 l4_byte_tx = 7;
    uint64 l4_byte_rx = 8;
    uint64 new_flow = 9;
    uint64 closed_flow = 10;
    uint32 l7_request = 11;
    uint32 l7_response = 12;
    uint32 syn = 13;
    uint32 synack = 14;
    uint32 direction_score = 15;
}

// current max id = 21
message Latency {
    uint32 rtt_max = 1;
    uint32 rtt_client_max = 2;
    uint32 rtt_server_max =3;
    uint32 srt_max = 4;
    uint32 art_max = 5;
    uint32 rrt_max = 6;
    uint32 cit_max = 19;

    uint64 rtt_sum = 7;
    uint64 rtt_client_sum = 8;
    uint64 rtt_server_sum = 9;
    uint64 srt_sum = 10;
    uint64 art_sum = 11;
    uint64 rrt_sum = 12;
    uint64 cit_sum = 20;

    uint32 rtt_count = 13;
    uint32 rtt_client_count = 14;
    uint32 rtt_server_count = 15;
    uint32 srt_count = 16;
    uint32 art_count = 17;
    uint32 rrt_count = 18;
    uint32 cit_count = 21;
}

message Performance {
    uint64 retrans_tx = 1;
    uint64 retrans_rx = 2;
    uint64 zero_win_tx = 3;
    uint64 zero_win_rx = 4;
    uint32 retrans_syn = 5;
    uint32 retrans_synack = 6;
}

message Anomaly {
    uint64 client_rst_flow = 1;
    uint64 server_rst_flow = 2;
    uint64 server_syn_miss = 3; // Modified on v6.5.2: uint64 client_syn_repeat = 3;
    uint64 client_ack_miss = 4; // Modified on v6.5.2: uint64 server_synack_repeat = 4;
    uint64 client_half_close_flow = 5;
    uint64 server_half_close_flow = 6;

    uint64 client_source_port_reuse = 7;
    uint64 client_establish_reset = 8;
    uint64 server_reset = 9;
    uint64 server_queue_lack = 10;
    uint64 server_establish_reset = 11;
    uint64 tcp_timeout = 12;

    uint32 l7_client_error = 13;
    uint32 l7_server_error = 14;
    uint32 l7_timeout = 15;
}

message FlowLoad {
    uint64 load = 1;
}

// usage meter
message UsageMeter {
    uint64 packet_tx = 1;
    uint64 packet_rx = 2;
    uint64 byte_tx = 3;
    uint64 byte_rx = 4;
    uint64 l3_byte_tx = 5;
    uint64 l3_byte_rx = 6;
    uint64 l4_byte_tx = 7;
    uint64 l4_byte_rx = 8;
}

// app meter
message AppMeter {
    AppTraffic traffic = 1;
    AppLatency latency = 2;
    AppAnomaly anomaly = 3;
}

message AppTraffic {
    uint32 request = 1;
    uint32 response= 2;
    uint32 direction_score = 3;
}

message AppLatency {
    uint32 rrt_max = 1;
    uint64 rrt_sum = 2;
    uint32 rrt_count = 3;
}

message AppAnomaly {
    uint32 client_error = 1;
    uint32 server_error = 2;
    uint32 timeout = 3;
}

enum ProfileEventType {
    External = 0;
    EbpfOnCpu = 1;
    EbpfOffCpu = 2;
    EbpfMemAlloc = 3;
    EbpfMemInUse = 4;
}

message Profile {
    bytes ip = 1;
    string name = 2;
    string units = 3;
    string aggregation_type = 4;
    uint32 sample_rate = 5;
    uint32 from = 6;
    uint32 until = 7;
    string spy_name = 8;
    string format = 9;
    bytes content_type = 10;
    bytes data = 11;
    bool data_compressed = 12; // Whether to compress `data`

    uint64 timestamp = 20;
    ProfileEventType event_type = 21;
    uint64 stime = 22;
    uint32 pid = 23;
    uint32 tid = 24;
    string thread_name = 25;
    string process_name = 26;
    uint32 u_stack_id = 27;
    uint32 k_stack_id = 28;
    uint32 cpu = 29;
    uint32 count = 30;
    // Deprecated in v6.4.1: uint64 netns_id = 31;
    // Deprecated in v6.4.1: string container_id = 32;
    uint32 pod_id = 33;
    uint64 wide_count = 34;
}

enum IoOperation {
    Write = 0;
    Read = 1;
}

enum FileType {
    Unknown = 0;
    Regular = 1; // A file stored on the local disk.
    Virtual = 2; // A file virtually created by the kernel, such as those in `procfs`, `sysfs`, etc.
    Network = 3; // A file stored on a remote `NAS` or `NFS` system.
}

message IoEventData {
    uint32 bytes_count = 1;
    IoOperation operation = 2;
    uint64 latency = 3;
    bytes filename = 4; // a bytes array ending with \0, length: is the actual length of the string
    uint64 off_bytes = 5; // the number of bytes of offset within the file content
    bytes mount_source = 6;
    bytes mount_point= 7;
    bytes file_dir = 8;
    FileType file_type = 9;
}

enum EventType {
    OtherEvent = 0;
    IoEvent = 1;
}

message ProcEvent {
    uint32 pid = 1;
    uint32 thread_id = 2;
    uint32 coroutine_id = 3;
    bytes process_kname = 4; // a bytes array ending with \0, length: 16
    uint64 start_time = 5;
    uint64 end_time = 6;
    EventType event_type = 7;
    IoEventData io_event_data = 8;
    // Deprecated in v6.4.1: uint32 netns_id = 9;
    uint32 pod_id = 10;
}

message PrometheusMetric {
    bytes metrics = 1;
    repeated string extra_label_names = 2;
    repeated string extra_label_values = 3;
}
