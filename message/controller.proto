syntax = "proto2";

package controller;
option go_package = "controller";

import "common.proto";

service Controller {
    rpc GenesisSharingK8S (GenesisSharingK8SRequest) returns (GenesisSharingK8SResponse) {}
    rpc GenesisSharingSync (GenesisSharingSyncRequest) returns (GenesisSharingSyncResponse) {}
    rpc GetEncryptKey (EncryptKeyRequest) returns (EncryptKeyResponse) {}
    rpc GetResourceID (GetResourceIDRequest) returns (GetResourceIDResponse) {}
    rpc ReleaseResourceID (ReleaseResourceIDRequest) returns (ReleaseResourceIDResponse) {}
    rpc SyncPrometheus (SyncPrometheusRequest) returns (SyncPrometheusResponse) {}
}

message GenesisSharingK8SRequest {
    optional uint32 org_id = 1;
    optional string cluster_id = 2;
}

message GenesisSharingK8SResponse {
    optional string epoch = 1;
    optional string error_msg = 2;
    repeated common.KubernetesAPIInfo entries = 3;
}

message GenesisSyncIP {
    optional uint32 masklen = 1;
    optional string ip = 2;
    optional string lcuuid = 3;
    optional string vinterface_lcuuid = 4;
    optional string node_ip = 5;
    optional string last_seen = 6;
    optional uint32 vtap_id = 7;
}

message GenesisSyncVIP {
    optional string ip = 1;
    optional string lcuuid = 2;
    optional string node_ip = 3;
    optional uint32 vtap_id = 4;
}

message GenesisSyncHost {
    optional string lcuuid = 1;
    optional string hostname = 2;
    optional string ip = 3;
    optional string node_ip = 4;
    optional uint32 vtap_id = 5;
}

message GenesisSyncLldp {
    optional string lcuuid = 1;
    optional string host_ip = 2;
    optional string host_interface = 3;
    optional string system_name = 4;
    optional string management_address = 5;
    optional string vinterface_lcuuid = 6;
    optional string vinterface_description = 7;
    optional string node_ip = 8;
    optional string last_seen = 9;
    optional uint32 vtap_id = 10;
}

message GenesisSyncNetwork {
    optional uint32 segmentation_id = 1;
    optional uint32 net_type = 2;
    optional bool external = 3;
    optional string name = 4;
    optional string lcuuid = 5;
    optional string vpc_lcuuid = 6;
    optional string node_ip = 7;
    optional uint32 vtap_id = 8;
}

message GenesisSyncPort {
    optional uint32 type = 1;
    optional uint32 device_type = 2;
    optional string lcuuid = 3;
    optional string mac = 4;
    optional string device_lcuuid = 5;
    optional string network_lcuuid = 6;
    optional string vpc_lcuuid = 7;
    optional string node_ip = 8;
    optional uint32 vtap_id = 9;
}

message GenesisSyncVm {
    optional uint32 state = 1;
    optional string lcuuid = 2;
    optional string name = 3;
    optional string label = 4;
    optional string vpc_lcuuid = 5;
    optional string launch_server = 6;
    optional string node_ip = 7;
    optional string created_at = 8;
    optional uint32 vtap_id = 9;
}

message GenesisSyncVpc {
    optional string lcuuid = 1;
    optional string name = 2;
    optional string node_ip = 3;
    optional uint32 vtap_id = 4;
}

message GenesisSyncVinterface {
    optional uint32 vtap_id = 1;
    optional string lcuuid = 2;
    optional string name = 3;
    optional string ips = 4;
    optional string mac = 5;
    optional string tap_name = 6;
    optional string tap_mac = 7;
    optional string device_lcuuid = 8;
    optional string device_name = 9;
    optional string device_type = 10;
    optional string host_ip = 11;
    optional string kubernetes_cluster_id = 12;
    optional string node_ip = 13;
    optional string last_seen = 14;
    optional uint32 netns_id = 15;
    optional string if_type = 16;
    optional uint32 team_id = 17;
}

message GenesisSyncProcess {
    optional uint32 vtap_id = 1;
    optional uint64 pid = 2;
    optional string lcuuid = 3;
    optional string name = 4;
    optional string process_name = 5;
    optional string cmd_line = 6;
    optional string user = 7;
    optional string os_app_tags = 8;
    optional string node_ip = 9;
    optional string start_time = 10;
    optional uint32 netns_id = 11;
    optional string container_id = 12;
}

message GenesisSyncData{
    repeated GenesisSyncIP ip = 1;
    repeated GenesisSyncHost host = 2;
    repeated GenesisSyncLldp lldp = 3;
    repeated GenesisSyncNetwork network = 4;
    repeated GenesisSyncPort port = 5;
    repeated GenesisSyncVm vm = 6;
    repeated GenesisSyncVpc vpc = 7;
    repeated GenesisSyncVinterface vinterface = 8;
    repeated GenesisSyncProcess process = 9;
    repeated GenesisSyncVIP vip = 10;
}

message GenesisSharingSyncRequest {
    optional uint32 org_id = 1;
}

message GenesisSharingSyncResponse {
    optional GenesisSyncData data = 1;
}

message EncryptKeyRequest {
    optional string key = 1;
}

message EncryptKeyResponse {
    optional string error_msg = 1;
    optional string encrypt_key = 2;
}

message GetResourceIDRequest {
    required string type = 1;
    required uint32 count = 2;
    optional uint32 org_id =3;
}

message GetResourceIDResponse {
    repeated uint32 ids = 1; 
}

message ReleaseResourceIDRequest {
    required string type = 1;
    repeated uint32 ids = 2;
    optional uint32 org_id =3;
}

message ReleaseResourceIDResponse {
}

message PrometheusMetricName {
    required uint32 id = 1;
    required string name = 2;
}

message PrometheusLabelName {
    required uint32 id = 1;
    required string name = 2;
}

message PrometheusLabelValue {
    required uint32 id = 1;
    required string value = 2;
}

message PrometheusMetricLabelNameRequest {
    required string metric_name = 1;
    repeated string label_names = 2;
}

message PrometheusMetricLabelName {
    optional uint32 metric_name_id = 1;
    repeated uint32 label_name_ids = 2;
}

message PrometheusMetricAPPLabelLayout {
    required string metric_name = 1;
    required string app_label_name = 2;
    required uint32 app_label_column_index = 3;
}

message PrometheusMetricAPPLabelLayoutRequest {
    required string metric_name = 1;
    required string app_label_name = 2;
}

message PrometheusLabelRequest {
    required string name = 1;
    required string value = 2;
}

message PrometheusMetricTargetRequest {
    optional string metric_name = 1;
    optional uint32 target_id = 2;
    optional string instance = 3;
    optional string job = 4;
    optional uint32 pod_cluster_id = 5;
    optional uint32 epc_id = 6;
}

message PrometheusMetricTarget {
    required string metric_name = 1;
    required uint32 target_id = 2;
}

message PrometheusLabel {
    required uint32 id = 1;
    required string name = 2;
    required string value = 3;
}

message PrometheusTargetRequest {
    optional string instance = 1;
    optional string job = 2;
    optional uint32 pod_cluster_id = 3;
    optional uint32 epc_id = 4;
}

message PrometheusTarget {
    optional uint32 id = 1;
    optional string instance = 2;
    optional string job = 3;
    optional uint32 pod_cluster_id = 4;
    optional uint32 epc_id = 5;
}

message SyncPrometheusRequest {
    repeated string metric_names = 1;
    repeated string label_names = 2;
    repeated string label_values = 3;
    repeated PrometheusMetricAPPLabelLayoutRequest metric_app_label_layouts = 4;
    repeated PrometheusLabelRequest labels = 5;
    repeated PrometheusMetricLabelNameRequest metric_label_names = 6;
    repeated PrometheusMetricTargetRequest metric_targets = 7;
    repeated PrometheusTargetRequest targets = 8;

    optional uint32 org_id = 11;
}

message SyncPrometheusResponse {
    optional string error_msg = 1;
    repeated PrometheusMetricName metric_names = 2;
    repeated PrometheusLabelName label_names = 3;
    repeated PrometheusLabelValue label_values = 4;
    repeated PrometheusMetricAPPLabelLayout metric_app_label_layouts = 5;
    repeated PrometheusLabel labels = 6;
    repeated PrometheusMetricTarget metric_targets = 7;
    repeated PrometheusTarget targets = 8;
    repeated PrometheusMetricLabelName metric_label_names = 9;

    optional uint32 org_id = 11;
}

service PrometheusDebug {
    rpc DebugPrometheusCache (PrometheusCacheRequest) returns (PrometheusCacheResponse) {}
}

enum PrometheusCacheType {
    ALL = 0;
    METRIC_NAME = 1;
    LABEL_NAME = 2;
    LABEL_VALUE = 3;
    METRIC_AND_APP_LABEL_LAYOUT = 4;
    TARGET = 5;
    LABEL = 6;
    METRIC_LABEL = 7;
    METRIC_TARGET = 8;
}

message PrometheusCacheRequest {
    optional PrometheusCacheType type = 1;
}

message PrometheusCacheResponse {
    optional bytes content = 1;     // json data
}
