syntax = "proto2";

package alarm_event;
option go_package = "alarm_event";

message AlarmEvent {
    optional string lcuuid = 1;
    optional string user = 2;
    optional uint32 timestamp = 3;

    optional uint32 policy_id = 4;
    optional string policy_name = 5; 
    optional uint32 policy_level = 6; 
    optional uint32 policy_app_type = 7;
    optional uint32 policy_sub_type = 8;
    optional uint32 policy_contrast_type = 9;
    optional string policy_data_level = 10; 
    optional string policy_target_uid = 11;
    optional string policy_target_name = 12; 
    optional string policy_go_to = 13;
    optional string policy_target_field = 14;
    optional string policy_endpoints = 15;
    optional string trigger_condition = 16; 
    optional double trigger_value = 17;
    optional string value_unit = 18; 
    optional uint32 event_level = 19;
    optional string alarm_target = 20;
    optional uint32 user_id = 21;
    optional string policy_query_url = 22;
    optional string policy_query_conditions = 23;
    optional string policy_threshold_critical = 24;
    optional string policy_threshold_error = 25;
    optional string policy_threshold_warning = 26;

    optional uint32 org_id = 27;
    optional uint32 team_id = 28;
}
