syntax = "proto2";

package trident;
option go_package = "trident";

import "common.proto";

service Synchronizer {
    rpc Sync(SyncRequest) returns (SyncResponse) {}
    rpc Push(SyncRequest) returns (stream SyncResponse) {}
    rpc AnalyzerSync(SyncRequest) returns (SyncResponse) {}
    rpc Upgrade(UpgradeRequest) returns (stream UpgradeResponse) {}
    rpc GetPrometheusLabelIDs(PrometheusLabelRequest) returns (PrometheusLabelResponse) {}
    rpc GetPrometheusTargets(PrometheusTargetRequest) returns (PrometheusTargetResponse) {}
    rpc GetUniversalTagNameMaps(UniversalTagNameMapsRequest) returns (UniversalTagNameMapsResponse) {}
    // because gRPC cannot be initiated by server, the req/resp of this rpc is reversed
    rpc GetOrgIDs(OrgIDsRequest) returns (OrgIDsResponse) {}
}

enum State {
    ENVIRONMENT_CHECK = 0;  // 检查运行环境
    DISABLED = 1;           // 禁用
    RUNNING = 2;            // 正常运行
    REBOOTING = 3;          // 因配置变更等缘故触发重启
    STRESSED = 4;           // 负载太大产生丢包
    RESTRICTED = 5;         // 占用过多系统资源
}

enum Exception {
    NORMAL = 0;
    DISK_NOT_ENOUGH = 1;
    MEM_NOT_ENOUGH = 2;
    COREFILE_TOO_MANY = 4;
    NPB_FUSE = 8;
    NPB_BPS_THRESHOLD_EXCEEDED = 16;
    NPB_NO_GW_ARP = 32;
    RX_PPS_THRESHOLD_EXCEEDED = 64;
    ANALYZER_NO_GW_ARP = 128;
    INVALID_CONFIGURATION = 256;
    THREAD_THRESHOLD_EXCEEDED = 512;
    PROCESS_THRESHOLD_EXCEEDED = 1024;
    // _  = 2048; // deprecate
    TOO_MANY_POLICIES = 4096;
    FREE_MEM_EXCEEDED = 8192;
    LOG_FILE_EXCEEDED = 16384;
    CONTROLLER_SOCKET_ERROR = 32768;
    ANALYZER_SOCKET_ERROR = 65536;
    NPB_SOCKET_ERROR = 131072;
    INTEGRATION_SOCKET_ERROR = 262144;
    CGROUPS_CONFIG_ERROR = 524288;
    SYSTEM_LOAD_CIRCUIT_BREAKER = 1048576;
    // 2^31及以下由采集器使用，采集器最大可用异常是2^31，顺序从前往后
    // 2^32及以上由控制器使用，顺序从后往前
}

message CommunicationVtap {
    optional uint32 vtap_id = 1;           // 限制在64000
    optional uint32 last_active_time = 2;  // 单位：秒
}

message TsdbReportInfo {
    optional string pcap_data_mount_path = 4;
}

enum KubernetesWatchPolicy {
    KWP_NORMAL = 0;
    KWP_WATCH_ONLY = 1;
    KWP_WATCH_DISABLED = 2;
}

message SyncRequest {
    optional uint32 boot_time = 1;
    optional bool config_accepted = 2 [default = true];
    optional State state = 4;
    optional string revision = 5;                 // trident用于self-update
    optional uint64 exception = 6 [default = 0];  // trident exception status
    optional string process_name = 7;
    optional TapMode tap_mode = 8 [default = LOCAL];

    optional uint64 version_platform_data = 9 [default = 0]; /* only platform data */
    optional uint64 version_acls = 10 [default = 0];
    optional uint64 version_groups = 11 [default = 0];
    optional string current_k8s_image = 12;

    optional string ctrl_ip = 21;
    optional string host = 22;      // 表示hostname，操作系统的原始主机名，注册和信息同步使用
    repeated string host_ips = 23;  // 仅作为注册使用
    optional string ctrl_mac = 25;
    optional string vtap_group_id_request = 26;  // 支持采集器自动加入组
    optional bool kubernetes_force_watch = 27 [default = false];
    optional AgentIdentifier agent_unique_identifier = 28 [default = IP_AND_MAC];
    optional string team_id = 29;   // agent team identity

    repeated CommunicationVtap communication_vtaps = 31;  // 仅对数据节点有意义

    // 运行环境基本信息
    optional uint32 cpu_num = 32;
    optional uint64 memory_size = 33;  // 单位：Bytes
    optional string arch = 34;
    optional string os = 35;
    optional string kernel_version = 36;

    optional KubernetesWatchPolicy kubernetes_watch_policy = 41;

    optional TsdbReportInfo tsdb_report_info = 43;  // 仅对数据节点有意义

    optional string kubernetes_cluster_id = 45;    // 仅对容器类型的采集器有意义
    optional string kubernetes_cluster_name = 46;  // 仅对容器类型的采集器有意义

    optional uint32 org_id = 50;  // only used by Ingester
}

enum Status {
    SUCCESS = 0;
    FAILED = 1;
    HEARTBEAT = 2;
    CLUSTER_ID_NOT_FOUND = 10;
}

enum TapMode {
    LOCAL = 0;     // 部署在宿主机之上，抓取本地虚拟接口流量
    MIRROR = 1;    // 部署在虚拟机之上，抓取镜像而来的流量
    ANALYZER = 2;  // 部署在专属服务器采集器之上
    DECAP = 3;     // 隧道解封装采集器
}

enum AgentIdentifier {
    IP_AND_MAC = 1;
    IP = 2;
}

enum IfMacSource {
    IF_MAC = 0;
    IF_NAME = 1;
    IF_LIBVIRT_XML = 2;  // 从libvirt的xml文件中获取
}

enum SocketType {
    RAW_UDP = 0;
    TCP = 1;
    UDP = 2;
    FILE = 3;
}

enum PacketType {
    PACKET = 1;
    SFLOW = 2;
    NETFLOW_V5 = 3;
    NETSTREAM_V5 = 4;
    NETFLOW_V9 = 5;
    NETSTREAM_V9 = 6;
}

enum CaptureSocketType {
    AUTO = 0;
    AF_PACKET_V1 = 1;
    AF_PACKET_V2 = 2;
    AF_PACKET_V3 = 3;
}

message TapType {
    optional uint32 tap_type = 1;
    optional PacketType packet_type = 2;
    optional uint32 vlan = 3;
    optional string source_ip = 4;
    optional uint32 tap_port = 5;
}

enum VlanMode {
    NONE = 0;
    VLAN = 1;
    QINQ = 2;
}

enum DecapType {
    DECAP_TYPE_NONE = 0;
    DECAP_TYPE_VXLAN = 1;
    DECAP_TYPE_IPIP = 2;
    DECAP_TYPE_TENCENT = 3;
    DECAP_TYPE_GENEVE = 4;
    DECAP_TYPE_VXLAN_NSH = 5;
}

enum SystemLoadMetric {
    Load1 = 0;
    Load5 = 1;
    Load15 = 2;
}

enum SysMemoryMetric {
    Free = 0;
    Available = 1;
}

message Config {
    optional bool enabled = 1 [default = true];
    optional uint32 max_cpus = 2 [default = 1];
    optional uint32 max_memory = 3 [default = 768];  // in MiB
    optional uint32 sync_interval = 4 [default = 60];
    optional uint32 stats_interval = 5 [default = 10];
    optional uint64 global_pps_threshold = 6 [default = 200000];

    // capture network namespace regex besides root ns
    optional string extra_netns_regex = 7 [default = ""];
    // qemu: tap.*
    // localhost: lo
    // common nic: eth|en[ospx].*
    // flannel: veth.*
    // calico: cali.*
    // cilium: lxc.*
    // kube-ovn: [0-9a-f]+_h$
    optional string tap_interface_regex = 8;
    optional string host = 9;  // override statsd host tag
    optional bool rsyslog_enabled = 10 [default = true];
    optional uint32 output_vlan = 11 [default = 0];
    optional uint32 mtu = 12 [default = 1500];
    optional uint64 npb_bps_threshold = 13 [default = 1000000000];
    optional bool collector_enabled = 14 [default = false];
    optional uint32 max_millicpus = 15 [default = 1000];  // in MilliCore, 1000 = 1 Core
    optional bool platform_enabled = 16 [default = false];
    optional bool kubernetes_api_enabled = 17 [default = false];
    optional uint64 server_tx_bandwidth_threshold = 18 [default = 0];  // bps
    optional uint64 bandwidth_probe_interval = 19 [default = 10];      // second

    optional CaptureSocketType capture_socket_type = 21 [default = AUTO];
    optional VlanMode npb_vlan_mode = 22 [default = NONE];
    repeated uint32 l4_log_tap_types = 23;
    optional bool npb_dedup_enabled = 24 [default = true];
    optional IfMacSource if_mac_source = 25 [default = IF_MAC];
    optional bool vtap_flow_1s_enabled = 27 [default = true];
    optional bool debug_enabled = 28 [default = true];

    optional string analyzer_ip = 31;
    optional uint32 max_escape_seconds = 32 [default = 3600];

    optional string proxy_controller_ip = 34;           // 控制器代理IP
    optional uint32 region_id = 35 [default = 0];       // 采集器所在区域ID或数据节点所在区域ID
    optional uint32 pod_cluster_id = 36 [default = 0];  // 采集器所在容器集群ID
    optional uint32 epc_id = 37 [default = 0];          // 采集器所在epc_id, 仅对Workload-V/P, 容器-V/P类型有意义
    optional uint32 analyzer_port = 38 [default = 30033];
    optional uint32 proxy_controller_port = 39 [default = 30035];

    optional uint32 vtap_id = 40;  // 限制在64000
    optional common.TridentType trident_type = 41 [default = TT_UNKNOWN];
    optional uint32 platform_sync_interval = 42 [default = 10];
    optional uint32 team_id = 43 [default = 0];
    optional uint32 organize_id = 44 [default = 0];

    optional SocketType collector_socket_type = 45 [default = TCP];
    // _ = 46; // deprecated
    optional SocketType npb_socket_type = 47 [default = RAW_UDP];

    optional uint32 pcap_data_retention = 51 [default = 7];  // deprecated, uint: day
    optional uint32 capture_packet_size = 52 [default = 65535];
    optional bool inactive_server_port_enabled = 53 [default = true];
    optional string capture_bpf = 54 [default = ""];
    optional bool inactive_ip_enabled = 55 [default = true];

    optional string libvirt_xml_path = 60 [default = "/etc/libvirt/qemu"];

    // 新增资源限制
    optional uint32 log_threshold = 101 [default = 300];
    optional string log_level = 102 [default = "INFO"];
    optional uint32 thread_threshold = 103 [default = 500];  // 限制采集器运行环境中trident进程内线程数量
    optional uint32 process_threshold = 104 [default = 10];  // 限制采集器运行环境中trident进程启动的其他子进程数量

    // 新增基础配置参数
    optional uint32 log_retention = 201 [default = 30];  // uint: day
    optional bool ntp_enabled = 203 [default = false];
    repeated DecapType decap_type = 204;

    // 新增全景图配置参数
    optional string http_log_proxy_client = 301 [default = 'X-Forwarded-For'];
    optional string http_log_trace_id = 302 [default = 'traceparent, sw8'];
    optional uint32 l7_log_packet_size = 303 [default = 1024];
    optional uint64 l4_log_collect_nps_threshold = 304 [default = 10000];
    optional uint64 l7_log_collect_nps_threshold = 305 [default = 10000];
    optional string http_log_span_id = 306 [default = 'traceparent, sw8'];
    optional string http_log_x_request_id = 307 [default = 'X-Request-ID'];

    // 新增全景图功能开关
    repeated uint32 l4_log_ignore_tap_sides = 401;
    repeated uint32 l7_log_ignore_tap_sides = 402;
    repeated uint32 l7_log_store_tap_types = 403;
    optional bool l4_performance_enabled = 404 [default = true];
    optional bool l7_metrics_enabled = 405 [default = true];
    optional bool external_agent_http_proxy_enabled = 406 [default = true];  // 外部Agent数据HTTP代理开关
    optional uint32 external_agent_http_proxy_port = 407 [default = 38086];  // 外部Agent数据HTTP代理端口
    // _ = 408; // deprecated. 408 was once occupied by prometheus_http_api_address.
    // repeated string prometheus_http_api_addresses = 409; // 6.5 delete

    optional uint32 packet_sequence_flag = 410 [default = 0];

    optional PluginConfig plugins = 420;

    optional uint32 sys_free_memory_limit = 501 [default = 0];
    optional uint32 log_file_size = 502 [default = 1000];
    optional TapMode tap_mode = 503 [default = LOCAL];
    optional float system_load_circuit_breaker_threshold = 504 [default = 1.0];
    optional SystemLoadMetric system_load_circuit_breaker_metric = 505 [default = Load15];
    optional float system_load_circuit_breaker_recover = 506 [default = 0.9];
    optional string secret_key = 507; // secret key for dataplane
    optional SysMemoryMetric sys_free_memory_metric = 508 [default = Free];

    optional string local_config = 510;  // 全量的配置文件内容
}

message Segment {  // e.g. single LAN area
    optional uint32 id = 1;
    repeated string mac = 2;
    repeated uint32 interface_id = 3;  // mac对应的Interface id
    repeated string vmac = 4;          // if interface vmac is not null, vmac = interface vmac; else vmac = interface mac
}

message IpResource {
    optional string ip = 1;
    optional uint32 masklen = 2 [default = 32];
    optional uint32 subnet_id = 3 [default = 0];
}

enum DeviceType {
    DEVICE_TYPE_UNKNOWN = 0;
    DEVICE_TYPE_VM = 1;
    DEVICE_TYPE_VGW = 2;
    DEVICE_TYPE_THIRD_PARTY_DEVICE = 3;
    DEVICE_TYPE_VMWAF = 4;
    DEVICE_TYPE_NSP_VGATEWAY = 5;
    DEVICE_TYPE_HOST_DEVICE = 6;
    DEVICE_TYPE_NETWORK_DEVICE = 7;
    DEVICE_TYPE_DHCP_PORT = 9;
    DEVICE_TYPE_POD = 10;
    DEVICE_TYPE_POD_SERVICE = 11;
    DEVICE_TYPE_REDIS_INSTANCE = 12;
    DEVICE_TYPE_RDS_INSTANCE = 13;
    DEVICE_TYPE_POD_NODE = 14;
    DEVICE_TYPE_LOAD_BALANCE = 15;
    DEVICE_TYPE_NAT_GATEWAY = 16;

    DEVICE_TYPE_PROCESS = 120;
}

enum AutoServiceType {
    AUTO_SERVICE_TYPE_INTERNET_IP = 0;
    AUTO_SERVICE_TYPE_CHOST = 1;
    AUTO_SERVICE_TYPE_VGATEWAY = 5;
    AUTO_SERVICE_TYPE_HOST = 6;
    AUTO_SERVICE_TYPE_DHCP_PORT = 9;
    AUTO_SERVICE_TYPE_POD = 10;
    AUTO_SERVICE_TYPE_POD_SERVICE = 11;
    AUTO_SERVICE_TYPE_REDIS_INSTANCE = 12;
    AUTO_SERVICE_TYPE_RDS_INSTANCE = 13;
    AUTO_SERVICE_TYPE_POD_NODE = 14;
    AUTO_SERVICE_TYPE_LOAD_BALANCE = 15;
    AUTO_SERVICE_TYPE_NAT_GATEWAY = 16;

    AUTO_SERVICE_TYPE_POD_GROUP = 101;
    AUTO_SERVICE_TYPE_SERVICE = 102;
    AUTO_SERVICE_TYPE_POD_CLUSTER = 103;
    AUTO_SERVICE_TYPE_CUSTOM_SERVICE = 104;
    AUTO_SERVICE_TYPE_PROCESS = 120;
    AUTO_SERVICE_TYPE_POD_GROUP_DEPLOYMENT = 130;
    AUTO_SERVICE_TYPE_POD_GROUP_STATEFULSET = 131;
    AUTO_SERVICE_TYPE_POD_GROUP_RC = 132;
    AUTO_SERVICE_TYPE_POD_GROUP_DAEMON_SET = 133;
    AUTO_SERVICE_TYPE_POD_GROUP_REPLICASET_CONTROLLER = 134;
    AUTO_SERVICE_TYPE_POD_GROUP_CLONESET = 135;

    AUTO_SERVICE_TYPE_IP = 255;
}

message Interface {
    optional uint32 id = 1;
    optional uint32 device_type = 2;
    optional uint32 device_id = 3;
    optional uint32 if_type = 4;
    optional uint32 epc_id = 6;
    optional string launch_server = 7;
    repeated IpResource ip_resources = 8;
    optional uint32 launch_server_id = 9;
    optional uint32 region_id = 10;
    optional uint64 mac = 11;  // 0x0123456789ab = 01:23:45:67:89:ab, 为0时if_type为WAN的数据
    optional uint32 pod_node_id = 21;
    optional uint32 az_id = 22;
    optional uint32 pod_group_id = 23;
    optional uint32 pod_ns_id = 24;
    optional uint32 pod_id = 25;
    optional uint32 pod_cluster_id = 26;
    optional uint32 netns_id = 27 [default = 0];
    optional uint32 vtap_id = 28;  // 限制在64000
    optional uint32 pod_group_type = 29;

    optional bool is_vip_interface = 100 [default = false];  // 目前仅微软MUX设配为true
}

enum GroupType {
    NAMED = 0;
    ANONYMOUS = 1;
}

// 字段含义查看README
message Group {
    optional uint32 id = 1;
    optional uint32 epc_id = 2 [default = 0];
    optional GroupType type = 3;
    repeated string ips = 5;
    repeated string ip_ranges = 6;
    optional uint32 business_id = 7;
}

enum ServiceType {
    POD_SERVICE_NODE = 1;       // (pod_cluster_id, protocol, server_port)
    POD_SERVICE_POD_GROUP = 2;  // (pod_group_id, protocol, server_port)
    POD_SERVICE_IP = 3;         // (epc_id, ip, protocol, server_port)
    LB_SERVICE = 4;             // (epc_id, ip, protocol, server_port)
    // NAT_SERVICE = 5;
    // RG_SERVICE = 6;
    CUSTOM_SERVICE = 7;         // (epc_id, ip) or (epc_id, ip, server_port)
}

enum ServiceProtocol {
    ANY = 0;
    TCP_SERVICE = 1;
    UDP_SERVICE = 2;
}

message ServiceInfo {
    optional ServiceType type = 1;
    optional uint32 id = 2;
    optional uint32 pod_cluster_id = 3 [default = 0];  // Availabel when type == POD_SERVICE_NODE
    optional uint32 pod_group_id = 4 [default = 0];    // Availabel when type == POD_SERVICE_POD_GROUP
    optional uint32 epc_id = 5 [default = 0];          // Availabel when type in [POD_SERVICE_IP, LB_SERVICE, CUSTOM_SERVICE]
    // when type == CUSTOM_SERVICE, the length of `ips` can only be 1
    repeated string ips = 6;                           // Availabel when type in [POD_SERVICE_IP, LB_SERVICE, CUSTOM_SERVICE], list of IP addresses
    // 7: reserve for cidrs
    // 8: reserve for ip-ranges
    optional ServiceProtocol protocol = 9 [default = ANY];
    repeated uint32 server_ports = 10;                // Availabel when type in [POD_SERVICE_IP, LB_SERVICE, CUSTOM_SERVICE], list of IP addresses
}

message Groups {
    repeated Group groups = 1;
    repeated ServiceInfo svcs = 3;  // reply to ingester only
}

message PeerConnection {
    optional uint32 id = 1;
    optional uint32 local_epc_id = 2;
    optional uint32 remote_epc_id = 3;
}

enum CidrType {
    WAN = 1;
    LAN = 2;
}

message Cidr {
    optional string prefix = 1;
    optional CidrType type = 2;
    optional int32 epc_id = 3;
    optional uint32 subnet_id = 4;
    optional uint32 region_id = 5;
    optional uint32 az_id = 6;
    optional uint32 tunnel_id = 7;

    optional bool is_vip = 20 [default = false];
}

message GProcessInfo {
    optional uint32 gprocess_id = 1;
    optional uint32 vtap_id = 3;  // 限制在64000
    optional uint32 pod_id = 4;
    optional uint32 pid = 5;
}

message Container {
    optional uint32 pod_id = 1;
    optional string container_id = 2;
}

message PlatformData {
    repeated Interface interfaces = 1;
    repeated PeerConnection peer_connections = 3;
    repeated Cidr cidrs = 4;
    repeated GProcessInfo gprocess_infos = 5;
}

enum Action {
    PACKET_CAPTURING = 1;  // 包存储（pcap）
}

enum TapSide {
    SRC = 1;
    DST = 2;
    BOTH = 3;
}

enum TunnelType {
    VXLAN = 0;
    GRE_ERSPAN = 1;
    PCAP = 2;
    NPB_DROP = 3;
}

enum Direction {
    ALL = 1;
    FORWARD = 2;
    BACKWARD = 3;
}

message NpbAction {
    optional TunnelType tunnel_type = 1 [default = VXLAN];
    optional uint32 tunnel_id = 2;
    optional string tunnel_ip = 3;
    optional TapSide tap_side = 4;
    optional uint32 payload_slice = 5 [default = 65535];
    optional uint32 npb_acl_group_id = 6;
    optional uint32 tunnel_ip_id = 7;  // 分发点id, 限制在64000
    optional Direction direction = 8 [default = ALL];
}

// 字段含义查看README
message FlowAcl {
    optional uint32 id = 1;
    optional uint32 tap_type = 2;
    optional uint32 protocol = 6 [default = 256];
    optional string src_ports = 7;
    optional string dst_ports = 8;
    optional uint32 vlan = 9;
    repeated NpbAction npb_actions = 11;
    repeated int32 src_group_ids = 12;
    repeated int32 dst_group_ids = 13;
}

message FlowAcls {
    repeated FlowAcl flow_acl = 1;
}

message PodIp {
    optional uint32 pod_id = 1;
    optional string pod_name = 2;
    optional uint32 epc_id = 3;
    optional string ip = 4;
    optional uint32 pod_cluster_id = 5;
    repeated string container_ids = 6;
    optional string pod_node_ip = 7;
    optional uint32 pod_ns_id = 8;
    optional uint32 pod_group_id = 9;
    optional uint32 pod_group_type = 10;
}

message VtapIp {
    optional uint32 vtap_id = 1;
    optional uint32 epc_id = 2;
    optional string ip = 3;             // 采集器运行环境的IP
    optional uint32 pod_cluster_id = 4;
    optional uint32 team_id = 5;        // agent team id for ingester
    optional uint32 org_id = 6;         // agent org id for ingester
}

message SkipInterface {
    // 若该接口对应的虚拟机内已经部署采集器,
    // 发送此接口给虚拟机所在宿主机采集器
    optional uint64 mac = 1;
}

message DeepFlowServerInstanceInfo {
    optional string pod_name = 1;
    optional string node_name = 2;
}

message AnalyzerConfig {
    optional uint32 analyzer_id = 1;  // for Ingester assign a globally unique flow log ID
    optional uint32 region_id = 2;    // for Ingester get self region, and drop metrics not from the region.
}

message SyncResponse {
    optional Status status = 1;
    optional Config config = 2;
    optional string revision = 4;         // 指定升级的目标revision
    optional string self_update_url = 5;  // 指定升级的URL路径

    optional uint64 version_platform_data = 6 [default = 0]; /* only platform data */
    optional uint64 version_acls = 7 [default = 0];
    optional uint64 version_groups = 8 [default = 0];

    // The controller sends a container list to each agent, which contains a list of
    // containers in the operating system that the agent is running on (Note that only
    // the local container will be issued, not other machines)
    // =================================================================================
    // 控制器向每个 Agent 下发一个 container list，其内容为该 Agent 运行操作
    // 系统中的 container 列表（注意仅会下发本机的 container，不会包含其他机器的）
    repeated Container containers = 9;
    repeated Segment local_segments = 10;
    repeated Segment remote_segments = 11;
    optional bytes platform_data = 12;  // serialized result of `message PlatformData`, transmitted only when the content changes
    optional bytes flow_acls = 13;      // serialized result of `message FlowAcls`, transmitted only when the content changes
    optional bytes groups = 15;         // serialized result of `message Groups`, transmitted only when the content changes
    repeated TapType tap_types = 16;
    repeated PodIp pod_ips = 17;    // pod_name到vpc + ip的映射关系, 仅下发给数据节点
    repeated VtapIp vtap_ips = 18;  // vtap_id到vpc + ip的映射关系, 仅下发给数据节点
    repeated SkipInterface skip_interface = 19;
    repeated DeepFlowServerInstanceInfo deepflow_server_instances = 20;  // Only return the normal deepflow-servers of current Region for Ingester
    optional AnalyzerConfig analyzer_config = 21;                        // Only for Analyzer
}

message UpgradeRequest {
    optional string ctrl_ip = 1;
    optional string ctrl_mac = 3;
    optional string team_id = 4;    // agent team identity
}
message UpgradeResponse {
    optional Status status = 1;     // 调用是否成功
    optional bytes content = 2;     // 数据
    optional string md5 = 3;        // 文件MD5
    optional uint64 total_len = 4;  // 数据总长
    optional uint32 pkt_count = 5;  // 包总个数
    optional string k8s_image = 6;  // When k8s_image is not empty, ignore content
}

message PluginConfig {
    optional uint32 update_time = 1 [default = 0];  // latest epoch of all configured plugins
    repeated string wasm_plugins = 2;
    repeated string so_plugins = 3;
}

message Pcap {
    optional uint64 flow_id = 1;
    optional uint64 start_time = 2;  // ns
    optional uint64 end_time = 3;    // ns
    optional uint32 packet_count = 4;
    optional bytes packet_records = 5;
    repeated uint32 acl_gids = 6;
}

message PcapBatch {
    optional uint32 magic = 1;
    repeated Pcap batches = 2;
}

enum RoleType {
    ROLE_NONE = 0;
    ROLE_CLIENT = 1;
    ROLE_SERVER = 2;
}

message LabelRequest {
    optional string name = 1;
    optional string value = 2;
}

message LabelResponse {
    optional string name = 1;
    optional string value = 2;
    optional uint32 name_id = 3;
    optional uint32 value_id = 4;
    optional uint32 app_label_column_index = 5;  // starting from 1, if it is 0, it means the label is the target label. When requesting all, response will not carry index
}

message MetricLabelRequest {
    optional string metric_name = 1;
    optional uint32 pod_cluster_id = 2;
    optional uint32 epc_id = 3;
    repeated LabelRequest labels = 4;  // must contain instance and job label

    optional uint32 org_id = 11;
}

message MetricLabelResponse {
    optional string metric_name = 1;
    optional uint32 metric_id = 2;
    optional uint32 pod_cluster_id = 3;
    optional uint32 epc_id = 4;
    repeated LabelResponse label_ids = 5;

    optional uint32 org_id = 11;
}

message TargetRequest {
    optional string job = 1;
    optional string instance = 2;
    optional uint32 pod_cluster_id = 3;
    optional uint32 epc_id = 4;

    optional uint32 org_id = 11;
}

message TargetResponse {
    optional string job = 1;
    optional string instance = 2;
    optional uint32 job_id = 3;
    optional uint32 instance_id = 4;
    optional uint32 pod_cluster_id = 5;
    optional uint32 epc_id = 6;
    optional uint32 target_id = 7;              // when the Controller cannot determine the target_id, fill in 0
    repeated uint32 metric_ids = 8;             // when requesting all prometheus information, you need to carry the metric list related to the target
    repeated uint32 target_label_name_ids = 9;  // when the target labels change, it needs to be synchronized to Ingester regularly

    optional uint32 org_id = 11;
}

message PrometheusLabelRequest {
    repeated MetricLabelRequest request_labels = 1;
    repeated TargetRequest request_targets = 2;
    // When the request content is empty except version, it means requesting all metric label and target information.

    // the verson is used to identify whether the total data has changed due to expiration.
    // if the version of the Controller is inconsistent with the requested version by Ingester, the Controller need to send the full amount of data.
    // if they are consistent, there is no need to send the data.
    optional uint32 version = 3;
}

message PrometheusLabelResponse {
    repeated MetricLabelResponse response_label_ids = 1;
    repeated TargetResponse response_target_ids = 2;

    // when requesting all, the Controller need to carry the label value list and the version
    repeated OrgLabelResponse org_response_labels = 3;
    optional uint32 version = 4;
}

message OrgLabelResponse {
    optional uint32 org_id = 1;
    repeated LabelResponse response_labels = 2;
}

message PrometheusTargetRequest {
    optional uint32 version = 1;
}

message PrometheusTargetResponse {
    optional uint32 version = 1;
    repeated TargetResponse response_target_ids = 2;
}

message PodK8sLabelMap {
    optional uint32 pod_id = 1;
    repeated string label_name = 2;
    repeated string label_value = 3;
}

message DeviceMap {
    optional uint32 id = 1;
    optional uint32 type = 2;
    optional string name = 3;
}

message IdNameMap {
    optional uint32 id = 1;
    optional string name = 2;
}

message UniversalTagNameMapsRequest {
    optional uint32 org_id = 1;
}

message UniversalTagNameMapsResponse {
    optional uint32 version = 1;
    repeated PodK8sLabelMap pod_k8s_label_map = 2;
    repeated IdNameMap region_map = 3;
    repeated IdNameMap az_map = 4;
    repeated DeviceMap device_map = 5;
    repeated IdNameMap pod_node_map = 6;
    repeated IdNameMap pod_ns_map = 7;
    repeated IdNameMap pod_group_map = 8;
    repeated IdNameMap pod_map = 9;
    repeated IdNameMap pod_cluster_map = 10;
    repeated IdNameMap l3_epc_map = 11;
    repeated IdNameMap subnet_map = 12;
    repeated IdNameMap gprocess_map = 13;
    repeated IdNameMap vtap_map = 14;
}

message OrgIDsRequest {}

message OrgIDsResponse {
    repeated uint32 org_ids = 1;
    optional uint32 update_time = 2;
}
