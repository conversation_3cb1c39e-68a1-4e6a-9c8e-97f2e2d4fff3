/*
 * Copyright (c) 2012-2015 YunShan Networks, Inc.
 *
 * Author Name: <PERSON><PERSON>
 * Finish Date: 2015-11-24
 */

syntax = "proto2";

package DFI;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option (gogoproto.unmarshaler_all) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.sizer_all) = true;

option java_package = "cn.net.deepflow.message";
option java_outer_classname = "DFIMessage";
option go_package = "dfi";

message FlowHeader {
    required fixed64 sequence = 1; /* start from 1 */
}

message OvsPort {
    optional string mac = 1;
    optional uint32 dp_port = 2;
    optional string interface_name = 3;
    optional string bridge_name = 4;
}

message OvsTopology {
    repeated OvsPort ports = 1;
}

enum RequestType {
    FLOW_HEADER = 1;
    OVS_TOPOLOGY = 2;
    PACKET_HEADER = 3;
    METERING_HEADER = 4;
}

message Request {
    optional RequestType type = 1;
}

message StreamHeader {
    required fixed32 timestamp = 1;
    required fixed32 sequence = 2;
    required fixed32 action_flags = 3;
}

/*
 * Evaluated in host byte order.
 * If the value is the same as the default value, then the corresponding key
 * is unnecessary to be evaluated, in other words, has_xxx = 0
 */
message Flow {
    optional uint32 vtap_id = 1;
    optional uint32 exporter = 2;     /* Control IP for exporter */
    optional uint32 close_type = 7;   /* 0=unknown, 1=tcp-fin, 2=tcp-rst, 3=timeout,
                                         4=flood, 5=forced-report, 6=forced-close,
                                         7=half-open-timeout, 8=half-close-timeout */
    optional uint64 flow_id = 8;      /* 不存：The forcefully reported flow is still kept in
                                         exporter and this tuple (host, thread_index, flow_id)
                                         can be used to identify the flow in poseidon */
    optional uint32 flow_source = 9;  /* 流的来源  0: 镜像, 分光
                                                   1: sflow
                                                   2: netflow/netstream v5 */
    optional uint32 start_time = 11;  /* 1. First report: the time when the first packet arrives
                                         2. Latter reports: equals to end_time in last report */
    optional uint32 end_time = 12;    /* The time when exporter reports this flow */
    optional uint64 duration = 13;    /* 不存：
                                         1. For flows !FIN && !RST, equals to
                                             end_time - time_of_the_first_packet_in_the_flow
                                         2. For others, equals to
                                             max(arr_time_0_last, arr_time_1_last) -
                                                 time_of_the_first_packet_in_the_flow */
    /* L1 */
    optional uint32 tap_type = 21 [default = 0];/* 3=虚拟网络, 1~250=接入网络 */
    optional uint32 tap_port = 22 [default = 0];

    /* L2 */
    optional uint32 vlan = 31 [default = 0];
    optional uint32 eth_type = 32 [default = 0];
    optional uint64 mac_src = 33 [default = 0];     /* 0x0000123456789abc == 12:34:56:78:9a:bc */
    optional uint64 mac_dst = 34 [default = 0];     /* 0x0000123456789abc == 12:34:56:78:9a:bc */

    /* L3 */
    optional uint32 ip_src = 41;      /* 0x01020304 == ******* */
    optional uint32 ip_dst = 42;      /* 0x01020304 == ******* */
    optional bytes  ip6_src = 45;     /* big endian ipv6 address, null if ipv4 */
    optional bytes  ip6_dst = 46;     /* big endian ipv6 address, null if ipv4  */

    /* L4 */
    optional uint32 proto = 51 [default = 0];
    optional uint32 port_src = 52;
    optional uint32 port_dst = 53;
    optional uint32 tcp_flags_0 = 54 [default = 0];
    optional uint32 tcp_flags_1 = 55 [default = 0];

    /* Tunnel */
    optional uint32 tun_tx_id = 61 [default = 0];      /* Tunnel index where the flow is encapped, i.e., vl2_id */
    optional uint32 tun_tx_ip_0 = 62 [default = 0];  /* Tunnel source vtep IP where the flow is encapped */
    optional uint32 tun_tx_ip_1 = 63 [default = 0];  /* Tunnel destination vtep IP where the flow is encapped */
    optional uint32 tun_type = 64 [default = 0];    /* Tunnel type where the flow is encapped,
                                                       1=VXLAN, 2=NVGRE, 3=GENEVE */
    optional uint32 tun_rx_ip_0 = 65 [default = 0];
    optional uint32 tun_rx_ip_1 = 66 [default = 0];
    optional uint32 tun_tier = 67 [default = 0];
    optional uint32 tun_rx_id = 68 [default = 0];

    /* L7 */

    /* Packet Counters */
    optional uint64 byte_cnt_0 = 71 [default = 0];  /* Received byte cnt in this report period in
                                                       forward direction */
    optional uint64 byte_cnt_1 = 72 [default = 0];  /* Received byte cnt in this report period in
                                                       backward direction */
    optional uint64 pkt_cnt_0 = 73 [default = 0];   /* Received packet cnt in this report period in
                                                       forward direction */
    optional uint64 pkt_cnt_1 = 74 [default = 0];   /* Received packet cnt in this report period in
                                                       backward direction */
    optional uint64 total_byte_cnt_0 = 75 [default = 0]; /* Received byte cnt of this flow in
                                                            forward direction */
    optional uint64 total_byte_cnt_1 = 76 [default = 0]; /* Received byte cnt of this flow in
                                                            backward direction */
    optional uint64 total_pkt_cnt_0 = 77 [default = 0];  /* Received packet cnt of this flow in
                                                            forward direction */
    optional uint64 total_pkt_cnt_1 = 78 [default = 0];  /* Received packet cnt of this flow in
                                                            backward direction */

    /* Platform Data */
    optional uint32 subnet_id_0 = 100 [default = 0];          /* subnetid of mac_src, platform network ID */
    optional uint32 subnet_id_1 = 101 [default = 0];          /* subnetid of mac_dst, platform network ID */

    optional uint32 l3_device_type_0 = 102 [default = 0];     /* devicetype of ip_src, platform VM/VGW/TPD instance */
    optional uint32 l3_device_type_1 = 103 [default = 0];     /* devicetype of ip_dst, platform VM/VGW/TPD instance */
    optional uint32 l3_device_id_0 = 104 [default = 0];       /* deviceid of ip_src, platform instance ID */
    optional uint32 l3_device_id_1 = 105 [default = 0];       /* deviceid of ip_dst, platform instance ID */
    optional uint32 l3_epc_id_0 = 106 [default = 0];          /* epc_id of device of ip_src, platform project ID */
    optional uint32 l3_epc_id_1 = 107 [default = 0];          /* epc_id of device of ip_dst, platform project ID */
    optional uint32 host_0 = 108;                             /* host of device of ip_src */
    optional uint32 host_1 = 109;                             /* host of device of ip_dst */

    optional uint32 epc_id_0 = 120 [default = 0];             /* epc_id of device of mac_src, platform project ID */
    optional uint32 epc_id_1 = 121 [default = 0];             /* epc_id of device of mac_dst, platform project ID */
    optional uint32 device_type_0 = 122 [default = 0];        /* devicetype of mac_src, platform VM/VGW/TPD instance */
    optional uint32 device_type_1 = 123 [default = 0];        /* devicetype of mac_dst, platform VM/VGW/TPD instance */
    optional uint32 device_id_0 = 124 [default = 0];          /* deviceid of mac_src, platform instance ID */
    optional uint32 device_id_1 = 125 [default = 0];          /* deviceid of mac_dst, platform instance ID */
    optional bool is_l2_end_0 = 130 [default = false];        /* 网流mac_src对应的实例和网流被采集的Exporter是否在同一宿主机上 */
    optional bool is_l2_end_1 = 131 [default = false];        /* 网流mac_dst对应的实例和网流被采集的Exporter是否在同一宿主机上 */
    optional bool is_l3_end_0 = 132 [default = false];        /* 网流mac_src和ip_src对应的同一实例和网流被采集的Exporter是否在同一宿主机上 */
    optional bool is_l3_end_1 = 133 [default = false];        /* 网流mac_dst和ip_dst对应的同一实例和网流被采集的Exporter是否在同一宿主机上 */

    /* TCP Perf Data */
    /* tcp会话报文交互过程简图
    client端         探针点          server端
    |     SYN         t0|                   |
    | ----------------> | ----------------> |
    |     SYN         t1|                   |
    | ----------------> | ----------------> |
    |                   |                   |
    |                   |t2       SYN/ACK   |
    | <---------------- | <---------------- |
    |                   |t3       SYN/ACK   |
    | <---------------- | <---------------- |
    |                   |                   |
    |     ACK         t4|                   |
    | ----------------> | ----------------> |
    |     ACK         t5|                   |
    | ----------------> | ----------------> |
    |                   |                   |
    |    DATA(len>1)  t6|                   |
    | ----------------> | ----------------> |
    |    DATA(len>1)  t7|                   |
    | ----------------> | ----------------> |
    |                   |t8    ACK(len==0)  |
    | <---------------- | <---------------- |
    |                   |                   |
    |   DATA(len>1)   t9|                   |
    | ----------------> | ----------------> |
    |   DATA(len>1)  t10|                   |
    | ----------------> | ----------------> |
    |                   |t11 DATA/ACK(len>1)|
    | <---------------- | <---------------- | */

    optional uint64 rtt_avg = 142 [default = 0];               /* 往返时延(rtt), 单位：ns, tcp三次握手阶段
                                                                    rtt_syn = (t2-t1) + (t4-t3) */
    optional uint64 srt_avg = 143 [default = 0];               /* 系统响应时延(srt), 单位：ns, tcp数据传输阶段, 单个上报周期内stt的均值. 不包含握手阶段的网络时延.
                                                                    rtt_Nth = t8 - t7
                                                                    rtt = (rtt_1st + rtt_2nd + ... + rtt_Nth) / N, or
                                                                    rtt = (rtt * (N - 1) + rtt_Nth) / N */
    optional uint64 retrans_cnt_0 = 145 [default = 0];         /* 重传计数(retrans_client), tcp数据传输阶段, 单个上报周期内, client端的重传包数量, 不包含握手阶段重传数量 */
    optional uint64 retrans_cnt_1 = 146 [default = 0];         /* 重传计数(retrans_server), tcp数据传输阶段, 单个上报周期内, server端的重传包数量, 不包含握手阶段重传数量 */
    optional uint64 total_retrans_cnt = 147 [default = 0];     /* 重传计数(retrans), tcp数据传输阶段, 整条流client与server端的重传包数量之和, 不包含握手阶段重传数量 */
    optional uint64 zero_wnd_cnt_0 = 148 [default = 0];        /* 零窗口(zero_window_client), 单个上报周期内，client端零窗口包数量, 包含握手阶段零窗数量 */
    optional uint64 zero_wnd_cnt_1 = 149 [default = 0];        /* 零窗口(zero_window_server), 单个上报周期内，server端零窗口包数量, 包含握手阶段零窗数量 */
    optional uint64 art_avg = 157 [default = 0];               /* 应用响应时延(art), 单位：ns, tcp数据传输阶段, 单个上报周期内, art的均值，不包含握手阶段的网络时延.
                                                                  art_Nth = t11 - t10
                                                                  art = (art_1st + art_2nd + ... + art_Nth) / N, or
                                                                  art = (art * (N - 1) + art_Nth) / N */

    optional uint64 rtt_client_avg = 165 [default = 0];        /* The RTT in the handshake phase of the flow.
                                                                  When pkt_cnt_0 >= 2 && pkt_cnt_1 >= 1,
                                                                  SYN/ACK -> ACK,
                                                                  rtt_syn = arr_time_0_1 (ACK) - arr_time_1_0 (SYN/ACK) */
    optional uint64 rtt_server_avg = 166 [default = 0];        /* The RTT in the handshake phase of the flow.
                                                                  When pkt_cnt_0 >= 1 && pkt_cnt_1 >= 1,
                                                                  SYN -> SYN/ACK,
                                                                  rtt_syn = arr_time_1_0 (SYN/ACK) - arr_time_0_0 (SYN) */
    /* Geo Info */
    optional uint32 province_src = 174 [default = 0];           /* 源IP端对应省份 */
    optional uint32 province_dst = 175 [default = 0];           /* 目的IP对应省份 */
    /* Other */
    optional uint32 cast_type_map_0   = 200 [default = 0];      /* 仅包含TSDB中的几个CastType标志位选项 */
    optional uint32 cast_type_map_1   = 201 [default = 0];      /* 仅包含TSDB中的几个CastType标志位选项 */
    optional uint32 tcp_flags_map_0   = 202 [default = 0];      /* 仅包含TSDB中的几个TCP标志位选项 */
    optional uint32 tcp_flags_map_1   = 203 [default = 0];      /* 仅包含TSDB中的几个TCP标志位选项 */
    optional uint32 ttl_map_0         = 204 [default = 0];      /* 仅包含TSDB中的几个TTL标志位选项 */
    optional uint32 ttl_map_1         = 205 [default = 0];      /* 仅包含TSDB中的几个TTL标志位选项 */
    optional uint32 packet_size_map_0 = 206 [default = 0];      /* 仅包含TSDB中的几个PacketSize标志位选项 */
    optional uint32 packet_size_map_1 = 207 [default = 0];      /* 仅包含TSDB中的几个PacketSize标志位选项 */
}

message Metering {
        optional uint32 exporter = 1;
        optional uint32 timestamp = 2;
        optional uint32 tap_type = 3 [default = 0];
        optional uint32 tap_port = 4 [default = 0];
        optional uint32 vlan = 11 [default = 0];
        optional uint32 ip_src = 21 [default = 0];
        optional uint32 ip_dst = 22 [default = 0];
        optional uint32 proto = 31 [default = 0];
        optional uint32 port_src = 32 [default = 0];
        optional uint32 port_dst = 33 [default = 0];
        optional uint64 byte_cnt_0 = 41 [default = 0];
        optional uint64 byte_cnt_1 = 42 [default = 0];
        optional uint64 pkt_cnt_0 = 43 [default = 0];
        optional uint64 pkt_cnt_1 = 44 [default = 0];
        /* Platform Data */
        optional uint32 l3_epc_id_0 = 51 [default = 0];
        optional uint32 l3_epc_id_1 = 52 [default = 0];
}

/* 注意：message中的tag不要大于31，大于31以后key会多占用1个字节。
 *    1. 字段delta_key表示记录该key和前一个包的该key的值的差量
 *       假定所有差量都可能为负数，使用sint，当差值为0时不用记录
 *    2. 字段key为null（has_key=0）表示该key和前一个包的该key的值相同
 *    3. 字段key不为null（has_key=1）表示记录原始值
 *    4. 每条网流的首包要记录所有key和delta_key
 */
message EthernetHeader {
    optional uint64 destination_address = 1;          //    6 B
    optional uint64 source_address = 2;               //    6 B
    optional uint32 ether_type = 3;                   //    2 B
    optional uint32 vlan_tag = 4;                     //    4 B
}

message Ipv4Header {
    optional uint32 version = 1;                      //  1/2 B
    optional uint32 ihl = 2;                          //  1/2 B
    optional uint32 tos = 3;                          //    1 B
    optional uint32 total_length = 4;                 //    2 B
    optional sint32 delta_identification = 5;         //    2 B
    // flags is combined into fragment offset
    optional sint32 delta_fragment_offset = 6;        //    2 B
    optional uint32 ttl = 7;                          //    1 B
    optional uint32 protocol = 8;                     //    1 B
    // ignore check_sum if it is correct
    optional uint32 header_checksum = 9;              //    2 B
    // IP address always has 4B, use fixed encoding
    optional fixed32 source_address = 10;             //    4 B
    optional fixed32 destination_address = 11;        //    4 B

    // 5.0不考虑IP option的压缩，直接将byte数组放在此处即可
    // options基本由byte组成，从wiki来看也没有压缩空间：
    // https://en.wikipedia.org/wiki/IPv4
    optional bytes options = 12;                      // 0-40 B
}

message TcpHeader {
    optional uint32 source_port = 1;                  //    2 B
    optional uint32 destination_port = 2;             //    2 B
    optional sint64 delta_sequence_number = 3;        //    4 B
    optional sint64 delta_acknowledgement_number = 4; //    4 B
    optional uint32 data_offset = 5;                  //  1/2 B
    optional uint32 reserved = 6;                     //  1/2 B
    optional uint32 flags = 7;                        //    1 B
    optional sint32 delta_window = 8;                 //    2 B
    // checksum is ignored here                             2 B
    optional uint32 urgent_pointer = 9;               //    2 B

    // TCP option很常见的，由byte、u16、u32组成，且存在很多不确定性
    // 5.0考虑将TCP options分割成10个4字节word，仅记录差量
    // https://en.wikipedia.org/wiki/Transmission_Control_Protocol
    optional sint64 delta_option_0w = 10;             //    4 B
    optional sint64 delta_option_1w = 11;             //    4 B
    optional sint64 delta_option_2w = 12;             //    4 B
    optional sint64 delta_option_3w = 13;             //    4 B
    optional sint64 delta_option_4w = 14;             //    4 B
    optional sint64 delta_option_5w = 15;             //    4 B
    optional sint64 delta_option_6w = 16;             //    4 B
    optional sint64 delta_option_7w = 17;             //    4 B
    optional sint64 delta_option_8w = 18;             //    4 B
    optional sint64 delta_option_9w = 19;             //    4 B
}

message UdpHeader {
    optional uint32 source_port = 1;                  //    2 B
    optional uint32 destination_port = 2;             //    2 B
    optional uint32 length = 3;                       //    2 B
    // checksum is ignored here                             2 B
}

message PacketHeader {
    optional sint64 delta_timestamp = 1;  // microseconds,  8 B
    optional EthernetHeader ethernet = 2; // L2 header, 14-18 B
    optional Ipv4Header ipv4 = 3;         // L3 header, 20-60 B
    optional TcpHeader tcp = 4;           // L4 header, 18-58 B
    optional UdpHeader udp = 5;           // L4 header,     6 B
}

enum Direction
{
    FORWARD = 0; // 正向，发起请求的流方向
    REVERSE = 1; // 反向，回复请求的流方向
}

/* protobuf-message format */
message FlowPktHdrBatch {
    optional uint32 host = 1;       // DFI所在节点的本地IP，无需填写，由控制器推断
    optional uint64 flow_id = 2;
    optional Direction direction = 3 [default = FORWARD];
    optional uint32 sequence = 4;

    // 连续多个PacketHeader序列化（压缩）以后的字节数组，最大长度为BUFFER_SIZE
    // 内存排布：header_0, ..., header_i, ..., header_n, 0xFF结束标记
    //   header_i排布：compressed_header_size_i(1B), compressed_header_i
    optional bytes compressed_headers = 5;
    optional uint32 close_type = 6;
    optional uint32 exporter = 7;
}
