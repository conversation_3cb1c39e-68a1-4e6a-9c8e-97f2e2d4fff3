/*
 * Copyright (c) 2012-2013 YunShan Networks, Inc.
 *
 * Author Name: <PERSON><PERSON>
 * Finish Date: 2013-11-18
 */
syntax = "proto2";

package Header;
option java_package = "cn.net.deepflow.message";
option java_outer_classname = "HeaderProtos";

enum Module {
    LCMD               = 1;
    VMDRIVER           = 2;
    LCPD               = 3;
    LCMOND             = 4;
    LCSNFD             = 5;
    VMCLOUDADAPTER     = 6;
    POSTMAN            = 7;
    LCRMD              = 8;
    TALKER             = 9;
    THIRDADAPTER       = 10;
    PMCLOUDADAPTER     = 11;
    MNTNCT             = 12;
    NOTIFICATIONCENTER = 13;
    STOREKEEPER        = 14;
    ALARM              = 15;
}

enum Type {
    UNICAST = 1;
}

message Header {
    /* all fields must be required and fixed */
    required fixed32 sender = 1; /* module of sender */
    required fixed32 type = 2; /* message type */
    required fixed32 length = 3; /* data length after this header */
    required fixed32 seq = 4 [default = 0]; /* message sequence number */
}
