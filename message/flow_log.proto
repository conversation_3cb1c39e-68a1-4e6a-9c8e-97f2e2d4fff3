syntax = "proto3";

package flow_log;

// rust 不支持以下import和option, 先删除，用于ingester时，需要加回
// import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// option (gogoproto.unmarshaler_all) = true;
// option (gogoproto.marshaler_all) = true;
// option (gogoproto.sizer_all) = true;

option go_package = "pb";

message TaggedFlow {
    Flow flow = 1;
}

message Flow {
    FlowKey flow_key = 1;
    FlowMetricsPeer metrics_peer_src = 2;
    FlowMetricsPeer metrics_peer_dst = 3;
    TunnelField Tunnel = 4;

    uint64 flow_id = 5;

    uint64 start_time = 6;
    uint64 end_time = 7;
    uint64 duration = 8;
    // uint64 flow_start_time = 9;  // 目前无需发送

    uint32 vlan = 10;
    uint32 eth_type = 11;

    uint32 has_perf_stats = 12;
    FlowPerfStats perf_stats = 13;

    uint32 close_type = 14;
    uint32 signal_source = 15;

    uint32 is_active_service = 16;
    uint32 queue_hash = 17;
    uint32 is_new_flow = 18;
    uint32 tap_side = 19;

    // TCP Seq
    uint32 syn_seq = 20;
    uint32 synack_seq = 21;
    uint32 last_keepalive_seq = 22;
    uint32 last_keepalive_ack = 23;

    repeated uint32 acl_gids = 24;

    uint32 direction_score = 25;

    string request_domain = 26;

    repeated uint64 aggregated_flow_ids = 27;
}

message FlowKey {
    uint32 vtap_id = 1;
    uint32 tap_type = 2;
    uint64 tap_port = 3;
    uint64 mac_src = 4;
    uint64 mac_dst = 5;

    uint32 ip_src = 6;
    uint32 ip_dst = 7;
    bytes ip6_src = 8;
    bytes ip6_dst = 9;

    uint32 port_src = 10;
    uint32 port_dst = 11;

    uint32 proto = 12;
}

message FlowMetricsPeer {
    uint64 byte_count = 1;
    uint64 l3_byte_count = 2;
    uint64 l4_byte_count = 3;
    uint64 packet_count = 4;
    uint64 total_byte_count = 5;
    uint64 total_packet_count = 6;

    uint64 first = 7;
    uint64 last = 8;
    uint32 tcp_flags = 9;
    int32 l3_epc_id = 10;

    uint32 is_l2_end = 11;
    uint32 is_l3_end = 12;
    uint32 is_active_host = 13;
    uint32 is_device = 14;
    uint32 is_vip_interface = 15;
    uint32 is_vip = 16;

    uint32 real_ip = 20;
    uint32 real_port = 21;

    uint32 gpid = 22;
}

message TunnelField {
    uint32 tx_ip0 = 1;
    uint32 tx_ip1 = 2;
    uint32 rx_ip0 = 3;
    uint32 rx_ip1 = 4;
    uint32 tx_mac0 = 5;
    uint32 tx_mac1 = 6;
    uint32 rx_mac0 = 7;
    uint32 rx_mac1 = 8;
    uint32 tx_id = 9;
    uint32 rx_id = 10;
    uint32 tunnel_type = 11;
    uint32 tier = 12;
    uint32 is_ipv6 = 13;
}

message FlowPerfStats {
    TCPPerfStats tcp = 1;
    L7PerfStats l7 = 2;
    uint32 l4_protocol = 3;
    uint32 l7_protocol = 4;
    uint32 l7_failed_count = 5;
}

message TCPPerfStats {
    uint32 rtt_client_max = 1;
    uint32 rtt_server_max = 2;
    uint32 srt_max = 3;
    uint32 art_max = 4;

    uint32 rtt = 5;
    // Deprecated in v6.4.1: uint32 rtt_client_sum = 6;
    // Deprecated in v6.4.1: uint32 rtt_client_sum = 7;
    uint32 srt_sum = 8;
    uint32 art_sum = 9;
    // Deprecated in v6.4.1: uint32 rtt_client_count = 10;
    // Deprecated in v6.4.1: uint32 rtt_client_count = 11;
    uint32 srt_count = 12;
    uint32 art_count = 13;

    TcpPerfCountsPeer counts_peer_tx = 14;
    TcpPerfCountsPeer counts_peer_rx = 15;

    uint32 total_retrans_count = 16;

    uint32 syn_count = 17;
    uint32 synack_count = 18;

    uint32 cit_max = 19;
    uint32 cit_sum = 20;
    uint32 cit_count = 21;
}

message TcpPerfCountsPeer {
    uint32 retrans_count = 1;
    uint32 zero_win_count = 2;
}

message L7PerfStats {
    uint32 request_count = 1;
    uint32 response_count = 2;
    uint32 err_client_count = 3;
    uint32 err_server_count = 4;
    uint32 err_timeout = 5;
    uint32 rrt_count = 6;
    uint64 rrt_sum = 7;
    uint32 rrt_max = 8;
    uint32 tls_rtt = 9;
}

message L7Request {
    string req_type = 1;
    string domain = 2;
    string resource = 3;
    string endpoint = 4;
}

message L7Response {
    uint32 status = 1;
    int32 code = 2;
    string exception = 3;
    string result = 4;
}

message TraceInfo {
    string trace_id = 1;
    string span_id = 2;
    string parent_span_id = 3;
}

message ExtendedInfo {
    string service_name = 1;
    string client_ip = 2;
    uint32 request_id = 3;
    string x_request_id_0 = 4;
    string http_user_agent = 6;
    string http_referer = 7;
    string rpc_service = 8;
    string protocol_str = 9;
    string x_request_id_1 = 10;

    repeated string attribute_names = 16;
    repeated string attribute_values= 17;
    repeated string metrics_names = 18;
    repeated double metrics_values = 19;
}

message AppProtoLogsData {
    AppProtoLogsBaseInfo base = 1;

    int32 req_len = 9;
    int32 resp_len = 10;
    L7Request req = 11;
    L7Response resp = 12;
    string version = 13;
    TraceInfo trace_info = 14;
    ExtendedInfo ext_info = 15;

    uint32 row_effect = 16;
    uint32 direction_score = 17;
    // Flags Bimmap:
    // 32                                  1     0
    // +-----------------------------------+-----+
    // | Reserve                           | TLS |
    // +-----------------------------------+-----+
    uint32 flags = 18;
    // Excluding L2-L4 packet header
    uint32 captured_request_byte = 19;
    uint32 captured_response_byte = 20;
}

message AppProtoLogsBaseInfo {
    uint64 start_time = 1;
    uint64 end_time = 2;
    uint64 flow_id = 3;
    uint64 tap_port = 4;
    uint32 vtap_id = 5;
    uint32 tap_type = 6;
    uint32 is_ipv6 = 7;
    uint32 tap_side = 8;
    AppProtoHead head = 9;

    uint64 mac_src = 10;
    uint64 mac_dst = 11;

    uint32 ip_src = 12;
    uint32 ip_dst = 13;

    bytes ip6_src = 14;
    bytes ip6_dst = 15;

    int32 l3_epc_id_src = 16;
    int32 l3_epc_id_dst = 17;

    uint32 port_src = 18;
    uint32 port_dst = 19;

    uint32 protocol = 20;
    uint32 is_vip_interface_src = 21;
    uint32 is_vip_interface_dst = 22;

    uint32 req_tcp_seq = 23;
    uint32 resp_tcp_seq = 24;

    uint32 process_id_0 = 25;
    uint32 process_id_1 = 26;
    string process_kname_0 = 27;
    string process_kname_1 = 28;
    uint64 syscall_trace_id_request = 29;
    uint64 syscall_trace_id_response = 30;
    uint32 syscall_trace_id_thread_0 = 31;
    uint32 syscall_trace_id_thread_1 = 32;
    uint32 syscall_cap_seq_0 = 33;
    uint32 syscall_cap_seq_1 = 34;
    uint32 gpid_0 = 35;
    uint32 gpid_1 = 36;
    // Deprecated in v6.4.1: uint32 netns_id_0 = 37;
    // Deprecated in v6.4.1: uint32 netns_id_1 = 38;
    uint64 syscall_coroutine_0 = 39;
    uint64 syscall_coroutine_1 = 40;
    uint32 pod_id_0 = 41;
    uint32 pod_id_1 = 42;
    uint32 biz_type = 43;
}

message AppProtoHead {
    uint32 proto = 1;
    uint32 msg_type = 2;

    uint64 rrt = 5;
}

message MqttTopic {
    string name = 1;
    int32 qos = 2; // -1 mean not exist qos
}

message ThirdPartyTrace {
    bytes data = 1;
    bytes peer_ip = 2;
    string uri = 3;
    repeated string extend_keys = 4;
    repeated string extend_values = 5;
}
