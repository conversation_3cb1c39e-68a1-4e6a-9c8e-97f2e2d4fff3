syntax = "proto3";

package k8s_event;

// rust 不支持以下import和option，暂时删除，go客户端使用时需要加回
// import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// option (gogoproto.unmarshaler_all) = true;
// option (gogoproto.marshaler_all) = true;
// option (gogoproto.sizer_all) = true;

option go_package = "k8s_event";

message Source {
    string component = 1;
}

message InvolvedObject {
    string field_path = 1;
    string kind = 2;
    string name = 3;
}

// refer to: https://github.com/kubernetes/kubernetes/blob/master/pkg/apis/core/types.go#L5497
enum EventType {
    NORMAL = 0;
    WARNING = 1;
}

message KubernetesEvent {
    uint64 first_timestamp = 1; // unit: us
    InvolvedObject involved_object = 2;
    string message = 3;
    string reason = 4; // refer to: https://github.com/kubernetes/kubernetes/blob/master/pkg/kubelet/events/event.go
    Source source = 5;
    EventType type = 6;
}
