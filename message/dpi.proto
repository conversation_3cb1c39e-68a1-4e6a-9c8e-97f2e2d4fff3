syntax = "proto2";

package DPI;
option java_package = "cn.net.deepflow.message";
option java_outer_classname = "DPIMessage";

enum RequestType {
    DPI_SEQ = 1;
}

message Request {
    optional RequestType type = 1;
}

message Header {
    required fixed64 sequence = 1; /* start from 1 */
}

message Dpi {
    optional uint32 host = 1;
    optional uint64 start_time = 2;
    optional uint64 end_time = 3;
    optional uint32 exporter = 4;
    optional uint64 flow_id = 5;

    optional uint32 vlan = 11 [default = 0];
    optional uint32 ip_src = 12 [default = 0];
    optional uint32 ip_dst = 13 [default = 0];
    optional uint32 proto = 14 [default = 0];
    optional uint32 port_src = 15 [default = 0];
    optional uint32 port_dst = 16 [default = 0];

    optional uint64 pkt_cnt = 21 [default = 0];
    optional uint64 byte_cnt = 22 [default = 0];

    optional string host_server_name = 31 [default = ''];

    optional Http http = 101;
    optional Dns dns = 102;
    optional Dhcp dhcp = 103;
}

message Http {
    optional string url = 1 [default = ''];
    optional string method = 2 [default = ''];
    optional string user_agent = 3 [default = ''];
    optional string content_type = 4 [default = ''];
    optional uint64 content_length = 5 [default = 0];

    optional uint32 response_code = 11 [default = 0];
}

message Dns {
    optional uint32 identifier = 1 [default = 0];
    optional uint32 op_code = 2 [default = 0];
    optional string query_name = 3 [default = ''];

    optional uint32 response_code = 11 [default = 0];
    repeated Dnsanswer dnsanswer = 12;
}

message Dnsanswer {
    optional uint32 record_type = 1 [default = 0];
    optional uint32 record_ttl = 2 [default = 0];
    optional string answer_name = 3 [default = ''];
    optional uint32 ipv4 = 4 [default = 0]; /* intel little endian host order */
    repeated uint32 ipv6 = 5;
}

message Dhcp {
    optional uint32 ip = 1 [default = 0];
    optional uint64 mac = 2 [default = 0];
    optional string hostname = 3 [default = ''];
}
