### Table of Contents

**[DeepFlow release main](#main)**<br/>
**[Changelog for v6.5](https://github.com/deepflowio/deepflow/blob/v6.5/docs/CHANGELOG-6-5.md)**<br/>

# Changelog

### <a id="main"></a>DeepFlow release main

#### Bug Fix
* fix: agnet - eBPF Fix the kernel kick on CPU0 was not triggered [#8817](https://github.com/deepflowio/deepflow/pull/8817) by [yinjiping](https://github.com/yinjiping)
* fix: agent - eBPF Fix Crashes Caused by Packet Count Statistics [#8816](https://github.com/deepflowio/deepflow/pull/8816) by [yinjiping](https://github.com/yinjiping)
* fix: wan type cidr may not be tagged [#8814](https://github.com/deepflowio/deepflow/pull/8814) by [lzf575](https://github.com/lzf575)
* fix: the throttler cannot write all data to the queue at once [#8791](https://github.com/deepflowio/deepflow/pull/8791) by [lzf575](https://github.com/lzf575)
* fix: Change oracle parse to accept multiple logs [#8756](https://github.com/deepflowio/deepflow/pull/8756) by [rvql](https://github.com/rvql)
* fix: agent - eBPF Fix process event type size [#8752](https://github.com/deepflowio/deepflow/pull/8752) by [yinjiping](https://github.com/yinjiping)
* fix: agent - eBPF Fix Event Type Value [#8745](https://github.com/deepflowio/deepflow/pull/8745) by [yinjiping](https://github.com/yinjiping)
* fix: agent group config api reponses invalid error [#8739](https://github.com/deepflowio/deepflow/pull/8739) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: failed to create agent group config using yaml [#8731](https://github.com/deepflowio/deepflow/pull/8731) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: the analyzer mode supports ebpf dpdk [#8721](https://github.com/deepflowio/deepflow/pull/8721) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: Configuration text [#8701](https://github.com/deepflowio/deepflow/pull/8701) by [rvql](https://github.com/rvql)
* fix: recorder fails to clean 10w data at one time [#8699](https://github.com/deepflowio/deepflow/pull/8699) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: agent - eBPF Correct the maximum data push delay [#8694](https://github.com/deepflowio/deepflow/pull/8694) by [yinjiping](https://github.com/yinjiping)
* fix: fix ip filter error [#8693](https://github.com/deepflowio/deepflow/pull/8693) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: modify log level [#8690](https://github.com/deepflowio/deepflow/pull/8690) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: query percentile to min/max time [#8685](https://github.com/deepflowio/deepflow/pull/8685) by [taloric](https://github.com/taloric)
* fix: agent group api responses invalid int value [#8681](https://github.com/deepflowio/deepflow/pull/8681) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: invalid src_interface [#8680](https://github.com/deepflowio/deepflow/pull/8680) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: Modify the range of the pcp value [#8678](https://github.com/deepflowio/deepflow/pull/8678) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: nil pointer may cause panic [#8670](https://github.com/deepflowio/deepflow/pull/8670) by [lzf575](https://github.com/lzf575)
* fix: agent config file.io_event.collect_mode [#8668](https://github.com/deepflowio/deepflow/pull/8668) by [askyrie](https://github.com/askyrie)
* fix: adapt pprof u64 params [#8666](https://github.com/deepflowio/deepflow/pull/8666) by [taloric](https://github.com/taloric)
* fix: failed to update old version agent group config when creating new version [#8662](https://github.com/deepflowio/deepflow/pull/8662) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: change proc socket sync interval config name and default value [#8658](https://github.com/deepflowio/deepflow/pull/8658) by [rvql](https://github.com/rvql)
* fix: agent group configuration api response unexcepted value [#8647](https://github.com/deepflowio/deepflow/pull/8647) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: add alarm_label table [#8642](https://github.com/deepflowio/deepflow/pull/8642) by [SongZhen0704](https://github.com/SongZhen0704)
* fix: parse 1d aggr table failed [#8645](https://github.com/deepflowio/deepflow/pull/8645) by [lzf575](https://github.com/lzf575)
* fix: modify agent config example [#8638](https://github.com/deepflowio/deepflow/pull/8638) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: errors occurred when modifying some agent group configuration [#8602](https://github.com/deepflowio/deepflow/pull/8602) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix:  pcap,l4_packet, spantrace data exception [#8594](https://github.com/deepflowio/deepflow/pull/8594) by [lzf575](https://github.com/lzf575)
* fix: TODOs in agent config [#8590](https://github.com/deepflowio/deepflow/pull/8590) by [rvql](https://github.com/rvql)
* fix: trim-tunnel-type has not taken effect [#8577](https://github.com/deepflowio/deepflow/pull/8577) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: the policy does not check memory in analyzer mode [#8540](https://github.com/deepflowio/deepflow/pull/8540) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: eBPF SELinux permission-related exit [#8534](https://github.com/deepflowio/deepflow/pull/8534) by [yinjiping](https://github.com/yinjiping)
* fix: failed to downgrade agent_group_configuration [#8520](https://github.com/deepflowio/deepflow/pull/8520) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: panic when prepare write failed [#8523](https://github.com/deepflowio/deepflow/pull/8523) by [lzf575](https://github.com/lzf575)
* fix: panic when app service tag write failed [#8521](https://github.com/deepflowio/deepflow/pull/8521) by [lzf575](https://github.com/lzf575)
* fix: Python uprobe not registered for symbols in main executable [#8515](https://github.com/deepflowio/deepflow/pull/8515) by [rvql](https://github.com/rvql)
* fix: mongo log was missing the request content [#8477](https://github.com/deepflowio/deepflow/pull/8477) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: Default values in agent config [#8476](https://github.com/deepflowio/deepflow/pull/8476) by [rvql](https://github.com/rvql)
* fix: ckwriter writing failure caused by not writing data for a long time [#8470](https://github.com/deepflowio/deepflow/pull/8470) by [lzf575](https://github.com/lzf575)
* fix: Setting type, parser and default value [#8464](https://github.com/deepflowio/deepflow/pull/8464) by [rvql](https://github.com/rvql)
* fix: agent - eBPF Adjust the control logic for modifying map size [#8456](https://github.com/deepflowio/deepflow/pull/8456) by [yinjiping](https://github.com/yinjiping)
* fix: no need to retry after writing failure [#8453](https://github.com/deepflowio/deepflow/pull/8453) by [lzf575](https://github.com/lzf575)
* fix: agent stuck when ntp is turned on [#8445](https://github.com/deepflowio/deepflow/pull/8445) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent config api panic when requests float type value [#8442](https://github.com/deepflowio/deepflow/pull/8442) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: Mega units in configuration [#8435](https://github.com/deepflowio/deepflow/pull/8435) by [rvql](https://github.com/rvql)
* fix: L4/L7 log store tap types configuration [#8434](https://github.com/deepflowio/deepflow/pull/8434) by [rvql](https://github.com/rvql)
* fix: Invalid type in config cause parse failure [#8433](https://github.com/deepflowio/deepflow/pull/8433) by [rvql](https://github.com/rvql)
* fix: container env supports aggr packets with and without tunnels [#8427](https://github.com/deepflowio/deepflow/pull/8427) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: modify the matching logic of flow_node [#8420](https://github.com/deepflowio/deepflow/pull/8420) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: Compatible with older configurations [#8377](https://github.com/deepflowio/deepflow/pull/8377) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: More configuration parse bugs [#8364](https://github.com/deepflowio/deepflow/pull/8364) by [rvql](https://github.com/rvql)
* fix: LogLevel deserialize [#8363](https://github.com/deepflowio/deepflow/pull/8363) by [rvql](https://github.com/rvql)
* fix: Duration deserialize in config [#8361](https://github.com/deepflowio/deepflow/pull/8361) by [rvql](https://github.com/rvql)
* fix: Accept empty value for some fields in config [#8360](https://github.com/deepflowio/deepflow/pull/8360) by [rvql](https://github.com/rvql)
* fix: agent - eBPF Total process message initialization (#8331) [#8334](https://github.com/deepflowio/deepflow/pull/8334) by [yinjiping](https://github.com/yinjiping)
* fix: remove dynamic libpcap [#8321](https://github.com/deepflowio/deepflow/pull/8321) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: when EPC is unknown, region should not be unknown [#8315](https://github.com/deepflowio/deepflow/pull/8315) by [lzf575](https://github.com/lzf575)
* fix agent - eBPF Fix the parsing of the kernel version [#8311](https://github.com/deepflowio/deepflow/pull/8311) by [yinjiping](https://github.com/yinjiping)
* fix: Fix warning log in kube-rs crate by upgrading to 0.77 [#8305](https://github.com/deepflowio/deepflow/pull/8305) by [rvql](https://github.com/rvql)
* fix: agent - eBPF Random time to process symbol table creation [#8283](https://github.com/deepflowio/deepflow/pull/8283) by [yinjiping](https://github.com/yinjiping)
* fix: agent - eBPF Handle the scenario where CONFIG_NET_NS is disabled [#8282](https://github.com/deepflowio/deepflow/pull/8282) by [yinjiping](https://github.com/yinjiping)
* fix: add export thread id [#8278](https://github.com/deepflowio/deepflow/pull/8278) by [lzf575](https://github.com/lzf575)
* fix: topK multiple parameter query error [#8249](https://github.com/deepflowio/deepflow/pull/8249) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: add process_id in profile data query [#8238](https://github.com/deepflowio/deepflow/pull/8238) by [taloric](https://github.com/taloric)
* fix: custom data source creation failed in enterprise ClickHouse [#8233](https://github.com/deepflowio/deepflow/pull/8233) by [lzf575](https://github.com/lzf575)
* fix: agent - eBPF Fix the process matcher handling for UPROBE [#8230](https://github.com/deepflowio/deepflow/pull/8230) by [yinjiping](https://github.com/yinjiping)
* fix: agent - Other eBPF bin-file will be selected when kfunc loading fails [#8226](https://github.com/deepflowio/deepflow/pull/8226) by [yinjiping](https://github.com/yinjiping)
* fix: prometheus.samples table init failed [#8225](https://github.com/deepflowio/deepflow/pull/8225) by [lzf575](https://github.com/lzf575)
* fix: prometheus data writing fails when using ByConity database [#8222](https://github.com/deepflowio/deepflow/pull/8222) by [lzf575](https://github.com/lzf575)
* fix: K8s watch env logs [#8217](https://github.com/deepflowio/deepflow/pull/8217) by [rvql](https://github.com/rvql)
* fix: reduce kafka app misidentification [#8205](https://github.com/deepflowio/deepflow/pull/8205) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent build error [#8204](https://github.com/deepflowio/deepflow/pull/8204) by [TomatoMr](https://github.com/TomatoMr)
* fix: ebpf dispatcher build error [#8197](https://github.com/deepflowio/deepflow/pull/8197) by [TomatoMr](https://github.com/TomatoMr)
* fix: peer connection api limit [#8194](https://github.com/deepflowio/deepflow/pull/8194) by [askyrie](https://github.com/askyrie)
* fix: incorrect tcp rrt [#8152](https://github.com/deepflowio/deepflow/pull/8152) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: the aggregation method of unsummable fields of custom data_source is wrong [#8181](https://github.com/deepflowio/deepflow/pull/8181) by [lzf575](https://github.com/lzf575)
* fix: applicaition log decode failed causes subsequent data anomalies [#8179](https://github.com/deepflowio/deepflow/pull/8179) by [lzf575](https://github.com/lzf575)
* fix: Bad cstr passed into pcap_compile_nopcap [#8171](https://github.com/deepflowio/deepflow/pull/8171) by [rvql](https://github.com/rvql)
* fix: memory profile feature compile failed [#8157](https://github.com/deepflowio/deepflow/pull/8157) by [lzf575](https://github.com/lzf575)
* fix: memory profile data cannot be compressed [#8155](https://github.com/deepflowio/deepflow/pull/8155) by [lzf575](https://github.com/lzf575)
* fix: dispatcher pipelines is empty [#8146](https://github.com/deepflowio/deepflow/pull/8146) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent - eBPF fix errors caused by java_syms_update_tasks_head [#8145](https://github.com/deepflowio/deepflow/pull/8145) by [yinjiping](https://github.com/yinjiping)
* fix: querier data visibility filter error [#8139](https://github.com/deepflowio/deepflow/pull/8139) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: incorrect request resource in kafka logs [#8141](https://github.com/deepflowio/deepflow/pull/8141) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: specify the go mod version when cli verify [#8135](https://github.com/deepflowio/deepflow/pull/8135) by [jiumos](https://github.com/jiumos)
* fix: Memory profile not outputing data [#8129](https://github.com/deepflowio/deepflow/pull/8129) by [rvql](https://github.com/rvql)
* fix: Crash caused by UnwindEntryShard on stack [#8125](https://github.com/deepflowio/deepflow/pull/8125) by [rvql](https://github.com/rvql)
* fix:  specify the go mod version when building the cli [#8122](https://github.com/deepflowio/deepflow/pull/8122) by [jiumos](https://github.com/jiumos)
* fix: Fix DWARF config on unsupported systems and tracer restart [#8118](https://github.com/deepflowio/deepflow/pull/8118) by [rvql](https://github.com/rvql)
* fix: agent - eBPF Fix the calculation of total process time [#8117](https://github.com/deepflowio/deepflow/pull/8117) by [yinjiping](https://github.com/yinjiping)
* fix: querier TopK supporrt resource_id [#8104](https://github.com/deepflowio/deepflow/pull/8104) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: remove unnecessary restart for ebpf collector [#8101](https://github.com/deepflowio/deepflow/pull/8101) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: Bad event type for offcpu profile [#8078](https://github.com/deepflowio/deepflow/pull/8078) by [rvql](https://github.com/rvql)
* fix: agent command panic [#8071](https://github.com/deepflowio/deepflow/pull/8071) by [roryye](https://github.com/roryye)
* fix: clickhouse disk monitoring failure [#8069](https://github.com/deepflowio/deepflow/pull/8069) by [lzf575](https://github.com/lzf575)
* fix: Tars parse failure cause agent exit [#8068](https://github.com/deepflowio/deepflow/pull/8068) by [rvql](https://github.com/rvql)
* fix: agent desensitizes the content in backticks [#8060](https://github.com/deepflowio/deepflow/pull/8060) by [TomatoMr](https://github.com/TomatoMr)
* fix: add timeout to receive agent command heart beat [#8050](https://github.com/deepflowio/deepflow/pull/8050) by [roryye](https://github.com/roryye)
* fix: OffCpuProfile adds authorization control [#8064](https://github.com/deepflowio/deepflow/pull/8064) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: target port associated to controller [#8056](https://github.com/deepflowio/deepflow/pull/8056) by [askyrie](https://github.com/askyrie)
* fix: agent - eBPF Fix the issue with loading '__sys_recvmmsg' (#8052) [#8055](https://github.com/deepflowio/deepflow/pull/8055) by [yinjiping](https://github.com/yinjiping)
* fix: Fix typos [#8027](https://github.com/deepflowio/deepflow/pull/8027) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: prom query error [#8046](https://github.com/deepflowio/deepflow/pull/8046) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: agent - eBPF fentry/fexit Check for '__sys_recvmmsg' [#8014](https://github.com/deepflowio/deepflow/pull/8014) by [yinjiping](https://github.com/yinjiping)
* fix: resource label errors for non-default organization resource change events [#8008](https://github.com/deepflowio/deepflow/pull/8008) by [lzf575](https://github.com/lzf575)
* fix: controller records unnecessary error logs [#7995](https://github.com/deepflowio/deepflow/pull/7995) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: querier converts types when using dictget [#7997](https://github.com/deepflowio/deepflow/pull/7997) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: if using ByConity database storage, no need to deal with _local tables [#7993](https://github.com/deepflowio/deepflow/pull/7993) by [lzf575](https://github.com/lzf575)
* fix: aarch64 musl compilation [#7992](https://github.com/deepflowio/deepflow/pull/7992) by [rvql](https://github.com/rvql)
* fix: Agent configuration template [#7989](https://github.com/deepflowio/deepflow/pull/7989) by [rvql](https://github.com/rvql)
* fix: add log to agent remote commands [#7987](https://github.com/deepflowio/deepflow/pull/7987) by [roryye](https://github.com/roryye)
* fix: fixes recorder cache log level error [#7970](https://github.com/deepflowio/deepflow/pull/7970) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: end_point is null [#7966](https://github.com/deepflowio/deepflow/pull/7966) by [incoffeemonster](https://github.com/incoffeemonster)
* fix: optimized transtype function [#7875](https://github.com/deepflowio/deepflow/pull/7875) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: failed to initialize mysql during new deployment [#7982](https://github.com/deepflowio/deepflow/pull/7982) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: add libpcap to deb package [#7979](https://github.com/deepflowio/deepflow/pull/7979) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent - eBPF Remove the handling of redundant process events [#7977](https://github.com/deepflowio/deepflow/pull/7977) by [yinjiping](https://github.com/yinjiping)
* fix: Non-PIC executable DWARF entry problem [#7971](https://github.com/deepflowio/deepflow/pull/7971) by [rvql](https://github.com/rvql)
* fix: delete agent group [#7962](https://github.com/deepflowio/deepflow/pull/7962) by [roryye](https://github.com/roryye)
* fix: agent - eBPF Modify memory barrier for ring [#7959](https://github.com/deepflowio/deepflow/pull/7959) by [yinjiping](https://github.com/yinjiping)
* fix: delete agent group [#7953](https://github.com/deepflowio/deepflow/pull/7953) by [roryye](https://github.com/roryye)
* fix: agent registration followGroupFeatures is all features [#7949](https://github.com/deepflowio/deepflow/pull/7949) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: license_func_log sql [#7943](https://github.com/deepflowio/deepflow/pull/7943) by [roryye](https://github.com/roryye)
* fix: Fix DWARF unwind entry for non PIE binaries [#7936](https://github.com/deepflowio/deepflow/pull/7936) by [rvql](https://github.com/rvql)
* fix: Server config name mismatch [#7934](https://github.com/deepflowio/deepflow/pull/7934) by [rvql](https://github.com/rvql)
* fix: log contents [#7920](https://github.com/deepflowio/deepflow/pull/7920) by [incoffeemonster](https://github.com/incoffeemonster)
* fix: Profiler tries to clean non-exist map [#7905](https://github.com/deepflowio/deepflow/pull/7905) by [rvql](https://github.com/rvql)
* fix: nil debug panic [#7904](https://github.com/deepflowio/deepflow/pull/7904) by [duandaa](https://github.com/duandaa)
* fix: uses wrong host when request server http service [#7902](https://github.com/deepflowio/deepflow/pull/7902) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: Fix windows compile [#7897](https://github.com/deepflowio/deepflow/pull/7897) by [rvql](https://github.com/rvql)
* fix: abnormal stats data, causing deepflow-server to panic [#7894](https://github.com/deepflowio/deepflow/pull/7894) by [lzf575](https://github.com/lzf575)
* fix: fixes invalid controller formatted logs [#7890](https://github.com/deepflowio/deepflow/pull/7890) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: incorrect fast path map size [#7888](https://github.com/deepflowio/deepflow/pull/7888) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: Modify alert_event tag type error #26069 [#7886](https://github.com/deepflowio/deepflow/pull/7886) by [Ericsssss](https://github.com/Ericsssss)
* fix: Log analysis content [#7878](https://github.com/deepflowio/deepflow/pull/7878) by [incoffeemonster](https://github.com/incoffeemonster)
* fix: Modify array tag filter zero check #26069 [#7853](https://github.com/deepflowio/deepflow/pull/7853) by [Ericsssss](https://github.com/Ericsssss)
* fix: skip check when getting icon fails [#7849](https://github.com/deepflowio/deepflow/pull/7849) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: Add libpcap dependency to installation package [#7833](https://github.com/deepflowio/deepflow/pull/7833) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: service data is not written to ck [#7832](https://github.com/deepflowio/deepflow/pull/7832) by [duandaa](https://github.com/duandaa)
* fix: fix agent rebalance panic [#7831](https://github.com/deepflowio/deepflow/pull/7831) by [roryye](https://github.com/roryye)
* fix: Modify alert_event enum tag filter error #26069 [#7828](https://github.com/deepflowio/deepflow/pull/7828) by [Ericsssss](https://github.com/Ericsssss)
* fix: fix the problem of config pointer copy [#7825](https://github.com/deepflowio/deepflow/pull/7825) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: Modify alert_event _id filter error #26055 [#7818](https://github.com/deepflowio/deepflow/pull/7818) by [Ericsssss](https://github.com/Ericsssss)
* fix: Increase the Tars protocol number in the Server section [#7813](https://github.com/deepflowio/deepflow/pull/7813) by [incoffeemonster](https://github.com/incoffeemonster)
* fix: Modify alert_event auto_service/auto_instance error and showtagv… [#7807](https://github.com/deepflowio/deepflow/pull/7807) by [Ericsssss](https://github.com/Ericsssss)
* fix: turn off the call monitoring function and stop related functions [#7805](https://github.com/deepflowio/deepflow/pull/7805) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: agent enable feature permission [#7794](https://github.com/deepflowio/deepflow/pull/7794) by [roryye](https://github.com/roryye)
* Revert "fix: refresh agent enable feature" [#7792](https://github.com/deepflowio/deepflow/pull/7792) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: refresh agent enable feature [#7790](https://github.com/deepflowio/deepflow/pull/7790) by [roryye](https://github.com/roryye)
* fix: concurrent map read and map write [#7776](https://github.com/deepflowio/deepflow/pull/7776) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: recorder log fmt error [#7782](https://github.com/deepflowio/deepflow/pull/7782) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: Modify ch_table update time #26153 [#7777](https://github.com/deepflowio/deepflow/pull/7777) by [Ericsssss](https://github.com/Ericsssss)
* fix: server allows empty values not to be overwritten as default values [#7775](https://github.com/deepflowio/deepflow/pull/7775) by [TomatoMr](https://github.com/TomatoMr)
* fix: resolve show tag values filter error [#7774](https://github.com/deepflowio/deepflow/pull/7774) by [duandaa](https://github.com/duandaa)
* fix: profileDebug assert error [#7760](https://github.com/deepflowio/deepflow/pull/7760) by [duandaa](https://github.com/duandaa)
* fix: not found replicaset pod [#7762](https://github.com/deepflowio/deepflow/pull/7762) by [askyrie](https://github.com/askyrie)
* fix: Modify alert_policy filter [#7757](https://github.com/deepflowio/deepflow/pull/7757) by [Ericsssss](https://github.com/Ericsssss)
* fix: traffic.go log [#7738](https://github.com/deepflowio/deepflow/pull/7738) by [roryye](https://github.com/roryye)
* fix: server fixes the problem that TapInterfaceRegex cannot be set to empty [#7735](https://github.com/deepflowio/deepflow/pull/7735) by [TomatoMr](https://github.com/TomatoMr)
* fix: Fix enum filter error [#7724](https://github.com/deepflowio/deepflow/pull/7724) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: Modify alert_event tag category #26069 [#7676](https://github.com/deepflowio/deepflow/pull/7676) by [Ericsssss](https://github.com/Ericsssss)
* fix: ntp support profile data [#7704](https://github.com/deepflowio/deepflow/pull/7704) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent fixes empty endpoint when setting http-endpoint-extraction.match-rules.prefix: "" [#7698](https://github.com/deepflowio/deepflow/pull/7698) by [TomatoMr](https://github.com/TomatoMr)
* fix: modify log format [#7690](https://github.com/deepflowio/deepflow/pull/7690) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: Memory leak in stack frame building [#7689](https://github.com/deepflowio/deepflow/pull/7689) by [rvql](https://github.com/rvql)
* fix: org log record [#7688](https://github.com/deepflowio/deepflow/pull/7688) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: agent remote exec panic [#7685](https://github.com/deepflowio/deepflow/pull/7685) by [roryye](https://github.com/roryye)
* fix: limit number of agent sync [#7682](https://github.com/deepflowio/deepflow/pull/7682) by [askyrie](https://github.com/askyrie)
* fix: Modify show tags API error #26063 [#7681](https://github.com/deepflowio/deepflow/pull/7681) by [Ericsssss](https://github.com/Ericsssss)
* fix: Translate metrics by table [#7677](https://github.com/deepflowio/deepflow/pull/7677) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: org log record [#7674](https://github.com/deepflowio/deepflow/pull/7674) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: in flow tag, field value type of int value should be `int` [#7671](https://github.com/deepflowio/deepflow/pull/7671) by [lzf575](https://github.com/lzf575)
* fix: restart agent after enabling ntp [#7661](https://github.com/deepflowio/deepflow/pull/7661) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent Redis cannot close obfuscation [#7653](https://github.com/deepflowio/deepflow/pull/7653) by [TomatoMr](https://github.com/TomatoMr)
* fix: need to drop deepflow_tenant/admin database when drop an organization [#7648](https://github.com/deepflowio/deepflow/pull/7648) by [lzf575](https://github.com/lzf575)
* fix: Modify select metric_value error #26036 [#7646](https://github.com/deepflowio/deepflow/pull/7646) by [Ericsssss](https://github.com/Ericsssss)
* fix: agent - eBPF Fix Kafka protocol inference (#7644) [#7645](https://github.com/deepflowio/deepflow/pull/7645) by [yinjiping](https://github.com/yinjiping)
* fix: Modify alert_event metric_value and add ip tag  #26017/26020 [#7637](https://github.com/deepflowio/deepflow/pull/7637) by [Ericsssss](https://github.com/Ericsssss)
* fix: agent cmd panic [#7631](https://github.com/deepflowio/deepflow/pull/7631) by [roryye](https://github.com/roryye)
* fix: Modify custom tag show tags error #25979 [#7628](https://github.com/deepflowio/deepflow/pull/7628) by [Ericsssss](https://github.com/Ericsssss)
* fix: Translate fields by table [#7627](https://github.com/deepflowio/deepflow/pull/7627) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: fix agent traffic log [#7620](https://github.com/deepflowio/deepflow/pull/7620) by [roryye](https://github.com/roryye)
* fix: Memory counter error [#7619](https://github.com/deepflowio/deepflow/pull/7619) by [rvql](https://github.com/rvql)
* fix: Fix off-by-one error in java symbol rewrite [#7613](https://github.com/deepflowio/deepflow/pull/7613) by [rvql](https://github.com/rvql)
* fix: Set 0 for e_stack_id [#7612](https://github.com/deepflowio/deepflow/pull/7612) by [rvql](https://github.com/rvql)
* fix: Fix duplicate key for different java classes [#7611](https://github.com/deepflowio/deepflow/pull/7611) by [rvql](https://github.com/rvql)
* fix: Modify alert_policy show tag values error #25978 [#7599](https://github.com/deepflowio/deepflow/pull/7599) by [Ericsssss](https://github.com/Ericsssss)
* fix: modify resource delete error #25844 [#7552](https://github.com/deepflowio/deepflow/pull/7552) by [Ericsssss](https://github.com/Ericsssss)
* fix: CVE-2024-35195 vuln [#7590](https://github.com/deepflowio/deepflow/pull/7590) by [fengshunli](https://github.com/fengshunli)
* fix: agent - windows compile error [#7588](https://github.com/deepflowio/deepflow/pull/7588) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent - missing cbpf l7 log [#7587](https://github.com/deepflowio/deepflow/pull/7587) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent - eBPF Modify the whitelist restructuring format (#7580) [#7581](https://github.com/deepflowio/deepflow/pull/7581) by [yinjiping](https://github.com/yinjiping)
* fix: appp_service should be app_service [#7569](https://github.com/deepflowio/deepflow/pull/7569) by [lzf575](https://github.com/lzf575)
* fix: data under all organizations should be cleared when disk is full [#7565](https://github.com/deepflowio/deepflow/pull/7565) by [lzf575](https://github.com/lzf575)
* fix: do not pub to tagrecorder [#7563](https://github.com/deepflowio/deepflow/pull/7563) by [askyrie](https://github.com/askyrie)
* fix: add agent cmd timeout to config [#7536](https://github.com/deepflowio/deepflow/pull/7536) by [roryye](https://github.com/roryye)
* fix: Java uprobe regex not configured [#7532](https://github.com/deepflowio/deepflow/pull/7532) by [rvql](https://github.com/rvql)
* fix: agent fix check_params failed when param is empty [#7509](https://github.com/deepflowio/deepflow/pull/7509) by [TomatoMr](https://github.com/TomatoMr)
* fix: Do not verify dynamic tags [#7504](https://github.com/deepflowio/deepflow/pull/7504) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: filter agent command by type name [#7501](https://github.com/deepflowio/deepflow/pull/7501) by [roryye](https://github.com/roryye)
* fix: agent - eBPF Continuous Java profiling support for linux 3.10 [#7486](https://github.com/deepflowio/deepflow/pull/7486) by [yinjiping](https://github.com/yinjiping)
* fix: update agent commands permission [#7480](https://github.com/deepflowio/deepflow/pull/7480) by [roryye](https://github.com/roryye)
* fix: Delete an alarm policy #25866 [#7478](https://github.com/deepflowio/deepflow/pull/7478) by [Ericsssss](https://github.com/Ericsssss)
* fix: solve the problem that sql is changed to lowercase [#7475](https://github.com/deepflowio/deepflow/pull/7475) by [duandaa](https://github.com/duandaa)
* fix: recorder reports wrong sub_domain infomation in domain logs [#7441](https://github.com/deepflowio/deepflow/pull/7441) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: unable to delete sub_domain related resource [#7439](https://github.com/deepflowio/deepflow/pull/7439) by [SongZhen0704](https://github.com/SongZhen0704)
* fix: Fix the problem of duplicate fields in select [#7434](https://github.com/deepflowio/deepflow/pull/7434) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: Fix concurrent request command for the same agent [#7410](https://github.com/deepflowio/deepflow/pull/7410) by [roryye](https://github.com/roryye)
* fix: Resolve cover show metrics use query cache can be configured problem [#7405](https://github.com/deepflowio/deepflow/pull/7405) by [duandaa](https://github.com/duandaa)
* fix: lo interface eBPF data needs to use Agent info to match resources [#7404](https://github.com/deepflowio/deepflow/pull/7404) by [lzf575](https://github.com/lzf575)
* fix: deepflow-server may panic when updating platform information [#7400](https://github.com/deepflowio/deepflow/pull/7400) by [lzf575](https://github.com/lzf575)
* fix: Add return processing to agent command [#7396](https://github.com/deepflowio/deepflow/pull/7396) by [roryye](https://github.com/roryye)
* fix: Fix windows compilation [#7389](https://github.com/deepflowio/deepflow/pull/7389) by [rvql](https://github.com/rvql)
* fix: handle agent command content when error occurs [#7381](https://github.com/deepflowio/deepflow/pull/7381) by [roryye](https://github.com/roryye)
* fix: Optimized error message #25736 [#7380](https://github.com/deepflowio/deepflow/pull/7380) by [Ericsssss](https://github.com/Ericsssss)
* fix: CE does not involve organization-related data [#7373](https://github.com/deepflowio/deepflow/pull/7373) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: agent wrong desensitized mysql trace_id [#7365](https://github.com/deepflowio/deepflow/pull/7365) by [TomatoMr](https://github.com/TomatoMr)
* fix: Add mysql error log to monitor module [#7357](https://github.com/deepflowio/deepflow/pull/7357) by [roryye](https://github.com/roryye)
* fix: agent - windows compile error [#7360](https://github.com/deepflowio/deepflow/pull/7360) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent - vm mac address not updated [#7355](https://github.com/deepflowio/deepflow/pull/7355) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: modify system alarm_policy filter conditions and tag_conditions [#7351](https://github.com/deepflowio/deepflow/pull/7351) by [Ericsssss](https://github.com/Ericsssss)
* fix: update member type of rawtracemap [#7349](https://github.com/deepflowio/deepflow/pull/7349) by [taloric](https://github.com/taloric)
* fix: agent - incorrect grpc ebpf tcp seq [#7333](https://github.com/deepflowio/deepflow/pull/7333) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: Remove useless output from CLI [#7331](https://github.com/deepflowio/deepflow/pull/7331) by [roryye](https://github.com/roryye)
* fix: agent - missing mysql log [#7323](https://github.com/deepflowio/deepflow/pull/7323) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: if OrgID is set, deepflow-server stats should be stored in deepflow_tenant [#7321](https://github.com/deepflowio/deepflow/pull/7321) by [lzf575](https://github.com/lzf575)
* fix: manually created ips are deleted unexpectedly [#7312](https://github.com/deepflowio/deepflow/pull/7312) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: filter invalid sql and fix debug return result [#7308](https://github.com/deepflowio/deepflow/pull/7308) by [duandaa](https://github.com/duandaa)
* fix: Fix missing pod interface in macvlan mode [#7315](https://github.com/deepflowio/deepflow/pull/7315) by [rvql](https://github.com/rvql)
* fix: agent - incorrect http2 log [#7296](https://github.com/deepflowio/deepflow/pull/7296) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: the flow_tag of prometheus may be lost under multiple organizations [#7294](https://github.com/deepflowio/deepflow/pull/7294) by [lzf575](https://github.com/lzf575)
* fix: agent - eBPF Adjust Java syms-cache update logic & error log output [#7291](https://github.com/deepflowio/deepflow/pull/7291) by [yinjiping](https://github.com/yinjiping)
* fix: deepflow stats may write to wrong database [#7284](https://github.com/deepflowio/deepflow/pull/7284) by [lzf575](https://github.com/lzf575)
* fix: agent - eBPF Addressing excessive eBPF maps memory usage [#7281](https://github.com/deepflowio/deepflow/pull/7281) by [yinjiping](https://github.com/yinjiping)
* fix: agent - remove duplicate vhost dispatcher [#7267](https://github.com/deepflowio/deepflow/pull/7267) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: Show metrics use query cache can be configured [#7263](https://github.com/deepflowio/deepflow/pull/7263) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: server recorder prints unnecessary error logs [#7262](https://github.com/deepflowio/deepflow/pull/7262) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Add fixed id for remote execution commands [#7255](https://github.com/deepflowio/deepflow/pull/7255) by [rvql](https://github.com/rvql)
* fix: Network interface card list supports duplicate mac [#7251](https://github.com/deepflowio/deepflow/pull/7251) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: agent - windows compilation errors [#7243](https://github.com/deepflowio/deepflow/pull/7243) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: uses long connections to connect to CK for datasources manager [#7239](https://github.com/deepflowio/deepflow/pull/7239) by [lzf575](https://github.com/lzf575)
* fix: server recorder prints unnecessary error logs [#7190](https://github.com/deepflowio/deepflow/pull/7190) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: Attribute supports Chinese [#7210](https://github.com/deepflowio/deepflow/pull/7210) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: agent Packet fanout can only be set in TapMode::Local mode [#7205](https://github.com/deepflowio/deepflow/pull/7205) by [TomatoMr](https://github.com/TomatoMr)
* fix: agent - eBPF DNS cannot obtain network tuple data (#7131) [#7132](https://github.com/deepflowio/deepflow/pull/7132) by [yinjiping](https://github.com/yinjiping)
* fix: Ingester always update prometheus labels even if labels version has not changed [#7128](https://github.com/deepflowio/deepflow/pull/7128) by [lzf575](https://github.com/lzf575)
* fix: k8s refresh close keep alive [#7125](https://github.com/deepflowio/deepflow/pull/7125) by [askyrie](https://github.com/askyrie)
* fix: server controller changes prometheus label version when data doe… [#7116](https://github.com/deepflowio/deepflow/pull/7116) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: agent - eBPF Enhance Mongo/SOFARPC/MySQL/HTTP2 proto-infer(#7110) [#7113](https://github.com/deepflowio/deepflow/pull/7113) by [yinjiping](https://github.com/yinjiping)
* fix: agent - add sleep before exiting [#7111](https://github.com/deepflowio/deepflow/pull/7111) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: agent - incorrect grpc log collected by uprobe [#7199](https://github.com/deepflowio/deepflow/pull/7199) by [yuanchaoa](https://github.com/yuanchaoa)
* fix: getting wrong org-id and team-id when the Agent version is less than v6.5.9 [#7188](https://github.com/deepflowio/deepflow/pull/7188) by [lzf575](https://github.com/lzf575)
* fix: when modifying the TTL of the CK table fails, the connection to CK needs to be closed [#7185](https://github.com/deepflowio/deepflow/pull/7185) by [lzf575](https://github.com/lzf575)
* fix: Modify the value of team's short lcuuid corresponding to org_id [#7177](https://github.com/deepflowio/deepflow/pull/7177) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: Only the default organization registers with tsdb [#7166](https://github.com/deepflowio/deepflow/pull/7166) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* fix: agent - eBPF Adjust syscall sendto() for IPv6 mapping to IPv4 [#7161](https://github.com/deepflowio/deepflow/pull/7161) by [yinjiping](https://github.com/yinjiping)
* fix: genesis reponse nil pointer [#7157](https://github.com/deepflowio/deepflow/pull/7157) by [askyrie](https://github.com/askyrie)
* fix: agent sync ignore loopback ip [#7152](https://github.com/deepflowio/deepflow/pull/7152) by [askyrie](https://github.com/askyrie)
* fix: agent - eBPF Ensure the Sofa protocol can reassemble [#7151](https://github.com/deepflowio/deepflow/pull/7151) by [yinjiping](https://github.com/yinjiping)
* fix: server static config PacketFanoutMode [#7147](https://github.com/deepflowio/deepflow/pull/7147) by [TomatoMr](https://github.com/TomatoMr)
* fix: Repair collector network card list display incomplete [#7146](https://github.com/deepflowio/deepflow/pull/7146) by [xiaochaoren1](https://github.com/xiaochaoren1)
* fix: server removes redundant function codes to avoid log errors [#7144](https://github.com/deepflowio/deepflow/pull/7144) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* fix: agent - eBPF Resolve missing fork() syscall on arm64 [#7143](https://github.com/deepflowio/deepflow/pull/7143) by [yinjiping](https://github.com/yinjiping)
* Fix prometheus data cannot be labeled with universal tags，if slow-decoder is used. [#7100](https://github.com/deepflowio/deepflow/pull/7100)

#### NEW FEATURE
* feat: agent - eBPF Add a Musl flag option (#9022) [#9024](https://github.com/deepflowio/deepflow/pull/9024) by [yinjiping](https://github.com/yinjiping)
* feat: updatae tunnel decap feature [#9021](https://github.com/deepflowio/deepflow/pull/9021) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: agent - eBPF Adaptation for TLinux 4.14.105-19-0019 [#9013](https://github.com/deepflowio/deepflow/pull/9013) by [yinjiping](https://github.com/yinjiping)
* feat: agent - eBPF DPDK User-Space Packet Statistics [#8808](https://github.com/deepflowio/deepflow/pull/8808) by [yinjiping](https://github.com/yinjiping)
* feat: eBPF tars infer support [#8785](https://github.com/deepflowio/deepflow/pull/8785) by [rvql](https://github.com/rvql)
* feat: Enable agent after first guard check [#8750](https://github.com/deepflowio/deepflow/pull/8750) by [rvql](https://github.com/rvql)
* feat: update vtap ignore fields [#8748](https://github.com/deepflowio/deepflow/pull/8748) by [askyrie](https://github.com/askyrie)
* feat: adds health check warning [#8726](https://github.com/deepflowio/deepflow/pull/8726) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: deprecated ipvlan [#8723](https://github.com/deepflowio/deepflow/pull/8723) by [askyrie](https://github.com/askyrie)
* feat: gets by page when refreshing recorder cache [#8709](https://github.com/deepflowio/deepflow/pull/8709) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: querier optimization group [#8688](https://github.com/deepflowio/deepflow/pull/8688) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: agent - eBPF Optimize data push logic (#8653) [#8684](https://github.com/deepflowio/deepflow/pull/8684) by [yinjiping](https://github.com/yinjiping)
* feat: querier supports show enum tags by language [#8512](https://github.com/deepflowio/deepflow/pull/8512) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: sender add compress flag [#8639](https://github.com/deepflowio/deepflow/pull/8639) by [taloric](https://github.com/taloric)
* feat: adds resource synchronization delay alarms [#8612](https://github.com/deepflowio/deepflow/pull/8612) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: add resource association abnormal alarm [#8585](https://github.com/deepflowio/deepflow/pull/8585) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: optimize memory [#8641](https://github.com/deepflowio/deepflow/pull/8641) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: support fanout in mirror mode and analyzer mode [#8587](https://github.com/deepflowio/deepflow/pull/8587) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: register esxi vtap support cloudtower [#8599](https://github.com/deepflowio/deepflow/pull/8599) by [askyrie](https://github.com/askyrie)
* feat: querier add version check [#8235](https://github.com/deepflowio/deepflow/pull/8235) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feature: adds index to some tables [#8589](https://github.com/deepflowio/deepflow/pull/8589) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feature: adds system alarm of recorder cleaner [#8565](https://github.com/deepflowio/deepflow/pull/8565) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: agent - eBPF Supplement the DPDK configuration description [#8543](https://github.com/deepflowio/deepflow/pull/8543) by [yinjiping](https://github.com/yinjiping)
* feat: add offset attribute to IO event [#8537](https://github.com/deepflowio/deepflow/pull/8537) by [lzf575](https://github.com/lzf575)
* feat: Add offset in IoEvent [#8532](https://github.com/deepflowio/deepflow/pull/8532) by [rvql](https://github.com/rvql)
* feat: Change consts for offcpu dwarf/python unwind [#8525](https://github.com/deepflowio/deepflow/pull/8525) by [rvql](https://github.com/rvql)
* feat: agent - eBPF Add file offset for io r/w events [#8514](https://github.com/deepflowio/deepflow/pull/8514) by [yinjiping](https://github.com/yinjiping)
* feature: supports agent cmd api using agent.proto [#8513](https://github.com/deepflowio/deepflow/pull/8513) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: the role field adds the values local and rest [#8474](https://github.com/deepflowio/deepflow/pull/8474) by [lzf575](https://github.com/lzf575)
* feat: agent - eBPF Support python unwind [#8407](https://github.com/deepflowio/deepflow/pull/8407) by [rvql](https://github.com/rvql)
* feat: single-endpoints db support storing packet where the tap_side i… [#8468](https://github.com/deepflowio/deepflow/pull/8468) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: agent - eBPF Adapt to 4.19.90-2211.5.0.0178.22.uel20.x86_64 [#8461](https://github.com/deepflowio/deepflow/pull/8461) by [yinjiping](https://github.com/yinjiping)
* feat: Enable agent new rpc by default [#8455](https://github.com/deepflowio/deepflow/pull/8455) by [rvql](https://github.com/rvql)
* feat: update trisolaris dynamic config [#8450](https://github.com/deepflowio/deepflow/pull/8450) by [askyrie](https://github.com/askyrie)
* feat: Enable agent new rpc by default [#8449](https://github.com/deepflowio/deepflow/pull/8449) by [rvql](https://github.com/rvql)
* feat: agent - eBPF Add DPDK packet capture [#8415](https://github.com/deepflowio/deepflow/pull/8415) by [yinjiping](https://github.com/yinjiping)
* feat: add new agent.proto func [#8112](https://github.com/deepflowio/deepflow/pull/8112) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: agent - eBPF Support for the ARM version of Kylin v10 SP2 [#8439](https://github.com/deepflowio/deepflow/pull/8439) by [yinjiping](https://github.com/yinjiping)
* feat: Change bpf map feat to feat_flags to support multi-function [#8424](https://github.com/deepflowio/deepflow/pull/8424) by [rvql](https://github.com/rvql)
* feat: modify agent.proto [#8416](https://github.com/deepflowio/deepflow/pull/8416) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: querier add query cache config [#8404](https://github.com/deepflowio/deepflow/pull/8404) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: querier optimize ip filter [#8343](https://github.com/deepflowio/deepflow/pull/8343) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: modify the epc of multicast address [#8336](https://github.com/deepflowio/deepflow/pull/8336) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: move zmq to plugins [#8327](https://github.com/deepflowio/deepflow/pull/8327) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: add ci to check dynamic libs [#8326](https://github.com/deepflowio/deepflow/pull/8326) by [jiumos](https://github.com/jiumos)
* feat: agent - eBPF Profile support RT kernel [#8324](https://github.com/deepflowio/deepflow/pull/8324) by [yinjiping](https://github.com/yinjiping)
* feat: sort prometheus export tags [#8328](https://github.com/deepflowio/deepflow/pull/8328) by [lzf575](https://github.com/lzf575)
* feat: agent - eBPF Support RT kernel (#8301) [#8304](https://github.com/deepflowio/deepflow/pull/8304) by [yinjiping](https://github.com/yinjiping)
* feat: add zeromq to npb sender [#8297](https://github.com/deepflowio/deepflow/pull/8297) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: Memcached ebpf support [#8296](https://github.com/deepflowio/deepflow/pull/8296) by [rvql](https://github.com/rvql)
* feat: agent - eBPF Adjust log output for get_process_starttime_and_comm [#8293](https://github.com/deepflowio/deepflow/pull/8293) by [yinjiping](https://github.com/yinjiping)
* feat: agent - eBPF Keep the agent running after initialization [#8287](https://github.com/deepflowio/deepflow/pull/8287) by [yinjiping](https://github.com/yinjiping)
* feat: support skywalking integration [#8286](https://github.com/deepflowio/deepflow/pull/8286) by [taloric](https://github.com/taloric)
* feat: exist function supports non-resource tags [#8255](https://github.com/deepflowio/deepflow/pull/8255) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: add process listener [#8211](https://github.com/deepflowio/deepflow/pull/8211) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: agent - eBPF Adapt 5.10.204-rt100-AD1000-PROTO [#8246](https://github.com/deepflowio/deepflow/pull/8246) by [yinjiping](https://github.com/yinjiping)
* feat: agent - Add a configuration option to enable go/tls trace [#8244](https://github.com/deepflowio/deepflow/pull/8244) by [yinjiping](https://github.com/yinjiping)
* feat: agent - eBPF Create maps based on functional configuration [#8243](https://github.com/deepflowio/deepflow/pull/8243) by [yinjiping](https://github.com/yinjiping)
* feat: agent - eBPF Add the BPF_F_NO_PREALLOC option to the hash map [#8236](https://github.com/deepflowio/deepflow/pull/8236) by [yinjiping](https://github.com/yinjiping)
* feat: querier uniq function supports multi params [#8218](https://github.com/deepflowio/deepflow/pull/8218) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: the application log table uses app_service as the primary key [#8220](https://github.com/deepflowio/deepflow/pull/8220) by [lzf575](https://github.com/lzf575)
* feat: support k8s_watch_policy env [#8219](https://github.com/deepflowio/deepflow/pull/8219) by [SongZhen0704](https://github.com/SongZhen0704)
* feat: tracemap supports multiple regions [#8075](https://github.com/deepflowio/deepflow/pull/8075) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Support K8S_WATCH_POLICY env [#8210](https://github.com/deepflowio/deepflow/pull/8210) by [rvql](https://github.com/rvql)
* feat: trace tree support multi regions [#8209](https://github.com/deepflowio/deepflow/pull/8209) by [taloric](https://github.com/taloric)
* feat: Modify default l7 protocol enable [#8198](https://github.com/deepflowio/deepflow/pull/8198) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: reduce the application protocols parsed by default [#8196](https://github.com/deepflowio/deepflow/pull/8196) by [sharang](https://github.com/sharang)
* feat: default genesis sync vpc name [#8192](https://github.com/deepflowio/deepflow/pull/8192) by [askyrie](https://github.com/askyrie)
* feat: update cloud region filter [#8105](https://github.com/deepflowio/deepflow/pull/8105) by [askyrie](https://github.com/askyrie)
* feat: agent - Retain the profiler_regex interface [#8150](https://github.com/deepflowio/deepflow/pull/8150) by [yinjiping](https://github.com/yinjiping)
* feat: agent - eBPF Unified Process Matching Interface [#8033](https://github.com/deepflowio/deepflow/pull/8033) by [yinjiping](https://github.com/yinjiping)
* feat: update genesis data store [#7964](https://github.com/deepflowio/deepflow/pull/7964) by [askyrie](https://github.com/askyrie)
* feat: support free OS memory at intervals [#8091](https://github.com/deepflowio/deepflow/pull/8091) by [lzf575](https://github.com/lzf575)
* feat: support profile data compressed by agent [#8088](https://github.com/deepflowio/deepflow/pull/8088) by [taloric](https://github.com/taloric)
* feat: agent - eBPF Optimize interface 'java_syms_update_main' [#8102](https://github.com/deepflowio/deepflow/pull/8102) by [yinjiping](https://github.com/yinjiping)
* feat: support profile data compression when sending [#8094](https://github.com/deepflowio/deepflow/pull/8094) by [lzf575](https://github.com/lzf575)
* feat: Allow changing memory profile report interval [#8076](https://github.com/deepflowio/deepflow/pull/8076) by [rvql](https://github.com/rvql)
* feat: Rust memory profile support [#8049](https://github.com/deepflowio/deepflow/pull/8049) by [rvql](https://github.com/rvql)
* feat: reduce clickhouse connection memory [#8045](https://github.com/deepflowio/deepflow/pull/8045) by [lzf575](https://github.com/lzf575)
* feat: add tags and metrics visibility [#7985](https://github.com/deepflowio/deepflow/pull/7985) by [duandaa](https://github.com/duandaa)
* feat: gather svc add external ip [#8037](https://github.com/deepflowio/deepflow/pull/8037) by [askyrie](https://github.com/askyrie)
* feat: support multi target port [#8021](https://github.com/deepflowio/deepflow/pull/8021) by [askyrie](https://github.com/askyrie)
* feat: get metrics divided into static and dynamic [#7996](https://github.com/deepflowio/deepflow/pull/7996) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: generate agent.proto to grpc data [#8023](https://github.com/deepflowio/deepflow/pull/8023) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: querier converts types when using dictget [#8012](https://github.com/deepflowio/deepflow/pull/8012) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: switch ClickHouse endpoint without restart [#7969](https://github.com/deepflowio/deepflow/pull/7969) by [lzf575](https://github.com/lzf575)
* feat: add pod_cluster info in vtap api [#7984](https://github.com/deepflowio/deepflow/pull/7984) by [SongZhen0704](https://github.com/SongZhen0704)
* feat: Support configuring DWARF memory consumption [#7983](https://github.com/deepflowio/deepflow/pull/7983) by [rvql](https://github.com/rvql)
* feat: support tcp packet reassembly [#7939](https://github.com/deepflowio/deepflow/pull/7939) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: Show API support all enum tags and values [#7976](https://github.com/deepflowio/deepflow/pull/7976) by [Ericsssss](https://github.com/Ericsssss)
* feat: trace tree support store PseudoLink field [#7956](https://github.com/deepflowio/deepflow/pull/7956) by [lzf575](https://github.com/lzf575)
* feat: refactors mysql migrator [#7951](https://github.com/deepflowio/deepflow/pull/7951) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: describe instance support resource group [#7922](https://github.com/deepflowio/deepflow/pull/7922) by [askyrie](https://github.com/askyrie)
* feat: Support DWARF unwinding from kernel context [#7940](https://github.com/deepflowio/deepflow/pull/7940) by [rvql](https://github.com/rvql)
* feat: moves mysql models to independent directory [#7929](https://github.com/deepflowio/deepflow/pull/7929) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Add load_balancer in k8s Service/ServiceRule [#7933](https://github.com/deepflowio/deepflow/pull/7933) by [rvql](https://github.com/rvql)
* feat: service is issued to add loadbalancer [#7932](https://github.com/deepflowio/deepflow/pull/7932) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: agent - eBPF Add support for parsing 4.19.90-vhulk2211.3.0.h154… [#7930](https://github.com/deepflowio/deepflow/pull/7930) by [yinjiping](https://github.com/yinjiping)
* feat: k8s svc support lb [#7927](https://github.com/deepflowio/deepflow/pull/7927) by [askyrie](https://github.com/askyrie)
* feat: removes global variable mysql.Db [#7926](https://github.com/deepflowio/deepflow/pull/7926) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: add enable features to agent group [#7898](https://github.com/deepflowio/deepflow/pull/7898) by [roryye](https://github.com/roryye)
* feat: add pseudo link field for treenode [#7917](https://github.com/deepflowio/deepflow/pull/7917) by [taloric](https://github.com/taloric)
* feat: Update kubernetes create cli example [#7914](https://github.com/deepflowio/deepflow/pull/7914) by [SongZhen0704](https://github.com/SongZhen0704)
* feat: support countDistinct function [#7896](https://github.com/deepflowio/deepflow/pull/7896) by [duandaa](https://github.com/duandaa)
* feat: agent - eBPF Optimize eBPF socket trace [#7871](https://github.com/deepflowio/deepflow/pull/7871) by [yinjiping](https://github.com/yinjiping)
* feat: ch_vtap add resource [#7901](https://github.com/deepflowio/deepflow/pull/7901) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: when only exporting a single kafka data, use the 'sendMessage' interface [#7885](https://github.com/deepflowio/deepflow/pull/7885) by [lzf575](https://github.com/lzf575)
* feat: querier support simple sql [#7883](https://github.com/deepflowio/deepflow/pull/7883) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: update domain daily trigger [#7850](https://github.com/deepflowio/deepflow/pull/7850) by [askyrie](https://github.com/askyrie)
* feat: The interface sent to ingester deletes some fields [#7840](https://github.com/deepflowio/deepflow/pull/7840) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: DWARF unwind support [#7821](https://github.com/deepflowio/deepflow/pull/7821) by [rvql](https://github.com/rvql)
* feat: drop data which organization is not exist [#7877](https://github.com/deepflowio/deepflow/pull/7877) by [lzf575](https://github.com/lzf575)
* feat: supports non-real-time deletion of ck of deleted orgs [#7876](https://github.com/deepflowio/deepflow/pull/7876) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: optimize org data check and sync [#7870](https://github.com/deepflowio/deepflow/pull/7870) by [SongZhen0704](https://github.com/SongZhen0704)
* feat: change service chsql filter [#7842](https://github.com/deepflowio/deepflow/pull/7842) by [duandaa](https://github.com/duandaa)
* feat: service_type of auto_service_type change to pod_service_type [#7841](https://github.com/deepflowio/deepflow/pull/7841) by [lzf575](https://github.com/lzf575)
* feat: Modify policy_app_type value [#7822](https://github.com/deepflowio/deepflow/pull/7822) by [Ericsssss](https://github.com/Ericsssss)
* feat: add column in ck pod_node and pod_ingress [#7799](https://github.com/deepflowio/deepflow/pull/7799) by [duandaa](https://github.com/duandaa)
* feat: Using libpcap without restarting when the network card does not… [#7780](https://github.com/deepflowio/deepflow/pull/7780) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: add endpoint-tcp-port-name configuration [#7778](https://github.com/deepflowio/deepflow/pull/7778) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: use dynamic library instead of libpcap static library [#7772](https://github.com/deepflowio/deepflow/pull/7772) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: increase billing function control capabilities [#7714](https://github.com/deepflowio/deepflow/pull/7714) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: agent license func operate [#7712](https://github.com/deepflowio/deepflow/pull/7712) by [roryye](https://github.com/roryye)
* feat: support ByConity database [#7756](https://github.com/deepflowio/deepflow/pull/7756) by [lzf575](https://github.com/lzf575)
* feat: querier support byconity [#7753](https://github.com/deepflowio/deepflow/pull/7753) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: mac displays the resources to which the agent belongs [#7765](https://github.com/deepflowio/deepflow/pull/7765) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Ingester use the ORG log interface to write logs [#7763](https://github.com/deepflowio/deepflow/pull/7763) by [lzf575](https://github.com/lzf575)
* feat: support opengauss svc [#7755](https://github.com/deepflowio/deepflow/pull/7755) by [askyrie](https://github.com/askyrie)
* feat: add function_types in profile api response [#7754](https://github.com/deepflowio/deepflow/pull/7754) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: moves server controller logger module to libs [#7742](https://github.com/deepflowio/deepflow/pull/7742) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: add trace query delta for tracemap [#7747](https://github.com/deepflowio/deepflow/pull/7747) by [taloric](https://github.com/taloric)
* Revert "feat: agent - eBPF Modify the version number of the Java agent" [#7740](https://github.com/deepflowio/deepflow/pull/7740) by [yinjiping](https://github.com/yinjiping)
* feat: add auto_instace/auto_service field for profile [#7737](https://github.com/deepflowio/deepflow/pull/7737) by [lzf575](https://github.com/lzf575)
* feat: Support k8s opengauss cluster [#7736](https://github.com/deepflowio/deepflow/pull/7736) by [rvql](https://github.com/rvql)
* feat: agent - eBPF Modify the version number of the Java agent [#7733](https://github.com/deepflowio/deepflow/pull/7733) by [yinjiping](https://github.com/yinjiping)
* feat: agent - eBPF Java agent uses caching to reduce send frequency [#7730](https://github.com/deepflowio/deepflow/pull/7730) by [yinjiping](https://github.com/yinjiping)
* feat: Tars protocol parsing is supported [#7709](https://github.com/deepflowio/deepflow/pull/7709) by [incoffeemonster](https://github.com/incoffeemonster)
* feat: add pod_cluster_id in ch_pod_ingress and ch_pod_node table [#7683](https://github.com/deepflowio/deepflow/pull/7683) by [duandaa](https://github.com/duandaa)
* feat: change lua function name [#7723](https://github.com/deepflowio/deepflow/pull/7723) by [duandaa](https://github.com/duandaa)
* feat: kubernetes support OpenGaussCluster [#7722](https://github.com/deepflowio/deepflow/pull/7722) by [askyrie](https://github.com/askyrie)
* feat: agent - eBPF Adjust method for checking if agent is running [#7718](https://github.com/deepflowio/deepflow/pull/7718) by [yinjiping](https://github.com/yinjiping)
* feat: change lua function name [#7715](https://github.com/deepflowio/deepflow/pull/7715) by [duandaa](https://github.com/duandaa)
* feat: Ch_vtap_port support chost and pod_node [#7675](https://github.com/deepflowio/deepflow/pull/7675) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: add config multiple_sockets_to_ingester [#7668](https://github.com/deepflowio/deepflow/pull/7668) by [lzf575](https://github.com/lzf575)
* feat: call lua plugin [#7634](https://github.com/deepflowio/deepflow/pull/7634) by [duandaa](https://github.com/duandaa)
* feat: agent - eBPF Improve aggregation efficiency of stack-trace string [#7655](https://github.com/deepflowio/deepflow/pull/7655) by [yinjiping](https://github.com/yinjiping)
* feat: Table alert_event add tags and modify metric_value function error #26054 [#7652](https://github.com/deepflowio/deepflow/pull/7652) by [Ericsssss](https://github.com/Ericsssss)
* feat: agent add syscall_trace_id_disabled [#7547](https://github.com/deepflowio/deepflow/pull/7547) by [TomatoMr](https://github.com/TomatoMr)
* feat: domain info case sensitive [#7647](https://github.com/deepflowio/deepflow/pull/7647) by [askyrie](https://github.com/askyrie)
* feat: unifies controller manager log [#7643](https://github.com/deepflowio/deepflow/pull/7643) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: agent - eBPF Configurable to disable tracing [#7534](https://github.com/deepflowio/deepflow/pull/7534) by [yinjiping](https://github.com/yinjiping)
* feat: add request type from Kafka trace map [#7639](https://github.com/deepflowio/deepflow/pull/7639) by [lzf575](https://github.com/lzf575)
* feat: server - Add configuration items to server [#7629](https://github.com/deepflowio/deepflow/pull/7629) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: adds blocker module to logger [#7625](https://github.com/deepflowio/deepflow/pull/7625) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: limit number of agent_sync [#7614](https://github.com/deepflowio/deepflow/pull/7614) by [askyrie](https://github.com/askyrie)
* feat: agent - do not send time_span to server [#7610](https://github.com/deepflowio/deepflow/pull/7610) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: supports lua plugin [#7602](https://github.com/deepflowio/deepflow/pull/7602) by [duandaa](https://github.com/duandaa)
* feat: support default team in domain create api [#7622](https://github.com/deepflowio/deepflow/pull/7622) by [SongZhen0704](https://github.com/SongZhen0704)
* feat: Modify app_service and app_instance tag type and filter condition [#7621](https://github.com/deepflowio/deepflow/pull/7621) by [Ericsssss](https://github.com/Ericsssss)
* feat: unifies legacy log organization info [#7618](https://github.com/deepflowio/deepflow/pull/7618) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: log format add org [#7616](https://github.com/deepflowio/deepflow/pull/7616) by [askyrie](https://github.com/askyrie)
* feat: fill profile app_instance [#7609](https://github.com/deepflowio/deepflow/pull/7609) by [taloric](https://github.com/taloric)
* feat: agent - eBPF Add delay threshold check for push period [#7607](https://github.com/deepflowio/deepflow/pull/7607) by [yinjiping](https://github.com/yinjiping)
* feat: Tagrecorder log add org info [#7579](https://github.com/deepflowio/deepflow/pull/7579) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Allow longer java symbol name [#7603](https://github.com/deepflowio/deepflow/pull/7603) by [rvql](https://github.com/rvql)
* feat: Modify alert_policy node type [#7601](https://github.com/deepflowio/deepflow/pull/7601) by [Ericsssss](https://github.com/Ericsssss)
* feat: agent - eBPF Add kernel dependency check [#7594](https://github.com/deepflowio/deepflow/pull/7594) by [yinjiping](https://github.com/yinjiping)
* feat: delete invalid code for promethus target [#7574](https://github.com/deepflowio/deepflow/pull/7574) by [askyrie](https://github.com/askyrie)
* feat: agent - support consistent timestamp in l7metrics [#7561](https://github.com/deepflowio/deepflow/pull/7561) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: Profile support grafana [#7559](https://github.com/deepflowio/deepflow/pull/7559) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Use the new logging module [#7530](https://github.com/deepflowio/deepflow/pull/7530) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: update trace_tree struct [#7523](https://github.com/deepflowio/deepflow/pull/7523) by [lzf575](https://github.com/lzf575)
* feat: update log format [#7519](https://github.com/deepflowio/deepflow/pull/7519) by [askyrie](https://github.com/askyrie)
* feat: add cloud common for h3c [#7320](https://github.com/deepflowio/deepflow/pull/7320) by [askyrie](https://github.com/askyrie)
* feat: agent - support parallel protocols [#7582](https://github.com/deepflowio/deepflow/pull/7582) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: unifies http service log [#7578](https://github.com/deepflowio/deepflow/pull/7578) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Table alarm_event modify tag support oprerator [#7573](https://github.com/deepflowio/deepflow/pull/7573) by [Ericsssss](https://github.com/Ericsssss)
* feat: support write flow_tag service [#7550](https://github.com/deepflowio/deepflow/pull/7550) by [lzf575](https://github.com/lzf575)
* feat: unifies controller prometheus log [#7528](https://github.com/deepflowio/deepflow/pull/7528) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Table alert_event add tag [#7564](https://github.com/deepflowio/deepflow/pull/7564) by [Ericsssss](https://github.com/Ericsssss)
* feat: alert event add fields [#7558](https://github.com/deepflowio/deepflow/pull/7558) by [lzf575](https://github.com/lzf575)
* feat: change auto name  [#7557](https://github.com/deepflowio/deepflow/pull/7557) by [duandaa](https://github.com/duandaa)
* feat: modify alert_event user_filter condition [#7556](https://github.com/deepflowio/deepflow/pull/7556) by [Ericsssss](https://github.com/Ericsssss)
* feat: alert event add new filed [#7555](https://github.com/deepflowio/deepflow/pull/7555) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: alert event add _target_uid [#7549](https://github.com/deepflowio/deepflow/pull/7549) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: update data source collection event.alarm_event to event.alert_e… [#7539](https://github.com/deepflowio/deepflow/pull/7539) by [roryye](https://github.com/roryye)
* feat: change service translation [#7538](https://github.com/deepflowio/deepflow/pull/7538) by [duandaa](https://github.com/duandaa)
* feat: Tagrecorder support update team_id [#7543](https://github.com/deepflowio/deepflow/pull/7543) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Modify alert_event event_level tag translation [#7542](https://github.com/deepflowio/deepflow/pull/7542) by [Ericsssss](https://github.com/Ericsssss)
* feat: sub domain support update team [#7535](https://github.com/deepflowio/deepflow/pull/7535) by [askyrie](https://github.com/askyrie)
* feat: update profile value to u64 [#7531](https://github.com/deepflowio/deepflow/pull/7531) by [taloric](https://github.com/taloric)
* feat: update alert event field and write flow_tag [#7529](https://github.com/deepflowio/deepflow/pull/7529) by [lzf575](https://github.com/lzf575)
* feat: Modify sub_domain cluster_id description [#7527](https://github.com/deepflowio/deepflow/pull/7527) by [SongZhen0704](https://github.com/SongZhen0704)
* feat: unifies controller recorder log [#7515](https://github.com/deepflowio/deepflow/pull/7515) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Modify table alert_event query sql [#7476](https://github.com/deepflowio/deepflow/pull/7476) by [Ericsssss](https://github.com/Ericsssss)
* feat: Server exporters support tag-filter-condition config (#7492) [#7524](https://github.com/deepflowio/deepflow/pull/7524) by [lzf575](https://github.com/lzf575)
* feat: agent - eBPF Persist Java symbol table (#7325) [#7522](https://github.com/deepflowio/deepflow/pull/7522) by [yinjiping](https://github.com/yinjiping)
* feat: agent - Add description of mirror-traffic-pcp [#7520](https://github.com/deepflowio/deepflow/pull/7520) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: alert event add target_tags [#7517](https://github.com/deepflowio/deepflow/pull/7517) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: agent - support server port [#7514](https://github.com/deepflowio/deepflow/pull/7514) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: update create k8s check [#7513](https://github.com/deepflowio/deepflow/pull/7513) by [askyrie](https://github.com/askyrie)
* feat: add  debug_sql_length_max config [#7511](https://github.com/deepflowio/deepflow/pull/7511) by [duandaa](https://github.com/duandaa)
* feat: prevents dirty data from increasing MySQL IO [#7510](https://github.com/deepflowio/deepflow/pull/7510) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Support memory profile [#7506](https://github.com/deepflowio/deepflow/pull/7506) by [rvql](https://github.com/rvql)
* feat: Profile support mem-inuse [#7503](https://github.com/deepflowio/deepflow/pull/7503) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: refactors recorder pubsub ResourceUpdatedSubscriber interface [#7407](https://github.com/deepflowio/deepflow/pull/7407) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: server encapsulates logger to support flexible recording of org information. [#7280](https://github.com/deepflowio/deepflow/pull/7280) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Add gitee mirror [#7502](https://github.com/deepflowio/deepflow/pull/7502) by [Nick-0314](https://github.com/Nick-0314)
* feat: create k8s assign cluster id [#7415](https://github.com/deepflowio/deepflow/pull/7415) by [askyrie](https://github.com/askyrie)
* feat: agent - eBPF Remove tracing for HEAD type requests in NGINX [#7491](https://github.com/deepflowio/deepflow/pull/7491) by [yinjiping](https://github.com/yinjiping)
* feat: support alarm event updated to alert event [#7457](https://github.com/deepflowio/deepflow/pull/7457) by [lzf575](https://github.com/lzf575)
* feat: agent - support ingore vlan pcp [#7449](https://github.com/deepflowio/deepflow/pull/7449) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: Querier support no limit [#7473](https://github.com/deepflowio/deepflow/pull/7473) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: agent adds the CommandParam fields, modifies the param validation method [#7464](https://github.com/deepflowio/deepflow/pull/7464) by [TomatoMr](https://github.com/TomatoMr)
* feat: add agent command type probe [#7433](https://github.com/deepflowio/deepflow/pull/7433) by [roryye](https://github.com/roryye)
* feat: Alarm event field optimization [#7409](https://github.com/deepflowio/deepflow/pull/7409) by [jin-xiaofeng](https://github.com/jin-xiaofeng)
* feat: Get metrics ignore certain databases [#7427](https://github.com/deepflowio/deepflow/pull/7427) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Get metrics ignore time [#7425](https://github.com/deepflowio/deepflow/pull/7425) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: agent - dispatcher support set cpu affinity [#7424](https://github.com/deepflowio/deepflow/pull/7424) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: create sub domian set default team id [#7420](https://github.com/deepflowio/deepflow/pull/7420) by [askyrie](https://github.com/askyrie)
* feat: Optimize get metrics performance [#7419](https://github.com/deepflowio/deepflow/pull/7419) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: sub domain support filter user id [#7406](https://github.com/deepflowio/deepflow/pull/7406) by [askyrie](https://github.com/askyrie)
* feat: controller recorder updates updated_at field when soft deleted data is recreated [#7376](https://github.com/deepflowio/deepflow/pull/7376) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: controller supports syncronizing clickhouse data from proxysql [#7354](https://github.com/deepflowio/deepflow/pull/7354) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Modify tracemap url [#7391](https://github.com/deepflowio/deepflow/pull/7391) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Tracemap add header [#7386](https://github.com/deepflowio/deepflow/pull/7386) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Allow overriding remote exec cmdline with async function [#7383](https://github.com/deepflowio/deepflow/pull/7383) by [rvql](https://github.com/rvql)
* feat: tracemap add generator [#7379](https://github.com/deepflowio/deepflow/pull/7379) by [taloric](https://github.com/taloric)
* feat: removes redundant security group code [#7377](https://github.com/deepflowio/deepflow/pull/7377) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Improve tracemap api [#7367](https://github.com/deepflowio/deepflow/pull/7367) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: add querier shared write queue [#7361](https://github.com/deepflowio/deepflow/pull/7361) by [taloric](https://github.com/taloric)
* feat: sub domain support team [#7338](https://github.com/deepflowio/deepflow/pull/7338) by [askyrie](https://github.com/askyrie)
* feat: support trace_tree storage [#7313](https://github.com/deepflowio/deepflow/pull/7313) by [lzf575](https://github.com/lzf575)
* feat: agent - add inner queue to mirror dispatcher [#7352](https://github.com/deepflowio/deepflow/pull/7352) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: recorder supports updating vinterface device id [#7346](https://github.com/deepflowio/deepflow/pull/7346) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Agent remote exec returns specific errors [#7298](https://github.com/deepflowio/deepflow/pull/7298) by [roryye](https://github.com/roryye)
* feat: add raw trace map model [#7339](https://github.com/deepflowio/deepflow/pull/7339) by [taloric](https://github.com/taloric)
* feat: Return stderr for remote exec [#7337](https://github.com/deepflowio/deepflow/pull/7337) by [rvql](https://github.com/rvql)
* feat: Querier add tracemap [#7330](https://github.com/deepflowio/deepflow/pull/7330) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Update remote command param regex [#7326](https://github.com/deepflowio/deepflow/pull/7326) by [rvql](https://github.com/rvql)
* feat: agent - eBPF Whitelist implementation reassembly [#7316](https://github.com/deepflowio/deepflow/pull/7316) by [yinjiping](https://github.com/yinjiping)
* feat: Change remote exec param to use regex [#7314](https://github.com/deepflowio/deepflow/pull/7314) by [rvql](https://github.com/rvql)
* feat: mark the resource information of loopback traffic by Agent info [#7305](https://github.com/deepflowio/deepflow/pull/7305) by [lzf575](https://github.com/lzf575)
* feat: Add RemoteExec param spec and change cmd id type to string [#7287](https://github.com/deepflowio/deepflow/pull/7287) by [rvql](https://github.com/rvql)
* feat: add trace map router [#7299](https://github.com/deepflowio/deepflow/pull/7299) by [taloric](https://github.com/taloric)
* feat: server uses sub_domain team id first when publishing message to tagrecorder. [#7293](https://github.com/deepflowio/deepflow/pull/7293) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: agent opens the protocol desensitization by default [#7285](https://github.com/deepflowio/deepflow/pull/7285) by [TomatoMr](https://github.com/TomatoMr)
* feat: agent - flow&app log collected by lo nic do not report ctrl_mac [#7272](https://github.com/deepflowio/deepflow/pull/7272) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: querier - Rewriting ParseShowSql with Regular Expressions [#7268](https://github.com/deepflowio/deepflow/pull/7268) by [duandaa](https://github.com/duandaa)
* feat: Support kubernetes api field_selector [#7248](https://github.com/deepflowio/deepflow/pull/7248) by [rvql](https://github.com/rvql)
* feat: add debug ctl to rebalance agent by traffic [#7184](https://github.com/deepflowio/deepflow/pull/7184) by [roryye](https://github.com/roryye)
* feat: agent - eBPF Add JAVA symbol file generation log [#7258](https://github.com/deepflowio/deepflow/pull/7258) by [yinjiping](https://github.com/yinjiping)
* feat: revert - Rewriting ParseShowSql with Regular Expressions [#7252](https://github.com/deepflowio/deepflow/pull/7252) by [duandaa](https://github.com/duandaa)
* feat: querier - Rewriting ParseShowSql with Regular Expressions [#7181](https://github.com/deepflowio/deepflow/pull/7181) by [duandaa](https://github.com/duandaa)
* feat: server adds mysql conns configs [#7139](https://github.com/deepflowio/deepflow/pull/7139) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* feat: Add command for java stack dump [#7226](https://github.com/deepflowio/deepflow/pull/7226) by [rvql](https://github.com/rvql)
* feat: Add volcengine icon const [#7204](https://github.com/deepflowio/deepflow/pull/7204) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: Added automatic update changlog action [#7135](https://github.com/deepflowio/deepflow/pull/7135) by [Nick-0314](https://github.com/Nick-0314)
* feat: Profile adjust decompression order [#7122](https://github.com/deepflowio/deepflow/pull/7122) by [xiaochaoren1](https://github.com/xiaochaoren1)
* feat: CK’s username and password support the use of special characters [#7119](https://github.com/deepflowio/deepflow/pull/7119) by [lzf575](https://github.com/lzf575)
* feat: add volcengine icon const [#7179](https://github.com/deepflowio/deepflow/pull/7179) by [askyrie](https://github.com/askyrie)
* feat: agent - support vhost user [#7164](https://github.com/deepflowio/deepflow/pull/7164) by [yuanchaoa](https://github.com/yuanchaoa)
* feat: Alarm_policy queue.metrics.overwritten and ingester.queue.metri… [#7173](https://github.com/deepflowio/deepflow/pull/7173) by [Ericsssss](https://github.com/Ericsssss)
* feat: Modify system alarm policy query_conditions columns [#7171](https://github.com/deepflowio/deepflow/pull/7171) by [Ericsssss](https://github.com/Ericsssss)
* feat: OTel’s HTTP protocol parsing optimization in l7_flow_log [#7136](https://github.com/deepflowio/deepflow/pull/7136) by [lzf575](https://github.com/lzf575)
* feat: deepflow-ctl ingester support debugging by org id [#7133](https://github.com/deepflowio/deepflow/pull/7133) by [lzf575](https://github.com/lzf575)
* feat: agent support setting PACKET_FANOUT [#7126](https://github.com/deepflowio/deepflow/pull/7126) by [TomatoMr](https://github.com/TomatoMr)
* feat: add volcengine cloud platform for server controller [#7090](https://github.com/deepflowio/deepflow/pull/7090) by [askyrie](https://github.com/askyrie)
* feat: agent directly reports metrics that can be used for alert [#7089](https://github.com/deepflowio/deepflow/pull/7089) by [TomatoMr](https://github.com/TomatoMr)
* feat: server directly reports metrics of load1_by_cpu_num that can be used for alert [#7088](https://github.com/deepflowio/deepflow/pull/7088) by [lzf575](https://github.com/lzf575)

#### Refactoring
* refactor: add logs [#8787](https://github.com/deepflowio/deepflow/pull/8787) by [yuanchaoa](https://github.com/yuanchaoa)
* refactor: Remove legacy agent config [#8586](https://github.com/deepflowio/deepflow/pull/8586) by [rvql](https://github.com/rvql)
* refactor: modify log of configuration changes [#8258](https://github.com/deepflowio/deepflow/pull/8258) by [yuanchaoa](https://github.com/yuanchaoa)
* refactor: agent workspace dependencies [#8208](https://github.com/deepflowio/deepflow/pull/8208) by [TomatoMr](https://github.com/TomatoMr)
* refactor: agent is compatibles with configuration and interfaces of both old and new versions [#8084](https://github.com/deepflowio/deepflow/pull/8084) by [TomatoMr](https://github.com/TomatoMr)
* refactor: agent adds the 'disabled-cgroups' configuration. [#8164](https://github.com/deepflowio/deepflow/pull/8164) by [TomatoMr](https://github.com/TomatoMr)
* refactor: Remove extended proc event handler [#8161](https://github.com/deepflowio/deepflow/pull/8161) by [rvql](https://github.com/rvql)
* refactor: Remove old profiler regex [#8158](https://github.com/deepflowio/deepflow/pull/8158) by [rvql](https://github.com/rvql)
* refactor: agent adds a disable_cgroups option [#8140](https://github.com/deepflowio/deepflow/pull/8140) by [TomatoMr](https://github.com/TomatoMr)
* refactor: update cli [#8134](https://github.com/deepflowio/deepflow/pull/8134) by [lzf575](https://github.com/lzf575)
* refactor: remove unused sql [#7656](https://github.com/deepflowio/deepflow/pull/7656) by [roryye](https://github.com/roryye)
* refactor: Adjust agent profile configurations [#7918](https://github.com/deepflowio/deepflow/pull/7918) by [rvql](https://github.com/rvql)
* refactor: update trace_tree debug info [#7915](https://github.com/deepflowio/deepflow/pull/7915) by [lzf575](https://github.com/lzf575)
* refactor: agent divides the config into dynamic_config and user_config [#7872](https://github.com/deepflowio/deepflow/pull/7872) by [TomatoMr](https://github.com/TomatoMr)
* refactor: add configuration structures [#7804](https://github.com/deepflowio/deepflow/pull/7804) by [yuanchaoa](https://github.com/yuanchaoa)
* refactor: modify the default batch write size for metrics and flow logs [#7789](https://github.com/deepflowio/deepflow/pull/7789) by [lzf575](https://github.com/lzf575)
* refactor: agent config [#7745](https://github.com/deepflowio/deepflow/pull/7745) by [TomatoMr](https://github.com/TomatoMr)
* refactor: define field value type using enum [#7686](https://github.com/deepflowio/deepflow/pull/7686) by [lzf575](https://github.com/lzf575)
* refactor: polish agent config [#7342](https://github.com/deepflowio/deepflow/pull/7342) by [sharang](https://github.com/sharang)
* refactor: update monitor, server log with org info [#7597](https://github.com/deepflowio/deepflow/pull/7597) by [roryye](https://github.com/roryye)
* refactor: agent remove prometheus api sync [#7576](https://github.com/deepflowio/deepflow/pull/7576) by [TomatoMr](https://github.com/TomatoMr)
* refactor: cli update and deepflow-server update message [#7500](https://github.com/deepflowio/deepflow/pull/7500) by [lzf575](https://github.com/lzf575)
* refactor: server message update [#7498](https://github.com/deepflowio/deepflow/pull/7498) by [TomatoMr](https://github.com/TomatoMr)
* refactor: trace_tree add topic field [#7418](https://github.com/deepflowio/deepflow/pull/7418) by [lzf575](https://github.com/lzf575)
* refactor: trace-id-with-index is enabled by default and the type is hash [#7416](https://github.com/deepflowio/deepflow/pull/7416) by [lzf575](https://github.com/lzf575)
* refactor: Ingester add default value config [#7395](https://github.com/deepflowio/deepflow/pull/7395) by [lzf575](https://github.com/lzf575)
* refactor: modify the type of TraceTreeEnabled field [#7378](https://github.com/deepflowio/deepflow/pull/7378) by [lzf575](https://github.com/lzf575)
* refactor: rename package name [#7344](https://github.com/deepflowio/deepflow/pull/7344) by [taloric](https://github.com/taloric)
* refactor: move trace-map dir path [#7343](https://github.com/deepflowio/deepflow/pull/7343) by [taloric](https://github.com/taloric)
* refactor: OTel HTTP l7_protocol_str change from http to HTTP [#7292](https://github.com/deepflowio/deepflow/pull/7292) by [lzf575](https://github.com/lzf575)
* refactor: server recorder polishes id allocator [#7168](https://github.com/deepflowio/deepflow/pull/7168) by [ZhengYa-0110](https://github.com/ZhengYa-0110)
* refactor: Change crate name [#7155](https://github.com/deepflowio/deepflow/pull/7155) by [rvql](https://github.com/rvql)
* refactor: trace_tree add encoding respose statistics [#7430](https://github.com/deepflowio/deepflow/pull/7430) by [lzf575](https://github.com/lzf575)

#### Performance
* perf: add primary key for in_process table [#8623](https://github.com/deepflowio/deepflow/pull/8623) by [lzf575](https://github.com/lzf575)
* perf: modify materialized view local table without group by [#8575](https://github.com/deepflowio/deepflow/pull/8575) by [lzf575](https://github.com/lzf575)
* perf: improve the performance of json string escape [#8508](https://github.com/deepflowio/deepflow/pull/8508) by [lzf575](https://github.com/lzf575)
* perf: use template instead of Interface{} in LockFreePool [#8372](https://github.com/deepflowio/deepflow/pull/8372) by [lzf575](https://github.com/lzf575)
* perf: reduce ckwriter memory [#8417](https://github.com/deepflowio/deepflow/pull/8417) by [lzf575](https://github.com/lzf575)
* perf: remove the '-l' compilation parameter to allow inline [#7935](https://github.com/deepflowio/deepflow/pull/7935) by [lzf575](https://github.com/lzf575)
* perf: improve the performance of ClickHouse table changes under multiple organizations [#7788](https://github.com/deepflowio/deepflow/pull/7788) by [lzf575](https://github.com/lzf575)
* perf: speed up the update of ClickHouse table structure [#7748](https://github.com/deepflowio/deepflow/pull/7748) by [lzf575](https://github.com/lzf575)
* perf: speed up the update of ClickHouse table structure [#7746](https://github.com/deepflowio/deepflow/pull/7746) by [lzf575](https://github.com/lzf575)
* perf: optimize trace_tree slice assignment [#7414](https://github.com/deepflowio/deepflow/pull/7414) by [lzf575](https://github.com/lzf575)
* perf: improve trace_tree search index [#7413](https://github.com/deepflowio/deepflow/pull/7413) by [lzf575](https://github.com/lzf575)
* perf: add setting ttl_only_drop_parts to the CK table to make TTL more efficient [#7265](https://github.com/deepflowio/deepflow/pull/7265) by [lzf575](https://github.com/lzf575)
* perf: improve parsing RequestResource from http.url of OTel data [#7172](https://github.com/deepflowio/deepflow/pull/7172) by [lzf575](https://github.com/lzf575)

#### Documentation
* docs: update agent config doc [#8660](https://github.com/deepflowio/deepflow/pull/8660) by [sharang](https://github.com/sharang)
* docs: polish compression config [#7701](https://github.com/deepflowio/deepflow/pull/7701) by [sharang](https://github.com/sharang)
* docs: agent correct the description of inputs.ebpf.socket.tunning [#7664](https://github.com/deepflowio/deepflow/pull/7664) by [TomatoMr](https://github.com/TomatoMr)
* docs: rename opentemetry to opentelemetry [#7245](https://github.com/deepflowio/deepflow/pull/7245) by [lzf575](https://github.com/lzf575)

#### Chore
* chore: Improve rebuild speed by eliminating unnecessary rerun of build.rs [#8207](https://github.com/deepflowio/deepflow/pull/8207) by [rvql](https://github.com/rvql)
* chore: update cli dependencies [#7249](https://github.com/deepflowio/deepflow/pull/7249) by [lzf575](https://github.com/lzf575)

#### OTHER
* add ci to clear stale branch [#8149](https://github.com/deepflowio/deepflow/pull/8149) by [jiumos](https://github.com/jiumos)
* failed to create tables in ByConity database under non-default organization [#8148](https://github.com/deepflowio/deepflow/pull/8148) by [lzf575](https://github.com/lzf575)
* Update typo in comments [#8127](https://github.com/deepflowio/deepflow/pull/8127) by [Hyzhou](https://github.com/Hyzhou)
* Cmbc [#8098](https://github.com/deepflowio/deepflow/pull/8098) by [rvql](https://github.com/rvql)
* Update the description of `status` in l4_flow_log. [#8036](https://github.com/deepflowio/deepflow/pull/8036) by [deepflow-lifei](https://github.com/deepflow-lifei)
* Relaxing regular expression restrictions for the ClusterID [#7907](https://github.com/deepflowio/deepflow/pull/7907) by [Hyzhou](https://github.com/Hyzhou)
* use .env to config docker-compose.yaml [#7729](https://github.com/deepflowio/deepflow/pull/7729) by [asdfsx](https://github.com/asdfsx)
* refector: add agent.proto [#7695](https://github.com/deepflowio/deepflow/pull/7695) by [yuanchaoa](https://github.com/yuanchaoa)
* Update changelog [#7660](https://github.com/deepflowio/deepflow/pull/7660) by [lzf575](https://github.com/lzf575)
