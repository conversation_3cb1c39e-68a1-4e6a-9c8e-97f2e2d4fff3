flowchart TB
    subgraph ControllerMaster["Controller Master"]
        direction TB
        API["API Server<br/>(云平台/控制器/采集器)"]
        Health["健康检查<br/>授权检查"]
        Failover["故障转移<br/>资源重分配"]
    end

    subgraph Controller["Controller"]
        direction TB
        Router["Router/Service<br/>对外API"]
        
        subgraph Sync["Synchronizer"]
            direction LR
            Config["配置数据推送"]
            Discovery["自动发现<br/>(控制器/数据节点/采集器)"]
        end
        
        subgraph DataMgr["数据管理"]
            direction LR
            Manager["Task Manager<br/>(云平台/记录任务)"]
            Recorder["资源数据记录<br/>变更事件发送"]
            TagRecorder["字典标签数据"]
        end
        
        subgraph Cloud["云平台集成"]
            direction LR
            Platforms["各类云平台<br/>数据收集组装"]
            K8sGather["K8s资源数据"]
        end
    end

    subgraph Resources["资源管理"]
        direction LR
        Vtap["采集器管理"]
        TSDB["数据节点"]
        MetaData["元数据"]
    end

    ControllerMaster --> Controller
    Controller --> Resources
    
    classDef master fill:#f9d5e5,stroke:#333,stroke-width:2px;
    classDef controller fill:#d5e8d4,stroke:#333,stroke-width:2px;
    classDef resource fill:#e1d5e7,stroke:#333,stroke-width:2px;
    classDef submodule fill:#fff,stroke:#333,stroke-width:1px;

    class ControllerMaster,API,Health,Failover master;
    class Controller,Router,Sync,DataMgr,Cloud controller;
    class Resources,Vtap,TSDB,MetaData resource;
    class Config,Discovery,Manager,Recorder,TagRecorder,Platforms,K8sGather submodule;