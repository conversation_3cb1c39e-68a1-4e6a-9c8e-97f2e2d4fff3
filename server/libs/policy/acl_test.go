/*
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package policy

import (
	"reflect"
	"testing"

	. "github.com/deepflowio/deepflow/server/libs/datatype"
)

func TestAclSimple(t *testing.T) {
	acl := Acl{}
	in := []uint16{1, 3, 4, 5, 10, 11, 12}
	out := acl.getPortRange(in)
	if !reflect.DeepEqual(out, []PortRange{NewPortRange(1, 1), NewPortRange(3, 5), NewPortRange(10, 12)}) {
		t.<PERSON><PERSON>("TestAclSimple in(%v) out(%v)\n", in, out)
	}
}
