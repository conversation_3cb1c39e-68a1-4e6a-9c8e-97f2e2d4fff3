/*
 * Copyright (c) 2024 <PERSON>shan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package updater

import (
	cloudmodel "github.com/deepflowio/deepflow/server/controller/cloud/model"
	ctrlrcommon "github.com/deepflowio/deepflow/server/controller/common"
	metadbmodel "github.com/deepflowio/deepflow/server/controller/db/metadb/model"
	"github.com/deepflowio/deepflow/server/controller/recorder/cache"
	"github.com/deepflowio/deepflow/server/controller/recorder/cache/diffbase"
	rcommon "github.com/deepflowio/deepflow/server/controller/recorder/common"
	"github.com/deepflowio/deepflow/server/controller/recorder/db"
	"github.com/deepflowio/deepflow/server/controller/recorder/pubsub/message"
)

type Subnet struct {
	UpdaterBase[
		cloudmodel.Subnet,
		*diffbase.Subnet,
		*metadbmodel.Subnet,
		metadbmodel.Subnet,
		*message.AddedSubnets,
		message.AddedSubnets,
		message.AddNoneAddition,
		*message.UpdatedSubnet,
		message.UpdatedSubnet,
		*message.UpdatedSubnetFields,
		message.UpdatedSubnetFields,
		*message.DeletedSubnets,
		message.DeletedSubnets,
		message.DeleteNoneAddition]
}

func NewSubnet(wholeCache *cache.Cache, cloudData []cloudmodel.Subnet) *Subnet {
	updater := &Subnet{
		newUpdaterBase[
			cloudmodel.Subnet,
			*diffbase.Subnet,
			*metadbmodel.Subnet,
			metadbmodel.Subnet,
			*message.AddedSubnets,
			message.AddedSubnets,
			message.AddNoneAddition,
			*message.UpdatedSubnet,
			message.UpdatedSubnet,
			*message.UpdatedSubnetFields,
			message.UpdatedSubnetFields,
			*message.DeletedSubnets,
			message.DeletedSubnets,
			message.DeleteNoneAddition,
		](
			ctrlrcommon.RESOURCE_TYPE_SUBNET_EN,
			wholeCache,
			db.NewSubnet().SetMetadata(wholeCache.GetMetadata()),
			wholeCache.DiffBaseDataSet.Subnets,
			cloudData,
		),
	}
	updater.dataGenerator = updater
	return updater
}

func (s *Subnet) getDiffBaseByCloudItem(cloudItem *cloudmodel.Subnet) (diffBase *diffbase.Subnet, exists bool) {
	diffBase, exists = s.diffBaseData[cloudItem.Lcuuid]
	return
}

func (s *Subnet) generateDBItemToAdd(cloudItem *cloudmodel.Subnet) (*metadbmodel.Subnet, bool) {
	networkID, exists := s.cache.ToolDataSet.GetNetworkIDByLcuuid(cloudItem.NetworkLcuuid)
	if !exists {
		log.Error(resourceAForResourceBNotFound(
			ctrlrcommon.RESOURCE_TYPE_NETWORK_EN, cloudItem.NetworkLcuuid,
			ctrlrcommon.RESOURCE_TYPE_SUBNET_EN, cloudItem.Lcuuid,
		), s.metadata.LogPrefixes)
		return nil, false
	}
	prefix, netmask, err := rcommon.CIDRToPreNetMask(cloudItem.CIDR)
	if err != nil {
		log.Errorf("convert %s cidr: %s failed: %v", ctrlrcommon.RESOURCE_TYPE_SUBNET_EN, cloudItem.CIDR, err.Error(), s.metadata.LogPrefixes)
		return nil, false
	}

	dbItem := &metadbmodel.Subnet{
		Name:      cloudItem.Name,
		Label:     cloudItem.Label,
		Prefix:    prefix,
		Netmask:   netmask,
		SubDomain: cloudItem.SubDomainLcuuid,
		NetworkID: networkID,
		Domain:    s.metadata.GetDomainLcuuid(),
	}
	dbItem.Lcuuid = cloudItem.Lcuuid
	return dbItem, true
}

func (s *Subnet) generateUpdateInfo(diffBase *diffbase.Subnet, cloudItem *cloudmodel.Subnet) (*message.UpdatedSubnetFields, map[string]interface{}, bool) {
	structInfo := new(message.UpdatedSubnetFields)
	mapInfo := make(map[string]interface{})
	if diffBase.Name != cloudItem.Name {
		mapInfo["name"] = cloudItem.Name
		structInfo.Name.Set(diffBase.Name, cloudItem.Name)
	}
	if diffBase.Label != cloudItem.Label {
		mapInfo["label"] = cloudItem.Label
		structInfo.Label.Set(diffBase.Label, cloudItem.Label)
	}

	return structInfo, mapInfo, len(mapInfo) > 0
}
