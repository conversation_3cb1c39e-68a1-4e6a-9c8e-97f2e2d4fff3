/**
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package pubsub

import (
	"github.com/deepflowio/deepflow/server/controller/common"
	"github.com/deepflowio/deepflow/server/controller/recorder/pubsub/message"
)

type NATRule struct {
	ResourcePubSubComponent[
		*message.AddedNATRules,
		message.AddedNATRules,
		message.AddNoneAddition,
		*message.UpdatedNATRule,
		message.UpdatedNATRule,
		*message.UpdatedNATRuleFields,
		message.UpdatedNATRuleFields,
		*message.DeletedNATRules,
		message.DeletedNATRules,
		message.DeleteNoneAddition]
}

func NewNATRule() *NATRule {
	return &NATRule{
		ResourcePubSubComponent[
			*message.AddedNATRules,
			message.AddedNATRules,
			message.AddNoneAddition,
			*message.UpdatedNATRule,
			message.UpdatedNATRule,
			*message.UpdatedNATRuleFields,
			message.UpdatedNATRuleFields,
			*message.DeletedNATRules,
			message.DeletedNATRules,
			message.DeleteNoneAddition,
		]{
			PubSubComponent: newPubSubComponent(PubSubTypeNATRule),
			resourceType:    common.RESOURCE_TYPE_NAT_RULE_EN,
		},
	}
}
