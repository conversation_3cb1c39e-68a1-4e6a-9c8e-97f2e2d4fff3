/**
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package pubsub

import (
	"github.com/deepflowio/deepflow/server/controller/common"
	"github.com/deepflowio/deepflow/server/controller/recorder/pubsub/message"
)

type PodNamespace struct {
	ResourcePubSubComponent[
		*message.AddedPodNamespaces,
		message.AddedPodNamespaces,
		message.AddNoneAddition,
		*message.UpdatedPodNamespace,
		message.UpdatedPodNamespace,
		*message.UpdatedPodNamespaceFields,
		message.UpdatedPodNamespaceFields,
		*message.DeletedPodNamespaces,
		message.DeletedPodNamespaces,
		message.DeleteNoneAddition]
}

func NewPodNamespace() *PodNamespace {
	return &PodNamespace{
		ResourcePubSubComponent[
			*message.AddedPodNamespaces,
			message.AddedPodNamespaces,
			message.AddNoneAddition,
			*message.UpdatedPodNamespace,
			message.UpdatedPodNamespace,
			*message.UpdatedPodNamespaceFields,
			message.UpdatedPodNamespaceFields,
			*message.DeletedPodNamespaces,
			message.DeletedPodNamespaces,
			message.DeleteNoneAddition,
		]{
			PubSubComponent: newPubSubComponent(PubSubTypePodNamespace),
			resourceType:    common.RESOURCE_TYPE_POD_NAMESPACE_EN,
		},
	}
}
