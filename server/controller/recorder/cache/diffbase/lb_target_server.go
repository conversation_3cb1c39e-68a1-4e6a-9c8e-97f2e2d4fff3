/**
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package diffbase

import (
	cloudmodel "github.com/deepflowio/deepflow/server/controller/cloud/model"
	ctrlrcommon "github.com/deepflowio/deepflow/server/controller/common"
	metadbmodel "github.com/deepflowio/deepflow/server/controller/db/metadb/model"
)

func (b *DataSet) AddLBTargetServer(dbItem *metadbmodel.LBTargetServer, seq int) {
	b.LBTargetServers[dbItem.Lcuuid] = &LBTargetServer{
		DiffBase: DiffBase{
			Sequence: seq,
			Lcuuid:   dbItem.Lcuuid,
		},
		IP:       dbItem.IP,
		Port:     dbItem.Port,
		Protocol: dbItem.Protocol,
	}
	b.GetLogFunc()(addDiffBase(ctrlrcommon.RESOURCE_TYPE_LB_TARGET_SERVER_EN, b.LBTargetServers[dbItem.Lcuuid]), b.metadata.LogPrefixes)
}

func (b *DataSet) DeleteLBTargetServer(lcuuid string) {
	delete(b.LBTargetServers, lcuuid)
	log.Info(deleteDiffBase(ctrlrcommon.RESOURCE_TYPE_LB_TARGET_SERVER_EN, lcuuid), b.metadata.LogPrefixes)
}

type LBTargetServer struct {
	DiffBase
	IP       string `json:"ip"`
	Port     int    `json:"port"`
	Protocol string `json:"protocal"`
}

func (l *LBTargetServer) Update(cloudItem *cloudmodel.LBTargetServer) {
	l.IP = cloudItem.IP
	l.Port = cloudItem.Port
	l.Protocol = cloudItem.Protocol
	log.Info(updateDiffBase(ctrlrcommon.RESOURCE_TYPE_LB_TARGET_SERVER_EN, l))
}
