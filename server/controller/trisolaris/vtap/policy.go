/*
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package vtap

import (
	mapset "github.com/deckarep/golang-set"
	"github.com/deepflowio/deepflow/server/controller/trisolaris/metadata"
)

type VTapPolicyData struct {
	metaData *metadata.MetaData
}

func newVTapPolicyData(metaData *metadata.MetaData) *VTapPolicyData {
	return &VTapPolicyData{
		metaData: metaData,
	}
}

func (v *VTapPolicyData) getVTapPolicyVersion(vtapID int, functions mapset.Set) uint64 {
	return v.metaData.GetVTapPolicyVersion(vtapID, functions)
}

func (v *VTapPolicyData) getVTapPolicyData(vtapID int, functions mapset.Set) []byte {
	return v.metaData.GetVTapPolicyString(vtapID, functions)
}

func (v *VTapPolicyData) getAgentPolicyVersion(vtapID int, functions mapset.Set) uint64 {
	return v.metaData.GetAgentMetaData().GetAgentPolicyVersion(vtapID, functions)
}

func (v *VTapPolicyData) getAgentPolicyData(vtapID int, functions mapset.Set) []byte {
	return v.metaData.GetAgentMetaData().GetAgentPolicyString(vtapID, functions)
}
