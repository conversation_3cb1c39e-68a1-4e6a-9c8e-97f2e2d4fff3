/*
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package http

import (
	"sync"

	"github.com/gin-gonic/gin"
)

type Registration interface {
	Register(mux *gin.Engine)
}

var register = struct {
	sync.RWMutex
	r []Registration
}{}

func Register(r interface{}) {
	register.Lock()
	defer register.Unlock()

	register.r = append(register.r, (r).(Registration))
}

func RegisterTo(mux *gin.Engine) {
	for _, registered := range register.r {
		registered.Register(mux)
	}
}
