/**
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package common

import (
	"github.com/deepflowio/deepflow/server/controller/db/metadb"
)

func FindPtr[T any](db *metadb.DB) ([]*T, error) {
	var result []*T
	err := db.Find(&result).Error
	return result, err
}

func WhereFind[T any](db *metadb.DB, query interface{}, args ...interface{}) ([]T, error) {
	var result []T
	err := db.Where(query, args...).Find(&result).Error
	return result, err
}

func WhereFindPtr[T any](db *metadb.DB, query interface{}, args ...interface{}) ([]*T, error) {
	var result []*T
	err := db.Where(query, args...).Find(&result).Error
	return result, err
}
