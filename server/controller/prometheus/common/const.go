/**
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package common

var (
	ResourcePrometheusMetricName           = "metric_name"
	ResourcePrometheusLabelName            = "label_name"
	ResourcePrometheusLabelValue           = "label_value"
	ResourcePrometheusMetricAPPLabelLayout = "metric_app_label_layout"
	ResourcePrometheusLabel                = "label"
	ResourcePrometheusMetricLabelName      = "metric_label_name"
)

var (
	TargetLabelInstance = "instance"
	TargetLabelJob      = "job"
)
