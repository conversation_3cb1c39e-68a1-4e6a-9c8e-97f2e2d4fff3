# type: section
# name:
#   en: Global
#   ch: 全局配置
# description:
global:
  # type: section
  # name:
  #   en: Limits
  #   ch: 资源限制
  # description:
  #   en: Resource limitations
  #   ch: 控制 deepflow-agent 资源用量
  limits:
    # type: int
    # name:
    #   en: CPU Limit
    #   ch: CPU 限制
    # unit: Logical Milli Cores
    # range: [1, 100000]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     deepflow-agent uses cgroups to limit CPU usage.
    #     1 millicpu = 1 millicore = 0.001 core.
    #   ch: |-
    #     deepflow-agent 使用 cgroups 来限制自身的 CPU 用量，
    #     1 millicpu = 1 millicore = 0.001 core。
    # upgrade_from: max_millicpus
    max_millicpus: 1000
    # type: int
    # name:
    #   en: Memory Limit
    #   ch: 内存限制
    # unit: MiB
    # range: [128, 100000]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     deepflow-agent uses cgroups to limit memory usage.
    #   ch: |-
    #     deepflow-agent 使用 cgroups 限制自身的 memory 用量.
    # upgrade_from: max_memory
    max_memory: 768
    # type: int
    # name:
    #   en: Maximum Log Backhaul Rate
    #   ch: 日志每小时回传上限
    # unit: Lines/Hour
    # range: [0, 1000000]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     deepflow-agent will send logs to deepflow-server, 0 means no limit.
    #   ch: |-
    #     用于 deepflow-agent 控制自身运行日志的每小时回传数量，设置为 0 表示不设限制。
    # upgrade_from: log_threshold
    max_log_backhaul_rate: 36000
    # type: int
    # name:
    #   en: Maximum Local Log File Size
    #   ch: 本地日志文件大小上限
    # unit: MiB
    # range: [10, 10000]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The maximum disk space allowed for deepflow-agent log files.
    #   ch: |-
    #     用于 deepflow-agent 控制自身运行日志在本地的存储量。
    # upgrade_from: log_file_size
    max_local_log_file_size: 1000
    # type: duration
    # name:
    #   en: Local Log Retention
    #   ch: 本地日志留存时间
    # unit:
    # range: [10d, 10000d]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The retention time for deepflow-agent log files.
    #   ch: |-
    #     用于 deepflow-agent 控制自身运行日志在本地的留存时长。
    # upgrade_from: log_retention
    local_log_retention: 300d
    # type: int
    # name:
    #   en: Maximum Socket Count
    #   ch: Socket 数量上限
    # unit: count
    # range: [16, 4096]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The maximum number of sockets that the agent can open.
    #     Agent will restart if socket count exceeds this value.
    #   ch: |-
    #     用于控制 deepflow-agent 可以打开的 socket 数量上限。
    #     超过限制时 agent 会重启。
    # upgrade_from: static_config.max-sockets
    max_sockets: 1024
    # type: duration
    # name:
    #   en: Maximum Socket Count Tolerate Interval
    #   ch: Socket 数量超限容忍时间
    # unit:
    # range: [0s, 3600s]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The interval to tolerate socket count exceeding max-sockets before restarting.
    #     Agent will only restart if socket count exceeds max-sockets for this duration.
    #     Restarts are triggered by guard module, so setting this value lower than guard-interval
    #     will cause agent to restart immediately.
    #   ch: |-
    #     用于控制 deepflow-agent 在 socket 数量超过上限后，重启前的容忍时间。
    #     只有当 socket 数量持续超过上限达到该时间后才会触发重启。
    #     重启由 guard 模块触发，因此该值小于 guard-interval 时会导致立即重启。
    # upgrade_from: static_config.max-sockets-tolerate-interval
    max_sockets_tolerate_interval: 60s
  # type: section
  # name:
  #   en: Alerts
  #   ch: 告警
  # description:
  #   en:
  #   ch:
  alerts:
    # type: int
    # name:
    #   en: Thread Limit
    #   ch: 线程数限制
    # unit:
    # range: [1, 1000]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The maximum number of threads deepflow-agent is allowed to create.
    #     - When the number of threads exceeds this limit, an exception alert will be triggered.
    #     - When the number of threads exceeds twice this limit value, a deepflow-agent restart will be triggered.
    #   ch: |-
    #     用于控制 deepflow-agent 创建的线程数量。
    #     - 当线程数量超过该限制值，会触发采集器异常告警。
    #     - 当线程数量超过该限制值的2倍，会触发采集器重启。
    # upgrade_from: thread_threshold
    thread_threshold: 500
    # type: int
    # name:
    #   en: Process Limit
    #   ch: 进程数限制
    # unit:
    # range: [1, 100]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The maximum number of processes named `deepflow-agent` is allowed to launch.
    #     If the number of processes named `deepflow-agent` in the current system reaches this limit,
    #     subsequent processes named `deepflow-agent` will fail to start.
    #   ch: |-
    #     用于控制名称为`deepflow-agent`的进程数量。
    #     若当前系统中名为`deepflow-agent`的进程数达到该限制值，则之后名为`deepflow-agent`的进程将会启动失败。
    # upgrade_from: process_threshold
    process_threshold: 10
    # type: bool
    # name:
    #   en: Core File Checker
    #   ch: Core File 检查
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     When the host has an invalid NFS file system or a docker is running,
    #     sometime program hang when checking the core file, so the core file
    #     check provides a switch to prevent the process hang. Additional links:
    #     - [https://serverfault.com/questions/367438/ls-hangs-for-a-certain-directory](https://serverfault.com/questions/367438/ls-hangs-for-a-certain-directory)
    #     - [https://unix.stackexchange.com/questions/495854/processes-hanging-when-trying-to-access-a-file](https://unix.stackexchange.com/questions/495854/processes-hanging-when-trying-to-access-a-file)
    #   ch: |-
    #     当主机存在无效的 NFS 文件系统，或者 Docker 正在运行时，
    #     检查 core 文件时可能会导致程序挂起。
    #     因此，core 文件检查提供了一个开关，以防止进程挂起。参考链接：
    #     - [https://serverfault.com/questions/367438/ls-hangs-for-a-certain-directory](https://serverfault.com/questions/367438/ls-hangs-for-a-certain-directory)
    #     - [https://unix.stackexchange.com/questions/495854/processes-hanging-when-trying-to-access-a-file](https://unix.stackexchange.com/questions/495854/processes-hanging-when-trying-to-access-a-file)
    # upgrade_from: static_config.check-core-file-disabled
    # deprecated: true
    check_core_file_disabled: false
  # type: section
  # name:
  #   en: Circuit Breakers
  #   ch: 熔断机制
  # description:
  #   en: Control deepflow-agent to stop running or stop some functions under certain environmental conditions.
  #   ch: 控制 deepflow-agent 在一定的环境条件下停止运行或停止部分功能。
  circuit_breakers:
    # type: section
    # name:
    #   en: System Free Memory Percentage
    #   ch: 系统空闲内存百分比
    # description:
    #   en: |-
    #     Calculation Method: `(free_memory / total_memory) * 100%`
    #   ch: |-
    #     计算公式：`(free_memory / total_memory) * 100%`
    sys_memory_percentage:
      # type: int
      # name:
      #   en: Trigger Threshold
      #   ch: 触发阈值
      # unit: '%'
      # range: [0, 100]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Setting it to 0 indicates that the system memory ratio is not checked.
      #     The `observed memory ratio` is determined by `global.circuit_breakers.sys_memory_percentage.metric`.
      #     1. When the current system `observed memory ratio` is below `trigger_threshold` * 70%,
      #        the agent will automatically restart.
      #     2. When the current system `observed memory ratio` is below trigger_threshold but above 70%,
      #        the agent is set to the abnormal state of `FREE_MEM_EXCEEDED` and reports an alarm.
      #     3. When the current system `observed memory ratio` remains above `trigger_threshold` * 110%,
      #        the agent recovers from the abnormal state.
      #   ch: |-
      #     设置为 0 表示不检查系统内存比率。
      #     观测内存比率是由 `global.circuit_breakers.sys_memory_percentage.metric` 决定.
      #     1. 当系统`观测内存比率`低于 `trigger_threshold` * 70% 时，
      #        采集器将自动重启。
      #     2. 当系统`观测内存比率`低于 `trigger_threshold` 但高于 70% 时，
      #        采集器设置为 `FREE_MEM_EXCEEDED` 的异常状态，并上报采集器异常告警。
      #     3. 当系统`观测内存比率`持续高于 `trigger_threshold` * 110% 时，
      #        采集器将从异常状态恢复。
      # upgrade_from: sys_free_memory_limit
      trigger_threshold: 0
      # type: string
      # name:
      #   en: Metric
      #   ch: 观测指标
      # unit:
      # range: []
      # enum_options: [free, available]
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     deepflow-agent observes the percentage of this memory metric
      #   ch: |-
      #     deepflow-agent 观测该内存指标的百分比
      # upgrade_from: sys_free_memory_metric
      metric: free
    # type: section
    # name:
    #   en: Relative System Load
    #   ch: 相对系统负载
    # description:
    #   en: |-
    #     Calculation Method: `system_load / total_cpu_cores`
    #   ch: |-
    #     计算公式: `system_load / total_cpu_cores`
    relative_sys_load:
      # type: float
      # name:
      #   en: Trigger Threshold
      #   ch: 触发阈值
      # unit:
      # range: [0, 10]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When Linux system load divided by the number of
      #     CPU cores exceeds this value, the agent automatically enters
      #     the disabled state.
      #     Setting it or `recovery_threshold` to 0 disables this feature.
      #   ch: |-
      #     当`相对系统负载`（load 除以 CPU 核数）高于此阈值时，采集器自动停止运行。
      #     设置该值或 `recovery_threshold` 为 0 时，该特性不生效。
      # upgrade_from: system_load_circuit_breaker_threshold
      trigger_threshold: 1.0
      # type: float
      # name:
      #   en: Recovery Threshold
      #   ch: 恢复阈值
      # unit:
      # range: [0, 10]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     After deepflow-agent enters disabled state and Linux system load
      #     divided by the number of CPU cores is continuously below this value for 5
      #     minutes, the agent can recover from the circuit breaker
      #     disabled state.
      #     Setting it or `trigger_threshold` to 0 disables this feature.
      #   ch: |-
      #     在采集器处于停止状态后，当`相对系统负载`（load 除以 CPU 核数）连续 5 分钟低于此阈值时，
      #     采集器自动从停止状态恢复运行。
      #     设置该值或 `trigger_threshold` 为 0 时，该特性不生效。
      # upgrade_from: system_load_circuit_breaker_recover
      recovery_threshold: 0.9
      # type: string
      # name:
      #   en: Metric
      #   ch: 观测指标
      # unit:
      # range: []
      # enum_options: [load1, load5, load15]
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     The system load circuit breaker mechanism uses this metric,
      #     and the agent will check this metric every 10 seconds by default.
      #   ch: |-
      #     deepflow-agent 默认每 10 秒监控一次所设定的系统负载指标项。
      # upgrade_from: system_load_circuit_breaker_metric
      metric: load15
    # type: section
    # name:
    #   en: Tx Throughput
    #   ch: 发送吞吐
    # description:
    tx_throughput:
      # type: int
      # name:
      #   en: Trigger Threshold
      #   ch: 触发阈值
      # unit: Mbps
      # range: [0, 100000]
      # enum_options: []
      # modification: hot_update
      # ee_feature: true
      # description:
      #   en: |-
      #     When the outbound throughput of the NPB interface reaches or exceeds
      #     the threshold, the broker will be stopped, after that the broker will
      #     be resumed if the throughput is lower than
      #     `(trigger_threshold - outputs.npb.max_tx_throughput)*90%`
      #     within 5 consecutive monitoring intervals.
      #
      #     Attention: When configuring this value, it must be greater than
      #     `outputs.npb.max_tx_throughput`. Set to 0 will disable this feature.
      #   ch: |-
      #     如果流量分发所用网络接口的出方向吞吐量达到或超出此阈值，deepflow-agent 停止流量分发；
      #     如果该网络接口的出方向吞吐量连续 5 个监控周期低于`(trigger_threshold -
      #     outputs.npb.max_tx_throughput)*90%`，deepflow-agent 恢复流量分发。
      #
      #     注意：
      #     1. 取值为 0 时，该特性不生效；
      #     2. 若取非 0 值，必须大于 `outputs.npb.max_tx_throughput`。
      # upgrade_from: max_tx_bandwidth
      trigger_threshold: 0
      # type: duration
      # name:
      #   en: Throughput Monitoring Interval
      #   ch: 吞吐监控间隔
      # unit:
      # range: [1s, 60s]
      # enum_options: []
      # modification: hot_update
      # ee_feature: true
      # description:
      #   en: |-
      #     Monitoring interval for outbound traffic rate of NPB interface.
      #   ch: |-
      #     deepflow-agent 对流量分发所使用网络接口的出方向吞吐量指标的监控周期。
      # upgrade_from: bandwidth_probe_interval
      throughput_monitoring_interval: 10s
    # type: section
    # name:
    #   en: Free Disk
    #   ch: 空闲磁盘
    # description:
    free_disk:
      # type: int
      # name:
      #   en: Percentage Trigger Threshold
      #   ch: 百分比触发阈值
      # unit: '%'
      # range: [0, 100]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     This configuration is only valid when the Agent runs in a non-container environment. Configuring to 0 means disabling the threshold.
      #     The observed disks are the disks where the `global.circuit_breakers.free_disk.directories` are located.
      #     1. When the system `free disk ratio` is lower than `this threshold`, the Agent enters the fuse disabled state,
      #        and sets the `FREE_DISK_CIRCUIT_BREAKER` abnormal state, and reports the Agent abnormal alarm.
      #     2. When the system `free disk ratio` is higher than `this threshold * 110%`, the Agent recovers from the abnormal state.
      #   ch: |-
      #     仅当采集器运行在非容器环境中时该配置有效。配置为 0 表示禁用该阈值。
      #     观测磁盘为`global.circuit_breakers.free_disk.directories`目录所在磁盘。
      #     1. 当系统`空闲磁盘比率`低于`该阈值`时，采集器进入熔断禁用状态，
      #        并设置`磁盘空闲空间触发熔断`异常状态，同时上报采集器异常告警。
      #     2. 当系统`空闲磁盘比率`高于`该阈值 * 110%` 时，采集器从异常状态恢复。
      percentage_trigger_threshold: 15
      # type: int
      # name:
      #   en: Absolute_Trigger Threshold
      #   ch: 绝对值触发阈值
      # unit: GB
      # range: [0, 100000]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     This configuration is only valid when the Agent runs in a non-container environment. Configuring to 0 means disabling the threshold.
      #     The observed disks are the disks where the `global.circuit_breakers.free_disk.directories` is located.
      #     1. When the system `free disk size` is lower than `this threshold`, the Agent enters the fuse disabled state,
      #        and sets the `FREE_DISK_CIRCUIT_BREAKER` abnormal state, and reports the Agent abnormal alarm.
      #     2. When the system `free disk size` is higher than `this threshold * 110%`, the Agent recovers from the abnormal state.
      #   ch: |-
      #     仅当采集器运行在非容器环境中时该配置有效。配置为 0 表示禁用该阈值。
      #     观测磁盘为`global.circuit_breakers.free_disk.directories`目录所在磁盘。
      #     1. 当系统`空闲磁盘大小`低于`该阈值`时，采集器进入熔断禁用状态，
      #        并设置`磁盘空闲空间触发熔断`异常状态，同时上报采集器异常告警。
      #     2. 当系统`空闲磁盘大小`高于`该阈值 * 110%` 时，采集器从异常状态恢复。
      absolute_trigger_threshold: 10
      # type: string
      # name:
      #   en: Directories
      #   ch: 观测目录
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Observe the disk space where the directories is located.
      #     For the `windows` operating system, the default value is `c:\`.
      #   ch: |-
      #     观测目录所在磁盘的空间。
      #     对于`windows`操作系统，默认值则是`c:\`
      directories: [/]

  # type: section
  # name:
  #   en: Tunning
  #   ch: 调优
  # description:
  #   en: Tune the runtime of deepflow-agent.
  #   ch: 对 deepflow-agent 的运行进行调优。
  tunning:
    # type: int
    # name:
    #   en: CPU Affinity
    #   ch: CPU 亲和性
    # unit:
    # range: [0, 65536]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     CPU affinity is the tendency of a process to run on a given CPU for as long as possible
    #     without being migrated to other processors. Invalid ID will be ignored. Currently only
    #     works for dispatcher threads. Example:
    #     ```yaml
    #     global:
    #       tunning:
    #         cpu_affinity: [1, 3, 5, 7, 9]
    #     ```
    #   ch: |-
    #     操作系统尽可能使用指定 ID 的 CPU 核运行 deepflow-agent 进程。无效的 ID 将被忽略。当前仅对
    #     dispatcher 线程生效。举例：
    #     ```yaml
    #     global:
    #       tunning:
    #         cpu_affinity: [1, 3, 5, 7, 9]
    #     ```
    # upgrade_from: static_config.cpu-affinity
    cpu_affinity: []
    # type: int
    # name:
    #   en: Process Scheduling Priority
    #   ch: 进程调度优先级
    # unit:
    # range: [-20, 19]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     The smaller the value of process scheduling priority, the higher the priority of the
    #     `deepflow-agent` process, and the larger the value, the lower the priority.
    #   ch: |-
    #     控制 deepflow-agent 进程的调度优先级。数值越小，调度优先级越高；数值越大，调度优先级越低。
    # upgrade_from: static_config.process-scheduling-priority
    process_scheduling_priority: 0
    # type: bool
    # name:
    #   en: Idle Memory Trimming
    #   ch: 闲置内存修剪
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Proactive memory trimming can effectively reduce memory usage, but there may be
    #     performance loss.
    #   ch: |-
    #     开启闲置内存修剪特性，将降低 agent 内存使用量，但可能会损失 agent 处理性能。
    # upgrade_from: static_config.memory-trim-disabled
    idle_memory_trimming: true
    # type: bool
    # name:
    #   en: Turn off swap memory
    #   ch: 禁用 swap 内存
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Note that disabling swap memory requires root and CAP_IPC_LOCK permissions, and disabling
    #     swap memory may improve performance and reduce CPU usage, but memory will increase.
    #   ch: |-
    #     注意禁用 swap 内存需要 root 和 CAP_IPC_LOCK 权限，禁用 swap 内存后性能也许会提升并且CPU使用率会降低,
    #     但是内存会升高。
    swap_disabled: false
    # type: int
    # name:
    #   en: Page Cache Reclaim Percentage
    #   ch: Page Cache 回收百分比
    # unit:
    # range: [0, 100]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     A page cache reclaim is triggered when the pecentage of page cache and
    #     cgroups memory.limit_in_bytes exceeds this value.
    #     Both anonymous memory and file page cache are accounted for in cgroup's memory usage.
    #     Under some circumstances, page cache alone can cause cgroup to OOM kill agent process.
    #     To avoid this, agent can reclaim page cache periodically. Although reclaming may not
    #     cause performance issues for agent who doesn't have much I/O, other processes in
    #     the same cgroup may be affected. Very low values are not recommended.
    #     Note:
    #     - This feature is available for cgroups v1 only.
    #     - This feature is disabled if agent memory cgroup path is "/".
    #     - The minimal interval of reclaims is 1 minute.
    #   ch: |-
    #     当文件页缓存和 cgroup 内存限制的百分比超过此阈值时，agent 将清空文件页缓存。
    #     Cgroup 的内存使用量包括匿名内存和文件页缓存。在某些情况下，仅仅是文件页缓存就可能导致
    #     cgroup 因为内存不足杀死 agent 进程。为了避免这种情况，agent 将定期强制清空文件页缓存，
    #     且由于 agent 的文件 I/O 量不大，这不太可能对 agent 的性能造成影响，但同一 cgroup 下的其他
    #     进程可能会受到影响。不建议设置很小的值。
    #     注意：
    #     - 该特性仅支持 cgroups v1。
    #     - 如果 agent 的 memory cgroup 路径是 “/”，该特性不生效。
    #     - 回收的最小间隔是 1 分钟。
    # upgrade_from: static_config.page-cache-reclaim-percentage
    page_cache_reclaim_percentage: 100
    # type: duration
    # name:
    #   en: Resource Monitoring Interval
    #   ch: 资源监控间隔
    # unit:
    # range: [1s, 3600s]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     The agent will monitor:
    #     1. System free memory
    #     2. Get the number of threads of the agent itself by reading the file information
    #        under the /proc directory
    #     3. Size and number of log files generated by the agent.
    #     4. System load
    #     5. Agent memory usage (check if memory trimming is needed)
    #   ch: |-
    #     deepflow-agent 将以配置的时间周期监控如下资源：
    #     1. 系统空闲内存
    #     2. 系统负载
    #     3. agent 的线程数量（通过读取 /proc 目录下的文件信息获取）
    #     4. agent 的日志数量和日志大小
    #     5. agent 的内存用量
    # upgrade_from: static_config.guard-interval
    resource_monitoring_interval: 10s
  # type: section
  # name:
  #   en: NTP Clock Synchronization
  #   ch: NTP 时钟同步
  # description:
  #   en: |-
  #     This synchronization mechanism does not alter the host's clock; it is only used
  #     internally by the deepflow-agent process.
  #   ch: |-
  #     此同步机制获取的时间仅供 deepflow-agent 进程内部使用，不影响主机时间。
  ntp:
    # type: bool
    # name: Enabled
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     Whether to synchronize the clock to the deepflow-server, this behavior
    #     will not change the time of the deepflow-agent running environment.
    #   ch: |-
    #     deepflow-agent 是否向 deepflow-server 做 NTP 同步的开关。
    # upgrade_from: ntp_enabled
    enabled: false
    # type: duration
    # name:
    #   en: Maximum Drift
    #   ch: 最大时钟偏差
    # unit:
    # range: [0ns, 365d]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     When the clock drift exceeds this value, the agent will restart.
    #   ch: |-
    #     当 deepflow-agent 与 deepflow-server 之间的时间偏移大于此设置值时，agent 会自动重启。
    # upgrade_from: static_config.ntp-max-interval
    max_drift: 300s
    # type: duration
    # name:
    #   en: Minimal Drift
    #   ch: 最小时钟偏差
    # unit:
    # range: [0ns, 365d]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     When the clock drift exceeds this value, the timestamp will be corrected.
    #   ch: |-
    #     当 deepflow-agent 与 deepflow-server 之间的时间偏移大于此设置值时，对 agent 的
    #     时间戳进行纠正。
    # upgrade_from: static_config.ntp-min-interval
    min_drift: 10s
  # type: section
  # name:
  #   en: Communication
  #   ch: 通信
  # description:
  #   en: Configuration of deepflow-agent communication.
  #   ch: 配置 deepflow-agent 的通信参数。
  communication:
    # type: duration
    # name:
    #   en: Proactive Request Interval
    #   ch: 主动请求间隔
    # unit:
    # range: [10s, 3600s]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The interval at which deepflow-agent proactively requests configuration and
    #     tag information from deepflow-server.
    #   ch: |-
    #     deepflow-agent 以设置的时间间隔周期性向 deepflow-server 请求配置数据和标签信息。
    # upgrade_from: sync_interval
    proactive_request_interval: 60s
    # type: duration
    # name:
    #   en: Maximum Escape Duration
    #   ch: 最大逃逸时长
    # unit:
    # range: [600s, 30d]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The maximum time that the agent is allowed to work normally when it
    #     cannot connect to the server. After the timeout, the agent automatically
    #     enters the disabled state.
    #   ch: |-
    #     `最大逃逸时长`是指 deepflow-agent 与 deepflow-server 失联后，自主运行的最长
    #     时间；超过该时长后，仍未与 server 恢复联系，agent 自动进入 disabled 状态。
    # upgrade_from: max_escape_seconds
    max_escape_duration: 3600s
    # type: ip
    # name:
    #   en: Controller IP Address
    #   ch: Controller IP 地址
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     When this value is set, deepflow-agent will use this IP to access the
    #     control plane port of deepflow-server, otherwise, the server will use
    #     its own node IP as the control plane communication IP. This parameter is
    #     usually used when the server uses a load balancer or a virtual IP to
    #     provide services externally.
    #   ch: |-
    #     用于设置 deepflow-agent 与 server 通信的控制面通信 IP；如果不设置本
    #     参数，server 下发自己的节点 IP 作为 server 端控制面通信IP。
    #     该参数通常用于 server 端使用负载均衡或虚 IP 对外提供服务的场景。
    # upgrade_from: proxy_controller_ip
    proxy_controller_ip: "127.0.0.1"
    # type: int
    # name:
    #   en: Controller Port
    #   ch: Controller 端口号
    # unit:
    # enum_options: []
    # range: [1, 65535]
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The control plane port used by deepflow-agent to access deepflow-server.
    #     The default port within the same K8s cluster is 20035, and the default port
    #     of deepflow-agent outside the cluster is 30035.
    #   ch: |-
    #     用于设置 deepflow-server 向 deepflow-agent 下发的 server 端控制面通信端口。
    # upgrade_from: proxy_controller_port
    proxy_controller_port: 30035
    # type: ip
    # name:
    #   en: Ingester IP Address
    #   ch: Ingester IP 地址
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     When this value is set, deepflow-agent will use this IP to access the
    #     data plane port of deepflow-server, which is usually used when
    #     deepflow-server uses an external load balancer.
    #   ch: |-
    #     用于设置 deepflow-server 向 deepflow-agent 下发的 server 端数据面通信 IP。
    # upgrade_from: analyzer_ip
    ingester_ip: ""
    # type: int
    # name:
    #   en: Ingester Port
    #   ch: Ingester 端口号
    # unit:
    # range: [1, 65535]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The data plane port used by deepflow-agent to access deepflow-server.
    #     The default port within the same K8s cluster is 20033, and the default port
    #     of deepflow-agent outside the cluster is 30033.
    #   ch: |-
    #     用于设置 deepflow-server 向 deepflow-agent 下发的 server 端数据面通信端口。
    # upgrade_from: analyzer_port
    ingester_port: 30033
    # type: int
    # name:
    #   en: gRPC Socket Buffer Size
    #   ch: gRPC Socket 缓冲区大小
    # unit: MiB
    # range: [5, 1024]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     gRPC socket buffer size.
    #   ch: |-
    #     设置 deepflow-agent 的 gRPC socket 缓冲区大小。
    # upgrade_from: static_config.grpc-buffer-size
    # deprecated: true
    grpc_buffer_size: 5
    # type: int
    # name:
    #   en: Max Throughput To Ingester
    #   ch: 发送到 Ingester 的最大流量
    # unit: Mbps
    # range: [0, 10000]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The maximum allowed flow rate for sending observability data to the server-side Ingester module.
    #     For the overflow action, refer to the `ingester_traffic_overflow_action` configuration description.
    #     Setting it to 0 means no speed limit.
    #   ch: |-
    #     向 Server 端 Ingester 模块发送可观测性数据的最大允许流量,
    #     超限行为参考 `ingester_traffic_overflow_action` 配置描述。
    #     配置为 0 表示不限速。
    max_throughput_to_ingester: 100
    # type: string
    # name:
    #   en: Action when the Ingester traffic exceeds the limit
    #   ch: Ingester 流量超限的动作
    # unit:
    # range: []
    # enum_options: [WAIT, DROP]
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     Action when the Ingester traffic exceeds the limit
    #     - WAIT: pause sending, cache data into queue, and wait for next sending
    #     - DROP: the data is discarded directly and the Agent `DATA_BPS_THRESHOLD_EXCEEDED` exception is triggered
    #   ch: |-
    #     Ingester 流量超限的动作
    #     - WAIT：暂停发送，数据缓存到队列，等待下次发送。
    #     - DROP：直接丢弃数据，并触发 Agent `数据流量达到限速`异常。
    ingester_traffic_overflow_action: WAIT
    # type: bool
    # name:
    #   en: Request via NAT IP Address
    #   ch: 请求 NAT IP 地址
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     Used when deepflow-agent uses an external IP address to access
    #     deepflow-server. For example, when deepflow-server is behind a NAT gateway,
    #     or the host where deepflow-server is located has multiple node IP addresses
    #     and different deepflow-agents need to access different node IPs, you can
    #     set an additional NAT IP for each deepflow-server address, and modify this
    #     value to `true`.
    #   ch: |-
    #     当 deepflow-agent 使用外部 IP 地址访问 deepflow-server 时，
    #     例如，当 deepflow-server 位于 NAT 网关后，或 deepflow-server 所在的主机有多个
    #     节点 IP 地址，不同的 deepflow-agent 需要访问不同的节点 IP 地址时，可以为每个
    #     deepflow-server 地址设置一个额外的 NAT IP，并将本参数设置为 `true`。
    # upgrade_from: nat_ip_enabled
    request_via_nat_ip: false
  # type: section
  # name:
  #   en: Self Monitoring
  #   ch: 自监控
  # description:
  #   en: Configuration of deepflow-agent's own diagnosis.
  #   ch: 配置 deepflow-agent 自身诊断相关的参数。
  self_monitoring:
    # type: section
    # name:
    #  en: Log
    #  ch: 日志
    # description:
    #   en: Configuration of deepflow-agent's own logs.
    #   ch: deepflow-agent 自身日志的相关配置参数。
    log:
      # type: string
      # name:
      #   en: Log Level
      #   ch: 日志等级
      # unit:
      # range: []
      # enum_options: [DEBUG, INFO, WARN, ERROR]
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Log level of deepflow-agent.
      #
      #     It is also possible to specify the log level for specific modules with advanced configuation in the following format:
      #
      #     ```
      #     <log_level_spec> ::= single_log_level_spec[{,single_log_level_spec}][/<text_filter>]
      #     <single_log_level_spec> ::= <path_to_module>|<log_level>|<path_to_module>=<log_level>
      #     <text_filter> ::= <regex>
      #     ```
      #
      #     For example:
      #
      #     ```
      #     log_level: info,deepflow_agent::rpc::session=debug
      #     ```
      #
      #     will set the log level to INFO for all modules and DEBUG for the rpc::session module.
      #
      #   ch: |-
      #     deepflow-agent 运行日志输出等级。
      #
      #     也可以通过高级配置指定特定模块的日志等级，格式如下：
      #
      #     ```
      #     <log_level_spec> ::= single_log_level_spec[{,single_log_level_spec}][/<text_filter>]
      #     <single_log_level_spec> ::= <path_to_module>|<log_level>|<path_to_module>=<log_level>
      #     <text_filter> ::= <regex>
      #     ```
      #
      #     例如：
      #
      #     ```
      #     log_level: info,deepflow_agent::rpc::session=debug
      #     ```
      #
      #     将设置所有模块的日志等级为 INFO，并将 rpc::session 模块的日志等级设置为 DEBUG。
      #
      # upgrade_from: log_level
      log_level: INFO
      # type: string
      # name:
      #   en: Log File
      #   ch: 日志文件
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The file where deepflow-agent logs are written.
      #     Note that this configuration is only used in standalone mode.
      #   ch: |-
      #     deepflow-agent 运行日志的写入位置。
      # upgrade_from: static_config.log-file
      log_file: /var/log/deepflow-agent/deepflow-agent.log
      # type: bool
      # name:
      #   en: Log Backhaul Enabled
      #   ch: 启用日志回传
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When enabled, deepflow-agent will send its own logs to deepflow-server.
      #   ch: |-
      #     开启后，deepflow-agent 将向 deepflow-server 回传运行日志。
      # upgrade_from: rsyslog_enabled
      log_backhaul_enabled: true
    # type: section
    # name:
    #   en: Profile
    #   ch: 持续剖析
    # description:
    #   en:
    #   ch: deepflow-agent 自身持续剖析数据配置参数
    profile:
      # type: bool
      # name: Enabled
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Only available for Trident (Golang version of Agent).
      #   ch: |-
      #     该参数仅对 deepflow-trident 有效，对 deepflow-agent 无效。
      #     开启后，支持对 Trident 持续剖析。
      # upgrade_from: static_config.profiler
      # deprecated: true
      enabled: false
    # type: section
    # name:
    #   en: Debug
    #   ch: 诊断
    # description:
    #   en:
    #   ch: deepflow-agent 的诊断功能配置参数
    debug:
      # type: bool
      # name: Enabled
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: Disabled / Enabled the debug function of the deepflow-agent.
      #   ch: 禁用 / 启用 deepflow-agent 的诊断功能。
      # upgrade_from: debug_enabled
      enabled: true
      # type: int
      # name:
      #   en: Local UDP Port
      #   ch: 本地 UDP 端口号
      # unit:
      # range: [0, 65535]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Default value `0` means use a random listen port number.
      #     Only available for Trident (Golang version of Agent).
      #   ch: |-
      #     该参数仅对 deepflow-trident 有效，对 deepflow-agent 无效。用于配置
      #     trident 用于诊断的 UDP 监听端口，默认值为 `0` ，表示使用随机的端口。
      # upgrade_from: static_config.debug-listen-port
      local_udp_port: 0
      # type: bool
      # name:
      #   en: Debug Metrics Enabled
      #   ch: 启用调试指标
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Only available for Trident (Golang version of Agent).
      #   ch: |-
      #     该参数仅对 deepflow-trident 有效，对 deepflow-agent 无效。
      # upgrade_from: static_config.enable-debug-stats
      # deprecated: true
      debug_metrics_enabled: false
    # type: duration
    # name: Interval
    # unit:
    # range: [1s, 3600s]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: statsd interval.
    #   ch: statsd 时间间隔。
    # upgrade_from: stats_interval
    interval: 10s
  # type: section
  # name:
  #   en: Standalone Mode
  #   ch: 独立运行模式
  # description:
  #   en: Configuration of deepflow-agent standalone mode.
  #   ch: deepflow-agent 独立运行模式的相关参数
  standalone_mode:
    # type: int
    # name:
    #   en: Maximum Data File Size
    #   ch: 最大数据文件大小
    # unit: MiB
    # range: [1, 1000000]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     When deepflow-agent runs in standalone mode, it will not be controlled by
    #     deepflow-server, and the collected data will only be written to the local file.
    #     Currently supported data types for writing are l4_flow_log and l7_flow_log. Each
    #     type of data is written to a separate file. This configuration can be used to
    #     specify the maximum size of the data file, and rotate when it exceeds this size.
    #     A maximum of two files are kept for each type of data.
    #   ch: |-
    #     独立运行模式下，单个数据文件的最大值，当文件大小超过最大值时，数据将滚动覆盖。
    #     deepflow-agent 在独立运行模式下不受 deepflow-server 管理/控制，会将采集数据以文件
    #     形式保存在本地磁盘中。目前支持 2 种数据：l4_flow_log 和 l7_flow_log，每种数据分开写入
    #     不同的数据文件，每种数据最多可产生 2 个数据文件。
    # upgrade_from: static_config.standalone-data-file-size
    max_data_file_size: 200
    # type: string
    # name:
    #   en: Data File Directory
    #   ch: 数据文件目录
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Directory where data files are written to.
    #   ch: |-
    #     数据文件的写入位置。
    # upgrade_from: static_config.standalone-data-file-dir
    data_file_dir: /var/log/deepflow-agent/

# type: section
# name:
#   en: Inputs
#   ch: 输入
# description:
#   en:
#   ch:
inputs:
  # type: section
  # name:
  #   en: Proc
  #   ch: 进程
  # description:
  proc:
    # type: bool
    # name: Enabled
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     After enabling this configuration, deepflow-agent will periodically report the process information
    #     specified in `inputs.proc.process_matcher` to deepflow-server. After synchronizing process information,
    #     all eBPF observability data will automatically inject the global process ID (gprocess_id) tag.
    #
    #     Note: When enabling this feature, the specific process list must also be specified in `inputs.proc.process_matcher`,
    #     i.e., `proc.gprocess_info` must be included in `inputs.proc.process_matcher.[*].enabled_features`.
    #
    #     This configuration only applies to agents of `cloud server` types (CHOST_VM, CHOST_BM) and `container`
    #     types (K8S_VM, K8S_BM). Use the command `deepflow-ctl agent list` to determine the specific agent
    #     type in CLI environments.
    #   ch: |-
    #     开启此配置后，deepflow-agent 会周期性将 `inputs.proc.process_matcher` 中指定的进程信息上报给 deepflow-server。
    #     同步进程信息后，所有的 eBPF 观测数据都会自动注入`全局进程 ID`（`gprocess_id`）标签。
    #
    #     注意：开启此功能时，需要同时在 `inputs.proc.process_matcher` 中进一步指定具体的进程列表，
    #     即 `inputs.proc.process_matcher.[*].enabled_features` 中需要包含 `proc.gprocess_info`。
    #
    #     该参数仅对`云服务器`（CHOST_VM、CHOST_BM）和`容器`（K8S_VM、K8S_BM）类型的 agent 有效，
    #     在命令行下使用 `deepflow-ctl agent list` 可确定 agent 的具体类型。
    # upgrade_from: static_config.os-proc-sync-enabled
    enabled: true
    # type: string
    # name:
    #   en: Directory of /proc
    #   ch: /proc 目录
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     The /proc fs mount path.
    #   ch: |-
    #     进程信息同步所用的目录。
    # upgrade_from: static_config.os-proc-root
    proc_dir_path: /proc
    # type: duration
    # name:
    #   en: Socket Information Synchronization Interval
    #   ch: Socket 信息同步间隔
    # unit:
    # range: [0ns, 1h]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Synchronization interval for process Socket information.
    #
    #     '0ns' means disabled, do not configure a value less than `1s` except for 0.
    #
    #     Note: When enabling this feature, the specific process list must also be specified in `inputs.proc.process_matcher`,
    #     i.e., `inputs.proc.socket_info_sync_interval` must be included in `inputs.proc.process_matcher.[*].enabled_features`.
    #     Additionally, ensure `inputs.proc.enabled` is configured to **true**.
    #   ch: |-
    #     进程 Socket 信息的同步周期。
    #
    #     '0ns' 表示不开启，除 '0ns' 外不要配置小于 `1s` 的值。
    #
    #     注意：开启此功能时，需要同时在 `inputs.proc.process_matcher` 中进一步指定具体的进程列表，
    #     即 `inputs.proc.process_matcher.[*].enabled_features` 中需要包含 `inputs.proc.socket_info_sync_interval`。
    #     另外，也要注意确认 `inputs.proc.enabled` 已配置为 **true**。
    # upgrade_from: static_config.os-proc-socket-sync-interval
    socket_info_sync_interval: 0ns
    # type: duration
    # name:
    #   en: Minimal Lifetime
    #   ch: 最小活跃时间
    # unit:
    # range: [1s, 1h]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Socket and Process will not be reported if their uptime is lower than this threshold.
    #   ch: |-
    #     如果接口或进程的活跃时间低于该参数值，deepflow-agent 将不上报该接口或进程的信息。
    # upgrade_from: static_config.os-proc-socket-min-lifetime
    min_lifetime: 3s
    # type: section
    # name:
    #   en: Tag Extraction
    #   ch: Tag 提取
    # description:
    tag_extraction:
      # type: string
      # name:
      #   en: Script Command
      #   ch: 脚本命令
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Execute the command every time when scan the process, expect get the process tag
      #     from stdout in yaml format, the example yaml format as follow:
      #     ```yaml
      #     - pid: 1
      #       tags:
      #       - key: xxx
      #         value: xxx
      #     - pid: 2
      #       tags:
      #       - key: xxx
      #         value: xxx
      #     ```
      #     Example configuration:
      #     ```yaml
      #     inputs:
      #       proc:
      #         tag_extraction:
      #           script_command: ["cat", "/tmp/tag.yaml"]
      #     ```
      #   ch: |-
      #     deepflow-agent 每次采集进程信息时，会执行配置的脚本命令，并从标准输出的 yaml 格式
      #     中尝试获取进程的标签字段。yaml 格式的样例如下：
      #     ```yaml
      #     - pid: 1
      #       tags:
      #       - key: xxx
      #         value: xxx
      #     - pid: 2
      #       tags:
      #       - key: xxx
      #         value: xxx
      #     ```
      #     配置样例:
      #     ```yaml
      #     inputs:
      #       proc:
      #         tag_extraction:
      #           script_command: ["cat", "/tmp/tag.yaml"]
      #     ```
      # upgrade_from: static_config.os-app-tag-exec
      script_command: []
      # type: string
      # name:
      #   en: Execution Username
      #   ch: 执行用户名
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The user who should execute the `script_command` command.
      #   ch: |-
      #     deepflow-agent 执行 `script_command` 脚本命令的用户名。
      # upgrade_from: static_config.os-app-tag-exec-user
      exec_username: deepflow
    # type: string
    # name:
    #   en: Process Blacklist
    #   ch: 进程黑名单
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     The list of processe names ignored by process matcher.
    #   ch: |-
    #     进程匹配器忽略的进程列表。
    process_blacklist: [sleep, sh, bash, pause, runc, grep, awk, sed, curl]
    # type: dict
    # name:
    #   en: Process Matcher
    #   ch: 进程匹配器
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   ch: |-
    #     用于指定为特定进程开启的高级功能列表。
    #
    #     匹配器将自上而下地遍历匹配规则，所以较前的规则将会被优先匹配。
    #     当 match_type 为 parent_process_name 时，匹配器将会递归地查找父进程名且忽略 rewrite_name 选项。
    #     rewrite_name 可定义为正则表达式捕获组索引，或 windows 风格的环境变量。
    #     例如：`$1-py-script-%HOSTNAME%` 中的 $1 将会替换正则表达式捕获到的第一组内容，并替换 HOSTNAME 环境变量。
    #
    #     配置键：
    #     - match_regex: 用于匹配进程的表达式，缺省值为 `""`。
    #     - match_type: 被用于正则表达式匹配的对象，缺省值为 `process_name`，可选项为：
    #       [process_name, cmdline, cmdline_with_args, parent_process_name, tag]
    #     - ignore: 是否要忽略匹配到的进程，缺省值为 `false`
    #     - rewrite_name: 使用正则替换匹配到的进程名或命令行，缺省值为 `""` 表示不做替换。
    #     - enabled_features: 为匹配到的进程开启的特性列表，可选项如下
    #       - proc.gprocess_info（注意确认 `inputs.proc.enabled` 已配置为 **true**）
    #       - proc.golang_symbol_table（注意确认 `inputs.proc.symbol_table.golang_specific.enabled` 已配置为 **true**）
    #       - proc.socket_list（注意确认 `inputs.proc.socket_info_sync_interval` 已配置为**大于 0 的数字**）
    #       - ebpf.socket.uprobe.golang（注意确认 `inputs.ebpf.socket.uprobe.golang.enabled` 已配置为 **true**）
    #       - ebpf.socket.uprobe.tls（注意确认 `inputs.ebpf.socket.uprobe.tls.enabled` 已配置为 **true**）
    #       - ebpf.profile.on_cpu（注意确认 `inputs.ebpf.profile.on_cpu.disabled` 已配置为 **false**）
    #       - ebpf.profile.off_cpu（注意确认 `inputs.ebpf.profile.off_cpu.disabled` 已配置为 **false**）
    #       - ebpf.profile.memory（注意确认 `inputs.ebpf.profile.memory.disabled` 已配置为 **false**）
    #
    #     示例:
    #     ```yaml
    #     inputs:
    #       proc:
    #         process_matcher:
    #         - match_regex: python3 (.*)\.py
    #           match_type: cmdline
    #           match_languages: []
    #           match_usernames: []
    #           only_in_container: true
    #           only_with_tag: false
    #           ignore: false
    #           rewrite_name: $1-py-script
    #           enabled_features: [ebpf.socket.uprobe.golang, ebpf.profile.on_cpu]
    #         - match_regex: (?P<PROC_NAME>nginx)
    #           match_type: process_name
    #           rewrite_name: ${PROC_NAME}-%HOSTNAME%
    #         - match_regex: "nginx"
    #           match_type: parent_process_name
    #           ignore: true
    #         - match_regex: .*sleep.*
    #           match_type: process_name
    #           ignore: true
    #         - match_regex: .+ # 可使用冒号连接需要匹配的 tag key 与 value
    #                           # i.e.: `app:.+` 表示匹配所有具有 `app` tag 的进程
    #           match_type: tag
    #     ```
    #   en: |-
    #     List of advanced features enabled for specific processes.
    #
    #     Will traverse over the entire array, so the previous ones will be matched first.
    #     when match_type is parent_process_name, will recursive to match parent proc name,
    #     and rewrite_name field will ignore. rewrite_name can replace by regexp capture group
    #     and windows style environment variable, for example: `$1-py-script-%HOSTNAME%` will
    #     replace regexp capture group 1 and HOSTNAME env var.
    #
    #     Configuration Item:
    #     - match_regex: The regexp use for match the process, default value is `""`
    #     - match_type: regexp match field, default value is `process_name`, options are
    #       [process_name, cmdline, cmdline_with_args, parent_process_name, tag]
    #     - ignore: Whether to ignore matched processes, default value is `false`
    #     - rewrite_name: The name will replace the process name or cmd use regexp replace.
    #       Default value `""` means no replacement.
    #     - enabled_features: List of features enabled for matched processes. Available options:
    #       - proc.gprocess_info (Ensure `inputs.proc.enabled` is configured to **true**)
    #       - proc.golang_symbol_table (Ensure `inputs.proc.symbol_table.golang_specific.enabled` is configured to **true**)
    #       - proc.socket_list (Ensure `inputs.proc.socket_info_sync_interval` is configured to a **number > 0**)
    #       - ebpf.socket.uprobe.golang (Ensure `inputs.ebpf.socket.uprobe.golang.enabled` is configured to **true**)
    #       - ebpf.socket.uprobe.tls (Ensure `inputs.ebpf.socket.uprobe.tls.enabled` is configured to **true**)
    #       - ebpf.profile.on_cpu (Ensure `inputs.ebpf.profile.on_cpu.disabled` is configured to **false**)
    #       - ebpf.profile.off_cpu (Ensure `inputs.ebpf.profile.off_cpu.disabled` is configured to **false**)
    #       - ebpf.profile.memory (Ensure `inputs.ebpf.profile.memory.disabled` is configured to **false**)
    #
    #     Example:
    #     ```yaml
    #     inputs:
    #       proc:
    #         process_matcher:
    #         - match_regex: python3 (.*)\.py
    #           match_type: cmdline
    #           match_languages: []
    #           match_usernames: []
    #           only_in_container: true
    #           only_with_tag: false
    #           ignore: false
    #           rewrite_name: $1-py-script
    #           enabled_features: [ebpf.socket.uprobe.golang, ebpf.profile.on_cpu]
    #         - match_regex: (?P<PROC_NAME>nginx)
    #           match_type: process_name
    #           rewrite_name: ${PROC_NAME}-%HOSTNAME%
    #         - match_regex: "nginx"
    #           match_type: parent_process_name
    #           ignore: true
    #         - match_regex: .*sleep.*
    #           match_type: process_name
    #           ignore: true
    #         - match_regex: .+ # match after concatenating a tag key and value pair using colon,
    #                           # i.e., an regex `app:.+` can match all processes has a `app` tag
    #           match_type: tag
    #     ```
    # upgrade_from: static_config.os-proc-regex
    # ---
    # type: string
    # name:
    #   en: Match Regex
    #   ch: 匹配正则表达式
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     The regex of matcher.
    #   ch: |-
    #     匹配正则表达式。
    # upgrade_from: static_config.os-proc-regex.match-regex
    # ---
    # match_regex: ""
    # ---
    # type: string
    # name:
    #   en: Match Type
    #   ch: 匹配类型
    # unit:
    # range: []
    # enum_options: [process_name, cmdline, cmdline_with_args, parent_process_name, tag]
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     The type of matcher.
    #   ch: |-
    #     匹配类型。
    # upgrade_from: static_config.os-proc-regex.match-type
    # ---
    # match_type: process_name
    # ---
    # type: string
    # name:
    #   en: Match Languages
    #   ch: 匹配语言
    # unit:
    # range: []
    # enum_options: [java, golang, python, nodejs, dotnet]
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Default value `[]` match all languages.
    #   ch: |-
    #     默认值 `[]` 匹配所有语言。
    # ---
    # match_languages: []
    # ---
    # type: string
    # name:
    #   en: Match Usernames
    #   ch: 匹配用户名
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Default value `[]` match all usernames.
    #   ch: |-
    #     默认值 `[]` 匹配所有语言。
    # ---
    # match_usernames: []
    # ---
    # type: bool
    # name:
    #   en: Only in Container
    #   ch: 仅匹配容器内的进程
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Default value `true` means only match processes in container.
    #   ch: |-
    #     默认值 `true` 表示仅匹配容器中的进程。
    # ---
    # only_in_container: true
    # ---
    # type: bool
    # name:
    #   en: Only with Tag
    #   ch: 仅匹配有 Tag 的进程
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Default value `false` means match processes with or without tags.
    #   ch: |-
    #     默认值 `false` 表示匹配所有进程。
    # upgrade_from: static_config.os-proc-sync-tagged-only
    # ---
    # only_with_tag: false
    # ---
    # type: bool
    # name:
    #   en: Ignore
    #   ch: 忽略
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Whether to ignore matched processes.
    #   ch: |-
    #     是否忽略匹配的进程。
    # upgrade_from: static_config.os-proc-regex.action
    # ---
    # ignore: false
    # ---
    # type: string
    # name:
    #   en: Rewrite Name
    #   ch: 重命名
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     New name after matched.
    #   ch: |-
    #     匹配后的新名称。
    # upgrade_from: static_config.os-proc-regex.rewrite-name
    # ---
    # rewrite_name: ""
    # ---
    # type: string
    # name:
    #   en: Enabled Features
    #   ch: 开启功能列表
    # unit:
    # range: []
    # enum_options:
    #   - proc.gprocess_info
    #   - proc.golang_symbol_table
    #   - proc.socket_list
    #   #- proc.proc_event
    #   - ebpf.socket.uprobe.golang
    #   - ebpf.socket.uprobe.tls
    #   #- ebpf.socket.uprobe.rdma
    #   #- ebpf.file.io_event
    #   #- ebpf.file.management_event
    #   - ebpf.profile.on_cpu
    #   - ebpf.profile.off_cpu
    #   - ebpf.profile.memory
    #   #- ebpf.profile.cuda
    #   #- ebpf.profile.hbm
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   ch: |-
    #     注意也需要同时开启相关特性的总开关：
    #     - proc.gprocess_info（注意确认 `inputs.proc.enabled` 已配置为 **true**）
    #     - proc.golang_symbol_table（注意确认 `inputs.proc.symbol_table.golang_specific.enabled` 已配置为 **true**）
    #     - proc.socket_list（注意确认 `inputs.proc.socket_info_sync_interval` 已配置为**大于 0 的数字**）
    #     - ebpf.socket.uprobe.golang（注意确认 `inputs.ebpf.socket.uprobe.golang.enabled` 已配置为 **true**）
    #     - ebpf.socket.uprobe.tls（注意确认 `inputs.ebpf.socket.uprobe.tls.enabled` 已配置为 **true**）
    #     - ebpf.profile.on_cpu（注意确认 `inputs.ebpf.profile.on_cpu.disabled` 已配置为 **false**）
    #     - ebpf.profile.off_cpu（注意确认 `inputs.ebpf.profile.off_cpu.disabled` 已配置为 **false**）
    #     - ebpf.profile.memory（注意确认 `inputs.ebpf.profile.memory.disabled` 已配置为 **false**）
    #   en: |-
    #     Also ensure the global configuration parameters for related features are enabled:
    #     - proc.gprocess_info (Ensure `inputs.proc.enabled` is configured to **true**)
    #     - proc.golang_symbol_table (Ensure `inputs.proc.symbol_table.golang_specific.enabled` is configured to **true**)
    #     - proc.socket_list (Ensure `inputs.proc.socket_info_sync_interval` is configured to a **number > 0**)
    #     - ebpf.socket.uprobe.golang (Ensure `inputs.ebpf.socket.uprobe.golang.enabled` is configured to **true**)
    #     - ebpf.socket.uprobe.tls (Ensure `inputs.ebpf.socket.uprobe.tls.enabled` is configured to **true**)
    #     - ebpf.profile.on_cpu (Ensure `inputs.ebpf.profile.on_cpu.disabled` is configured to **false**)
    #     - ebpf.profile.off_cpu (Ensure `inputs.ebpf.profile.off_cpu.disabled` is configured to **false**)
    #     - ebpf.profile.memory (Ensure `inputs.ebpf.profile.memory.disabled` is configured to **false**)
    # upgrade_from: static_config.ebpf.on-cpu-profile.regex, static_config.ebpf.off-cpu-profile.regex
    # ---
    # enabled_features: []
    process_matcher:
      - match_regex: \bjava( +\S+)* +-jar +(\S*/)*([^ /]+\.jar)
        match_type: cmdline_with_args
        only_in_container: false
        rewrite_name: $3
        enabled_features: [ebpf.profile.on_cpu, proc.gprocess_info]
      - match_regex: \bpython(\S)*( +-\S+)* +(\S*/)*([^ /]+)
        match_type: cmdline_with_args
        only_in_container: false
        rewrite_name: $4
        enabled_features: [ebpf.profile.on_cpu, proc.gprocess_info]
      - match_regex: ^deepflow-
        only_in_container: false
        enabled_features: [ebpf.profile.on_cpu, proc.gprocess_info]
      - match_regex: .*
        enabled_features: [proc.gprocess_info]
    # type: section
    # name:
    #   en: Symbol Table
    #   ch: 符号表
    # description:
    symbol_table:
      # type: section
      # name:
      #   en: Golang-specific
      #   ch: Golang 特有
      # description:
      golang_specific:
        # type: bool
        # name:
        #   en: Enabled
        #   ch: Enabled
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Whether to enable Golang-specific symbol table parsing.
        #
        #     This feature acts on Golang processes that have trimmed the standard symbol
        #     table. When this feature is enabled, for processes with Golang
        #     version >= 1.13 and < 1.18, when the standard symbol table is missing, the
        #     Golang-specific symbol table will be parsed to complete uprobe data collection.
        #     Note that enabling this feature may cause the eBPF initialization process to
        #     take ten minutes.
        #
        #     Example:
        #     - You've encountered the following warning log:
        #       ```
        #       [eBPF] WARNING: func resolve_bin_file() [user/go_tracer.c:558] Go process pid 1946
        #       [path: /proc/1946/root/usr/local/bin/kube-controller-manager] (version: go1.16). Not find any symbols!
        #       ```
        #       Suppose there is a Golang process with a process ID of '1946.'
        #     - To initially confirm whether the executable file for this process has a symbol table:
        #       - Retrieve the executable file's path using the process ID:
        #         ```
        #         # ls -al /proc/1946/exe
        #         /proc/1946/exe -> /usr/local/bin/kube-controller-manager
        #         ```
        #       - Check if there is a symbol table:
        #         ```
        #         # nm /proc/1946/root/usr/local/bin/kube-controller-manager
        #         nm: /proc/1946/root/usr/local/bin/kube-controller-manager: no symbols
        #         ```
        #     - If "no symbols" is encountered, it indicates the absence of a symbol table. In such a
        #       scenario, we need to configure the "golang-symbol" setting.
        #     - During the agent startup process, you will observe the following log information: (The entry
        #       address for the function `crypto/tls.(*Conn).Write` has already been resolved, i.e., entry:0x25fca0).
        #       ```
        #       [eBPF] INFO Uprobe [/proc/1946/root/usr/local/bin/kube-controller-manager] pid:1946 go1.16.0
        #       entry:0x25fca0 size:1952 symname:crypto/tls.(*Conn).Write probe_func:uprobe_go_tls_write_enter rets_count:0
        #       ```
        #       The logs indicate that the Golang program has been successfully hooked.
        #
        #     Note: When enabling this feature, the specific process list must also be specified in `inputs.proc.process_matcher`,
        #     i.e., `proc.golang_symbol_table` must be included in `inputs.proc.process_matcher.[*].enabled_features`.
        #   ch: |-
        #     是否开启 Golang 特有符号表的解析能力。
        #
        #     如果 Golang（版本 >= 1.13 and < 1.18条件下）进程运行时裁切了标准符号
        #     表，开启此开关后 deepflow-agent 将解析生成 Golang-specific 符号表以
        #     完善 eBPF uprobe 数据，实现 Golang 程序的零侵扰调用链追踪。注意：开启
        #     该开关后，eBPF 程序初始化过程可能会持续 10 分钟以上的时间。
        #
        #     配置方法：
        #     - 如果在 deepflow-agent 的运行日志中发现如下 warning：
        #       ```
        #       [eBPF] WARNING: func resolve_bin_file() [user/go_tracer.c:558] Go process pid 1946
        #       [path: /proc/1946/root/usr/local/bin/kube-controller-manager] (version: go1.16). Not find any symbols!
        #       ```
        #       以上日志说明存在一个 PID = 1946 的 Golang 进程。
        #     - 确认是否 Golang 进程是否已有符号表:
        #       - 通过 PID 获取程序可执行文件的目录:
        #         ```
        #         # ls -al /proc/1946/exe
        #         /proc/1946/exe -> /usr/local/bin/kube-controller-manager
        #         ```
        #       - 检查目录下是否有符号表：
        #         ```
        #         # nm /proc/1946/root/usr/local/bin/kube-controller-manager
        #         nm: /proc/1946/root/usr/local/bin/kube-controller-manager: no symbols
        #         ```
        #     - 如果结果中出现 "no symbols"，则说明符号表缺失，需要开启 Golang 程序符号表解析开关.
        #     - deepflow-agent 启动阶段运行日志中出现类似下面的信息，说明 Golang 进程已经被成功 hook。
        #       ```
        #       [eBPF] INFO Uprobe [/proc/1946/root/usr/local/bin/kube-controller-manager] pid:1946 go1.16.0
        #       entry:0x25fca0 size:1952 symname:crypto/tls.(*Conn).Write probe_func:uprobe_go_tls_write_enter rets_count:0
        #       ```
        #
        #     注意：开启此功能时，需要同时在 `inputs.proc.process_matcher` 中进一步指定具体的进程列表，
        #     即 `inputs.proc.process_matcher.[*].enabled_features` 中需要包含 `proc.golang_symbol_table`。
        # upgrade_from: static_config.ebpf.uprobe-process-name-regexs.golang-symbol
        enabled: false
      # type: section
      # name: Java
      java:
        # type: duration
        # name:
        #   en: Refresh Defer Duration
        #   ch: 刷新延迟时长
        # unit:
        # range: [5s, 3600s]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     When the `deepflow-agent` detects unresolved function names in the Java process call stack, it
        #     triggers the generation of the process function symbol table and updates the symbol cache. Currently,
        #     the Java symbol file is continuously updated, and the `duration` is used to control the delay in
        #     updating the symbol cache with the new symbol file. This delay is necessary because Java uses a JIT
        #     (Just-In-Time) compilation mechanism, which requires a warm-up phase for symbol generation. To obtain
        #     more complete Java symbols, the update of the Java symbol cache is deferred. This approach also helps
        #     avoid frequent symbol cache refreshes due to missing symbols, which could otherwise result in significant
        #     CPU resource consumption.
        #   ch: |-
        #     当 deepflow-agent 在 Java 进程的函数调用栈中发现未能解析的函数名时，将触发进程函数符号表的生成和符号缓存
        #     的更新。当前 Java 符号文件是采用持续更新的方式，该 duration 用于控制推迟使用符号文件更新符号缓存的时间。
        #     原因是由于 Java 使用了 JIT 编译机制，编译符号生成有个预热阶段，为了获取更充足的 Java 符号需要推迟一段时间
        #     来更新 Java 符号的缓存，也可避免由于符号缺失而造成的频繁符号缓存刷新引起大量CPU资源消耗。
        #
        # upgrade_from: static_config.ebpf.java-symbol-file-refresh-defer-interval
        refresh_defer_duration: 60s
        # type: int
        # name:
        #   en: Maximum Symbol File Size
        #   ch: 符号表文件最大大小
        # unit: MiB
        # range: [2, 100]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     All Java symbol files are stored in the '/tmp' directory mounted by the deepflow-agent.
        #     To prevent excessive occupation of host node space due to large Java symbol files, a
        #     maximum size limit is set for each generated Java symbol file.
        #   ch: |-
        #     deepflow-agent 将所有的 Java 符号表文件存放在'/tmp'目录下，该参数用于限制每一个 Java 符号表文件的
        #     大小上限，以避免占用过多的节点磁盘空间。
        # upgrade_from: static_config.ebpf.java-symbol-file-max-space-limit
        # deprecated: true
        max_symbol_file_size: 10
  # type: section
  # name: cBPF
  # description:
  cbpf:
    # type: section
    # name:
    #   en: Common
    #   ch: 通用配置
    # description:
    common:
      # type: int
      # name:
      #   en: Packet Capture Mode
      #   ch: Packet 采集模式
      # unit:
      # range: []
      # enum_options:
      #   - 0:
      #       en: Local
      #       ch: 本地流量
      #   - 1:
      #       en: Virtual Mirror
      #       ch: 虚拟网络镜像
      #   - 2:
      #       en: Physical Mirror
      #       ch: 物理网络镜像
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     `Virtual Mirror` mode is used when deepflow-agent cannot directly capture the traffic from
      #     the source. For example:
      #     - in the K8s macvlan environment, capture the Pod traffic through the Node NIC
      #     - in the Hyper-V environment, capture the VM traffic through the Hypervisor NIC
      #     - in the ESXi environment, capture traffic through VDS/VSS local SPAN
      #     - in the DPDK environment, capture traffic through DPDK ring buffer
      #
      #     Use `Physical Mirror` mode when deepflow-agent captures traffic through physical
      #     switch mirroring.
      #
      #     <mark>`Physical Mirror` is only supported in the Enterprise Edition.</mark>
      #   ch: |-
      #     `虚拟网络镜像`模式用于 deepflow-agent 无法直接采集流量的场景，比如：
      #     - k8s 的 macvlan 环境中，从 Node 网口接口采集 POD 流量；
      #     - Hyper-V 环境中，从宿主机的网络接口采集 VM 流量；
      #     - ESXi 环境中，通过 VDS/VSS 的本地 SPAN 采集 VM 流量；
      #     - DPDK 环境中，通过 DPDK ring buffer 采集 VM 流量。
      #
      #     `物理网络镜像`模式（仅企业版支持）用于 deepflow-agent 从物理设备镜像采集流量的场景。
      # upgrade_from: tap_mode
      capture_mode: 0
    # type: section
    # name:
    #   en: Capture via AF_PACKET
    #   ch: 使用 AF_PACKET 采集
    # description:
    #   en:
    af_packet:
      # type: string
      # name:
      #   en: Interface Regex
      #   ch: 网卡名正则表达式
      # unit:
      # range: [0, 65535]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Regular expression of NIC name for collecting traffic.
      #
      #     Explanation of the default configuration:
      #     ```
      #     Localhost:     lo
      #     Common NIC:    eth.*|en[osipx].*
      #     QEMU VM NIC:   tap.*
      #     Flannel:       veth.*
      #     Calico:        cali.*
      #     Cilium         lxc.*
      #     Kube-OVN       [0-9a-f]+_h$
      #     ```
      #     When it is not configured, it indicates
      #     that network card traffic is not being collected
      #   ch: |-
      #     需要采集流量的网络接口的正则表达式。
      #
      #     默认配置说明:
      #     ```
      #     Localhost:     lo
      #     Common NIC:    eth.*|en[osipx].*
      #     QEMU VM NIC:   tap.*
      #     Flannel:       veth.*
      #     Calico:        cali.*
      #     Cilium         lxc.*
      #     Kube-OVN       [0-9a-f]+_h$
      #     ```
      #     未配置时，表示未采集网卡流量
      # upgrade_from: tap_interface_regex
      interface_regex: ^(tap.*|cali.*|veth.*|eth.*|en[osipx].*|lxc.*|lo|[0-9a-f]+_h)$
      # type: bool
      # name:
      #   en: Inner Net Namespace Capture Enabled
      #   ch: 内网络命名空间采集开关
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Whether to collect traffic in sub net namespaces.
      #     When enabled, agent will spawn recv engine threads to capture traffic in different namespaces,
      #     causing additional memory consumption for each namespace captured.
      #     The default setting of `inputs.cbpf.af_packet.tunning.ring_blocks` is 128,
      #     which means that the memory consumption will be 128 * 1MB for each namespace.
      #     For example, a node with 20 pods will require 20 * 128 * 1MB = 2.56GB for dispatcher.
      #     Make sure to estimate this memory consumption before enabling this feature.
      #     Enabling `inputs.cbpf.af_packet.tunning.ring_blocks_enabled` and change
      #     `inputs.cbpf.af_packet.tunning.ring_blocks` to reduce memory consumption.
      #   ch: |-
      #     是否采集内网络命名空间流量。
      #     设置为启用会使采集器为每个内网络命名空间创建一个独立的接收引擎线程，
      #     这会导致额外的内存消耗。
      #     默认配置 `inputs.cbpf.af_packet.tunning.ring_blocks` 为 128，
      #     这意味着每个网络命名空间将消耗 128 * 1MB 的内存。
      #     一个有 20 个 POD 的节点将需要 20 * 128 * 1MB = 2.56GB 的内存。
      #     请在启用此功能之前估计内存消耗，启用 `inputs.cbpf.af_packet.tunning.ring_blocks_enabled`
      #     并调整 `inputs.cbpf.af_packet.tunning.ring_blocks` 以减少内存消耗。
      # upgrade_from:
      inner_interface_capture_enabled: false
      # type: string
      # name:
      #   en: Inner Net Namespace Interface Regex
      #   ch: 内网络命名空间网卡名正则表达式
      # unit:
      # range: [0, 65535]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Regular expression of NIC name for collecting traffic in sub net namespaces.
      #   ch: |-
      #     需要采集流量的内网络命名空间网卡的正则表达式。
      # upgrade_from:
      inner_interface_regex: ^eth\d+$
      # type: dict
      # name:
      #   en: Bond Interfaces
      #   ch: Bond 网卡列表
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Packets of interfaces in the same group can be aggregated together,
      #     Only effective when `inputs.cbpf.common.capture_mode` is 0.
      #
      #     Example:
      #     ```yaml
      #     inputs:
      #       cbpf:
      #         af_packet:
      #           bond_interfaces:
      #           - slave_interfaces: [eth0, eth1]
      #           - slave_interfaces: [eth2, eth3]
      #     ```
      #   ch: |-
      #     同一组内接口的数据包可以聚合在一起，
      #     仅当 `inputs.cbpf.common.capture_mode` 为0时有效。
      #
      #     例子:
      #     ```yaml
      #     inputs:
      #       cbpf:
      #         af_packet:
      #           bond_interfaces:
      #           - slave_interfaces: [eth0, eth1]
      #           - slave_interfaces: [eth2, eth3]
      #     ```
      # upgrade_from: static_config.tap-interface-bond-groups
      # ---
      # type: string
      # name:
      #   en: Slave Interfaces
      #   ch: Slave 网卡列表
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The slave interfaces of one bond interface.
      #   ch: |-
      #     Bond 网卡的从网卡列表。
      # upgrade_from: static_config.tap-interface-bond-groups.tap-interfaces
      # ---
      # slave_interfaces: []
      bond_interfaces: []
      # type: string
      # name:
      #   en: Extra Network Namespace Regex
      #   ch: 需要采集的额外网络 Namespace
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: true
      # description:
      #   en: |-
      #     Packet will be captured in regex matched namespaces besides the default
      #     namespace. NICs captured in extra namespaces are also filtered with
      #     `inputs.cbpf.af_packet.interface_regex`.
      #
      #     Default value `""` means no extra network namespace (default namespace only).
      #   ch: |-
      #     除默认网络 namespace 之外，deepflow-agent 还会根据此参数正则匹配额外的网络 namespace，
      #     在匹配命中的网络 namespace 中根据`inputs.cbpf.af_packet.interface_regex`正则匹配网络接口并采集流量。默认
      #     配置 `""` 表示仅采集默认网络 namesapce，不采集额外的网络 namespace 流量。
      # upgrade_from: extra_netns_regex
      extra_netns_regex: ""
      # type: string
      # name:
      #   en: Extra BPF Filter
      #   ch: 额外的 BPF 过滤器
      # unit:
      # range: [0, 512]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     If not configured, all traffic will be collected. Please
      #     refer to BPF syntax: [https://biot.com/capstats/bpf.html](https://biot.com/capstats/bpf.html)
      #   ch: |-
      #     如果不配置该参数，则采集全部流量。BPF 语法详见：[https://biot.com/capstats/bpf.html](https://biot.com/capstats/bpf.html)
      # upgrade_from: capture_bpf
      extra_bpf_filter: ""
      # type: string
      # name: TAP Interfaces
      # upgrade_from: static_config.src-interfaces
      # deprecated: true
      src_interfaces: []
      # type: int
      # name:
      #   en: VLAN PCP in Physical Mirror Traffic
      #   ch: 物理网络镜像流量中的 VLAN PCP
      # unit:
      # range: [0, 9]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     - When this configuration <= 7 calculate TAP value from vlan tag only if vlan pcp matches this value.
      #     - when this configuration is 8 calculate TAP value from outer vlan tag,
      #     - when this configuration is 9 calculate TAP value from inner vlan tag.
      #   ch: |-
      #     - 当此配置值小于等于 7 时，仅当 VLAN PCP 与该值匹配时，从 VLAN tag 中计算 TAP。
      #     - 当此配置值为 8 时，从外层 VLAN tag 中计算 TAP，
      #     - 当此配置值为 9 时，从内层 VLAN tag 中计算 TAP。
      # upgrade_from: static_config.mirror-traffic-pcp
      vlan_pcp_in_physical_mirror_traffic: 0
      # type: bool
      # name:
      #   en: BPF Filter Disabled
      #   ch: 禁用 BPF 过滤
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     It is found that there may be bugs in BPF traffic filtering under some
      #     versions of Linux Kernel. After this configuration is enabled, deepflow-agent
      #     will not use the filtering capabilities of BPF, and will filter by itself after
      #     capturing full traffic. Note that this may significantly increase the resource
      #     overhead of deepflow-agent.
      #   ch: |-
      #     此开关用于对特定 Linux 内核版本 BPF 功能诊断，打开此开关后 deepflow-agent 将不启用 Linux
      #     内核的 BPF 包过滤能力，而是获取全流量的数据包之后由采集器程序进行过滤。注意，打开此开关将明显
      #     增加 deepflow-agent 的资源消耗。
      # upgrade_from: static_config.bpf-disabled
      bpf_filter_disabled: false
      # type: bool
      # name:
      #   en: Skip NPB BPF
      #   ch: 跳过 NPB BPF
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     If the NIC on the data plane has ERSPAN tunnel traffic but does not NPB traffic,
      #     enable the switch to collect ERSPAN traffic.
      #   ch: |-
      #     如果采集网卡中有 ERSPAN 流量但是没有分发流量，开启这个开关来采集 ERSPAN 流量。
      # upgrade_from: static_config.skip-npb-bpf
      skip_npb_bpf: false
      # type: section
      # name:
      #   en: Tunning
      #   ch: 调优
      # description:
      tunning:
        # type: int
        # name:
        #   en: Socket Version
        #   ch: Socket 版本
        # unit:
        # range: []
        # enum_options:
        #   - 0:
        #       en: Adaptive
        #       ch: 自适应
        #   - 2: AF_PACKET V2
        #   - 3: AF_PACKET V3
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     AF_PACKET socket version in Linux environment.
        #   ch: |-
        #     deepflow-agent 所在的 Linux 操作系统的 AF_PACKET socket 版本号。
        # upgrade_from: capture_socket_type
        socket_version: 0
        # type: bool
        # name:
        #   en: Ring Blocks Config Enabled
        #   ch: 使能 Ring Blocks 配置
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     When `inputs.cbpf.common.capture_mode` != `Physical Mirror`, you need to explicitly turn on this switch to
        #     configure 'inputs.cbpf.af_packet.tunning.ring_blocks'.
        #   ch: |-
        #     当 `inputs.cbpf.common.capture_mode` 为 `本地流量`或`虚拟网络镜像`模式，需开启此开关，
        #     并配置 `inputs.cbpf.af_packet.tunning.ring_blocks` 参数。
        # upgrade_from: static_config.afpacket-blocks-enabled
        ring_blocks_enabled: false
        # type: int
        # name: Ring Blocks
        # unit:
        # range: [8, 1000000]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     deepflow-agent will automatically calculate the number of blocks
        #     used by AF_PACKET according to max_memory, which can also be specified
        #     using this configuration item. The size of each block is fixed at 1MB.
        #   ch: |-
        #     配置此参数后，deepflow-agent 将分配指定数量的 block 用于 AF_PACKET，每个 block 的
        #     大小固定为 1 MByte。
        # upgrade_from: static_config.afpacket-blocks
        ring_blocks: 128
        # type: int
        # name:
        #   en: Packet Fanout Count
        #   ch: Packet Fanout 路数
        # unit:
        # range: [1, 64]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     The configuration takes effect when `inputs.cbpf.common.capture_mode` is `Local` and `inputs.cbpf.af_packet.extra_netns_regex` is null,
        #     PACKET_FANOUT is to enable load balancing and parallel processing, scaling dispatcher for
        #     better performance of handling network applications. When the `packet_fanout_count`
        #     is greater than 1, multiple dispatcher threads will be launched, consuming more CPU and
        #     memory. Increasing the `packet_fanout_count` helps to reduce the operating system's
        #     software interrupts on multi-core CPU servers.
        #
        #     Attention:
        #     - only valid for `inputs.cbpf.common.capture_mode` = `Local`
        #     - When `self.inputs.cbpf.special_network.dpdk.source` is `eBPF`, this configuration value is forced to be `self.inputs.ebpf.tunning.userspace_worker_threads`
        #   ch: |-
        #     当此配置值大于 1 时，deepflow-agent 将开启多个 dispatcher 线程，并把数据包分散到多个处理
        #     线程并行处理，弹性扩展 dispatcher 以优化网络应用的处理性能。增大此配置可以降低
        #     多核服务器的操作系统软中断数量，但会消耗更多的 CPU 和内存。
        #
        #     注意：
        #     - 参数仅在`inputs.cbpf.common.capture_mode`为 `本地流量`，且`inputs.cbpf.af_packet.extra_netns_regex`为空时有效。
        #     - 当`self.inputs.cbpf.special_network.dpdk.source`为`eBPF`时，这个配置值会被强制置为`self.inputs.ebpf.tunning.userspace_worker_threads`
        # upgrade_from: static_config.local-dispatcher-count
        packet_fanout_count: 1
        # type: int
        # name:
        #   en: Packet Fanout Mode
        #   ch: Packet Fanout 模式
        # unit:
        # range: []
        # enum_options:
        #   - 0: PACKET_FANOUT_HASH
        #   - 1: PACKET_FANOUT_LB
        #   - 2: PACKET_FANOUT_CPU
        #   - 3: PACKET_FANOUT_ROLLOVER
        #   - 4: PACKET_FANOUT_RND
        #   - 5: PACKET_FANOUT_QM
        #   - 6: PACKET_FANOUT_CBPF
        #   - 7: PACKET_FANOUT_EBPF
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     The configuration is a parameter used with the PACKET_FANOUT feature in the Linux
        #     kernel to specify the desired packet distribution algorithm. Refer to:
        #     - [https://github.com/torvalds/linux/blob/afcd48134c58d6af45fb3fdb648f1260b20f2326/include/uapi/linux/if_packet.h#L71](https://github.com/torvalds/linux/blob/afcd48134c58d6af45fb3fdb648f1260b20f2326/include/uapi/linux/if_packet.h#L71)
        #     - [https://www.stackpath.com/blog/bpf-hook-points-part-1/](https://github.com/torvalds/linux/blob/afcd48134c58d6af45fb3fdb648f1260b20f2326/include/uapi/linux/if_packet.h#L71)
        #   ch: |-
        #     数据包 Fanout 的算法/模式。参考：
        #     - [https://github.com/torvalds/linux/blob/afcd48134c58d6af45fb3fdb648f1260b20f2326/include/uapi/linux/if_packet.h#L71](https://github.com/torvalds/linux/blob/afcd48134c58d6af45fb3fdb648f1260b20f2326/include/uapi/linux/if_packet.h#L71)
        #     - [https://www.stackpath.com/blog/bpf-hook-points-part-1/](https://github.com/torvalds/linux/blob/afcd48134c58d6af45fb3fdb648f1260b20f2326/include/uapi/linux/if_packet.h#L71)
        # upgrade_from: static_config.packet-fanout-mode
        packet_fanout_mode: 0
        # type: bool
        # name:
        #   en: Interface Promisc Enabled
        #   ch: 开启网卡混杂模式
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     The following scenarios require promiscuous mode to be enabled:
        #     - `inputs.cbpf.common.capture_mode` is `Virtual Mirror` or `Physical Mirror`
        #     - `inputs.cbpf.common.capture_mode` is `Local` and traffic to the virtual machine cannot be collected
        #     Note: After the NIC is enabled in promiscuous mode, more traffic will be collected, resulting in lower performance
        #   ch: |-
        #     如下场景需要开启混杂模式：
        #     - `inputs.cbpf.common.capture_mode` 等于`虚拟网络镜像`或`物理网络镜像`
        #     - `inputs.cbpf.common.capture_mode` 等于`本地流量`并且无法采集到虚拟机的流量
        #     注意：网卡开启混杂模式后会采集更多的流量导致性能降低。
        interface_promisc_enabled: false
    # type: section
    # name:
    #   en: Special Network
    #   ch: 特殊网络
    # description:
    special_network:
      # type: section
      # name: DPDK
      # description:
      dpdk:
        # type: string
        # name:
        #   en: Data Source
        #   ch: 数据源
        # unit:
        # range: []
        # enum_options: [None, eBPF, pdump]
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     Currently, there are two ways to collect DPDK traffic, including:
        #     - pdump: See details [https://dpdk-docs.readthedocs.io/en/latest/prog_guide/multi_proc_support.html](https://dpdk-docs.readthedocs.io/en/latest/prog_guide/multi_proc_support.html)
        #     - eBPF: Use eBPF Uprobe to obtain DPDK traffic, configuration `inputs.ebpf.socket.uprobe.dpdk` is also required.
        #   ch: |-
        #     目前支持两种采集 DPDK 流量的方式，包括：
        #     - pdump: 详情见 [https://dpdk-docs.readthedocs.io/en/latest/prog_guide/multi_proc_support.html](https://dpdk-docs.readthedocs.io/en/latest/prog_guide/multi_proc_support.html)
        #     - eBPF: 使用 eBPF Uprobe 的方式获取 DPDK 流量，同时需要配置 `inputs.ebpf.socket.uprobe.dpdk`
        source: None
        # type: duration
        # name:
        #   en: reorder cache window size
        #   ch: 乱序重排缓存时间窗口大小
        # unit:
        # range: [60ms, 100ms]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     When `inputs.cbpf.special_network.dpdk.source` is eBPF, the larger the time window will cause the agent to use more memory.
        #   ch: |-
        #     当 `inputs.cbpf.special_network.dpdk.source` 为 eBPF 时该配置生效，时间窗口变大会导致 agent 占用更多的内存。
        reorder_cache_window_size: 60ms
      # type: section
      # name: Libpcap
      # description:
      libpcap:
        # type: bool
        # name: Enabled
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     Supports running on Windows and Linux, Low performance when using multiple interfaces.
        #     Default to true in Windows, false in Linux.
        #   ch: |-
        #     libpcap 的启动开关，该参数在 Windows 系统中默认开启，在 Linux 系统中默认关闭。libcap 在 Windows
        #     和 Linux 系统中均支持，但在多接口的环境中流量采集性能较低。
        # upgrade_from: static_config.libpcap-enabled
        enabled: false
      # type: section
      # name: vHost User
      # description:
      vhost_user:
        # type: string
        # name: vHost Socket Path
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     Supports running on Linux with mirror mode.
        #   ch: |-
        #     支持在 Linux 环境中以虚拟网络镜像模式运行。
        # upgrade_from: static_config.vhost-socket-path
        vhost_socket_path: ""
      # type: section
      # name:
      #   en: Physical Switch
      #   ch: 物理交换机
      # description:
      physical_switch:
        # type: int
        # name:
        #   en: sFlow Receiving Ports
        #   ch: sFlow 接收端口号
        # unit:
        # range: [1, 65535]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     This feature is only supported by the Enterprise Edition of Trident.
        #     In general, sFlow uses port 6343. Default value `[]` means that no sFlow
        #     data will be collected.
        #   ch: |-
        #     配置 sFlow 的接收端口号，默认值`[]`表示不采集 sFlow 数据。通常 sFlow 使用 6343 端口。
        #     注意，该特性仅对企业版的 Trident 有效。
        # upgrade_from: static_config.xflow-collector.sflow-ports
        sflow_ports: []
        # type: int
        # name:
        #   en: NetFlow Receiving Ports
        #   ch: NetFlow 接收端口号
        # unit:
        # range: [1, 65535]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     This feature is only supported by the Enterprise Edition of Trident.
        #     Additionally, only NetFlow v5 is currently supported. In general, NetFlow
        #     uses port 2055. Default value `[]` means that no NetFlow data will be collected.
        #   ch: |-
        #     配置 NetFlow 的接收端口号，默认值`[]`表示不采集 NetFlow 数据。通常 sFlow 使用 2055 端口。
        #     注意，该特性仅对企业版的 Trident 有效，且目前仅支持 NetFlow v5 协议。
        # upgrade_from: static_config.xflow-collector.netflow-ports
        netflow_ports: []
    # type: section
    # name:
    #   en: Tunning
    #   ch: 调优
    # description:
    tunning:
      # type: bool
      # name:
      #   en: Dispatcher Queue Enabled
      #   ch: 启用 Dispatcher 队列
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The configuration takes effect when `inputs.cbpf.common.capture_mode` is `Local` or `Virtual Mirror`,
      #     dispatcher-queue is always true when `inputs.cbpf.common.capture_mode` is `Physical Mirror`.
      #
      #     Available for all recv_engines.
      #   ch: |-
      #     当 `inputs.cbpf.common.capture_mode` 为`本地流量`或`虚拟网络镜像`时该配置生效。
      #
      #     对所有流量采集方式都可用。
      # upgrade_from: static_config.dispatcher-queue
      dispatcher_queue_enabled: false
      # type: int
      # name:
      #   en: Maximum Capture Packet Size
      #   ch: 最大采集包长
      # unit: byte
      # range: [128, 65535]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     DPDK environment does not support this configuration.
      #   ch: |-
      #     该参数配置对 DPDK 环境无效。
      # upgrade_from: capture_packet_size
      max_capture_packet_size: 65535
      # type: int
      # name:
      #   en: Raw Packet Buffer Block Size
      #   ch: 裸包缓冲区 Block 大小
      # unit:
      # range: [65536, 16000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     In certain modes, raw packets will go through a queue before being processed.
      #     To avoid memory allocation for each packet, a memory block of size
      #     raw_packet_buffer_block_size is allocated for multiple packets.
      #     Larger value will reduce memory allocation for raw packet, but will also
      #     delay memory free.
      #     This configuration is effective for the following `inputs.cbpf.common.capture_mode`:
      #     - analyzer mode
      #     - local mode with `inputs.cbpf.af_packet.inner_interface_capture_enabled` = true
      #     - local mode with `inputs.cbpf.tunning.dispatcher_queue_enabled` = true
      #     - mirror mode with `inputs.cbpf.tunning.dispatcher_queue_enabled` = true
      #   ch: |-
      #     Analyzer 模式下采集到的包进入队列前需要分配内存暂存。为避免每个包进行内存申请，每次开辟
      #     raw_packet_buffer_block_size 大小的内存块给数个包使用。
      #     更大的配置可以减少内存分配，但会延迟内存释放。
      #     该配置对以下采集模式(`inputs.cbpf.common.capture_mode`)生效：
      #     - analyzer 模式
      #     - local 模式，且 `inputs.cbpf.af_packet.inner_interface_capture_enabled` = true
      #     - local 模式，且 `inputs.cbpf.tunning.dispatcher_queue_enabled` = true
      #     - mirror 模式，且 `inputs.cbpf.tunning.dispatcher_queue_enabled` = true
      # upgrade_from: static_config.analyzer-raw-packet-block-size
      raw_packet_buffer_block_size: 65536
      # type: int
      # name:
      #   en: Raw Packet Queue Size
      #   ch: 裸包队列大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     The length of the following queues (only for `inputs.cbpf.common.capture_mode` = `Physical Mirror`):
      #     - 0.1-bytes-to-parse
      #     - 0.2-packet-to-flowgenerator
      #     - 0.3-packet-to-pipeline
      #   ch: |-
      #     以下队列的长度（仅在 `inputs.cbpf.common.capture_mode` = `物理网络镜像`时有效）：
      #     - 0.1-bytes-to-parse
      #     - 0.2-packet-to-flowgenerator
      #     - 0.3-packet-to-pipeline
      # upgrade_from: static_config.analyzer-queue-size
      raw_packet_queue_size: 131072
      # type: int
      # name:
      #   en: Max Capture PPS
      #   ch: 最大采集 PPS
      # unit: pps
      # range: [1, 10000000]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Maximum packet rate allowed for collection.
      #
      #     Available for all recv_engines.
      #   ch: |-
      #     deepflow-agent 采集数据包的速率上限。
      # upgrade_from: max_collect_pps
      max_capture_pps: 1048576
    # type: section
    # name:
    #   en: Preprocess
    #   ch: 预处理
    # description:
    preprocess:
      # type: int
      # name:
      #   en: Tunnel Decap Protocols
      #   ch: 隧道解封装协议
      # unit:
      # range: []
      # enum_options:
      #   - 1: VXLAN
      #   - 2: IPIP
      #   - 3: GRE
      #   - 4: Geneve
      #   - 5: VXLAN-NSH
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Decapsulation tunnel protocols, Only the Enterprise Edition supports decap GRE and VXLAN-NSH.
      #   ch: |-
      #     deepflow-agent 需要对数据包解封装的隧道协议，仅企业版本支持解析 GRE 和 VXLAN-NSH。
      # upgrade_from: decap_type
      tunnel_decap_protocols: [1, 2]
      # type: string
      # name:
      #   en: Tunnel Trim Protocols
      #   ch: 隧道头剥离协议
      # unit:
      # range: []
      # enum_options: [ERSPAN, VXLAN, TEB]
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Whether to remove the tunnel header in mirrored traffic.
      #     Only the Enterprise Edition supports decap ERSPAN and TEB.
      #   ch: |-
      #     流量镜像（虚拟或物理）模式下，deepflow-agent 需要剥离的隧道头协议类型。
      #     仅企业版支持解析 ERSPAN 和 TEB。
      # upgrade_from: static_config.trim-tunnel-types
      tunnel_trim_protocols: []

      # type: int
      # name:
      #   en: Packet Segmentation Reassembly Ports
      #   ch: TCP分段重组端口
      # unit:
      # range: [1, 65535]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     For the specified ports, consecutive TCP packets will be aggregated together for application log parsing.
      #   ch: |-
      #     对指定端口的流，相邻的两个TCP分段 Packet 聚合在一起解析应用日志
      # upgrade_from: static_config.packet-segmentation-reassembly
      packet_segmentation_reassembly: []

    # type: section
    # name:
    #   en: Physical Mirror Traffic
    #   ch: 物理网络流量镜像
    # description:
    physical_mirror:
      # type: int
      # name:
      #   en: Default Capture Network Type
      #   ch: 默认采集网络类型
      # unit:
      # range: []
      # enum_options:
      #   - 3:
      #       en: Cloud Network
      #       ch: 云网络
      #   - _DYNAMIC_OPTIONS_: _DYNAMIC_OPTIONS_
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     deepflow-agent will mark the TAP (Traffic Access Point) location
      #     according to the outer vlan tag in the mirrored traffic of the physical
      #     switch. When the vlan tag has no corresponding TAP value, or the vlan
      #     pcp does not match the `inputs.cbpf.af_packet.vlan_pcp_in_physical_mirror_traffic`, it will assign the TAP value.
      #     This configuration item. Default value `3` means Cloud Network.
      #   ch: |-
      #     在 `inputs.cbpf.common.capture_mode` 为`物理网络镜像`模式下，deepflow-agent 通过镜像流量的外层 VLAN 标签识别并标记采集数据的
      #     TAP(Traffic Access Point)值。当流量外层 VLAN 标签没有对应的 TAP 值，或 VLAN pcp 值与
      #     `inputs.cbpf.af_packet.vlan_pcp_in_physical_mirror_traffic` 的配置不一致时，deepflow-agent 使用本参数值
      #     标记数据的 TAP 值。
      # upgrade_from: static_config.default-tap-type
      default_capture_network_type: 3
      # type: bool
      # name:
      #   en: Packet Dedup Disabled
      #   ch: 禁用 Packet 去重
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     Whether to enable mirror traffic deduplication when `inputs.cbpf.common.capture_mode` = `Physical Mirror`.
      #   ch: |-
      #     当 `inputs.cbpf.common.capture_mode` 为`物理网络镜像`模式, 该参数配置为 `true` 时，deepflow-agent 将不对数据包做去重处理。
      # upgrade_from: static_config.analyzer-dedup-disabled
      packet_dedup_disabled: false
      # type: bool
      # name:
      #   en: Gateway Traffic of Private Cloud
      #   ch: 专有云网关流量
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     Whether it is the mirrored traffic of NFVGW (cloud gateway) when `inputs.cbpf.common.capture_mode` = `Physical Mirror`.
      #   ch: |-
      #     当 `inputs.cbpf.common.capture_mode` 为 `物理网络镜像` 模式，该参数配置为 `true` 时，deepflow-agent 会将流量识别为 NFVGW 流量。
      # upgrade_from: static_config.cloud-gateway-traffic
      private_cloud_gateway_traffic: false
  # type: section
  # name: eBPF
  # description:
  ebpf:
    # type: bool
    # name: Disabled
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Whether to enable eBPF features.
    #   ch: |-
    #     eBPF 特性的总开关。
    # upgrade_from: static_config.ebpf.disabled
    disabled: false
    # type: section
    # name:
    #   en: Socket
    #   ch: Socket
    # description:
    socket:
      # type: section
      # name: Uprobe
      # description:
      uprobe:
        # type: section
        # name: Golang
        # description:
        golang:
          # type: bool
          # name: Enabled
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     Whether golang process enables HTTP2/HTTPS protocol data collection
          #     and auto-tracing. go auto-tracing also dependent go-tracing-timeout.
          #
          #     Note: When enabling this feature, the specific process list must also be specified in `inputs.proc.process_matcher`,
          #     i.e., `ebpf.socket.uprobe.golang` must be included in `inputs.proc.process_matcher.[*].enabled_features`.
          #   ch: |-
          #     Golang 程序 HTTP2/HTTPS 协议数据采集及零侵扰追踪特性的开启开关。
          #
          #     注意：开启此功能时，需要同时在 `inputs.proc.process_matcher` 中进一步指定具体的进程列表，
          #     即 `inputs.proc.process_matcher.[*].enabled_features` 中需要包含 `ebpf.socket.uprobe.golang`。
          # upgrade_from: static_config.ebpf.uprobe-golang-trace-enabled, static_config.ebpf.uprobe-process-name-regexs.golang
          enabled: false
          # type: duration
          # name:
          #   en: Tracing Timeout
          #   ch: 追踪超时时间
          # unit:
          # range: [0ns, 1d]
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     The expected maximum time interval between the server receiving the request and returning
          #     the response, If the value is '0ns', this feature is disabled. Tracing only considers the
          #     thread number.
          #   ch: |-
          #     Golang 程序追踪时请求与响应之间的最大时间间隔，设置为 '0ns' 时，Golang 程序的零侵扰追踪特性自动关闭。
          # upgrade_from: static_config.ebpf.go-tracing-timeout
          tracing_timeout: 120s
        # type: section
        # name: TLS
        # description:
        tls:
          # type: bool
          # name: Enabled
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     Whether the process that uses the openssl library to enable HTTPS protocol data collection.
          #
          #     One can use the following method to determine whether an application process can use
          #     `Uprobe hook openssl library` to access encrypted data:
          #     - Use the command `cat /proc/<PID>/maps | grep "libssl.so"` to check if it contains
          #       information about openssl. If it does, it indicates that this process is using the
          #       openssl library.
          #
          #     After enabled, deepflow-agent will retrieve process information that
          #     matches the regular expression, hooking the corresponding encryption/decryption
          #     interfaces of the openssl library. In the logs, you will encounter a message similar
          #     to the following:
          #     ```
          #     [eBPF] INFO openssl uprobe, pid:1005, path:/proc/1005/root/usr/lib64/libssl.so.1.0.2k
          #     ```
          #
          #     Note: When enabling this feature, the specific process list must also be specified in `inputs.proc.process_matcher`,
          #     i.e., `ebpf.socket.uprobe.tls` must be included in `inputs.proc.process_matcher.[*].enabled_features`.
          #   ch: |-
          #     是否启用使用 openssl 库的进程以支持 HTTPS 协议数据采集。
          #
          #     可通过以下方式判断应用进程是否能够使用 `Uprobe hook openssl 库`来采集加密数据：
          #     - 执行命令`cat /proc/<PID>/maps | grep "libssl.so"`，若包含 openssl 相关信息
          #       则说明该进程正在使用 openssl 库。
          #
          #     启用后，deepflow-agent 将获取符合正则表达式匹配的进程信息，并 Hook openssl 库的相应加解密接口。
          #     在日志中您会看到类似如下信息：
          #     ```
          #     [eBPF] INFO openssl uprobe, pid:1005, path:/proc/1005/root/usr/lib64/libssl.so.1.0.2k
          #     ```
          #
          #     注意：开启此功能时，需要同时在 `inputs.proc.process_matcher` 中进一步指定具体的进程列表，
          #     即 `inputs.proc.process_matcher.[*].enabled_features` 中需要包含 `ebpf.socket.uprobe.tls`。
          # upgrade_from: static_config.ebpf.uprobe-openssl-trace-enabled, static_config.ebpf.uprobe-process-name-regexs.openssl
          enabled: false
        # type: section
        # name: DPDK
        # description:
        dpdk:
          # type: string
          # name:
          #   en: DPDK Application Command Name
          #   ch: DPDK 应用命令名称
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: true
          # description:
          #   en: |-
          #     Set the command name of the DPDK application, eBPF will automatically
          #     locate and trace packets for data collection.
          #
          #     Example: In the command line `/usr/bin/mydpdk`, it can be set as `command: mydpdk`, and set `inputs.cbpf.special_network.dpdk.source = eBPF`
          #
          #     In scenarios where DPDK acts as the vhost-user backend, data exchange between the virtual machine and the DPDK
          #     application occurs through virtqueues (vrings). eBPF can automatically hook into the vring interface without
          #     requiring any modifications to DPDK or the virtual machine, enabling packet capture and traffic observability
          #     with zero additional configuration. In contrast, capturing packets on physical NICs requires explicit configuration
          #     of the corresponding DPDK driver interfaces.
          #   ch: |-
          #     设置 DPDK 应用的命令名称, eBPF 会自动寻找并进行追踪采集数据包
          #
          #     配置样例: 如果命令行是 `/usr/bin/mydpdk`, 可以配置成 `command: mydpdk`, 并设置 `inputs.cbpf.special_network.dpdk.source = eBPF`
          #
          #     在 DPDK 作为 vhost-user 后端的场景中，虚拟机与 DPDK 应用之间通过 virtqueue（vring）进行数据交换。
          #     eBPF 可以在无需修改 DPDK 或虚拟机的前提下，自动 hook 到 vring 接口，实现对传输数据包的捕获和分析，
          #     无需额外配置即可实现流量可观测。相比之下，若要捕获物理网卡上的数据包，则需要配合 DPDK 的驱动接口进行显式配置。
          # upgrade_from:
          command: ""
          # type: string
          # name:
          #   en: DPDK Application RX Hooks Configuration
          #   ch: DPDK 应用数据包接收 hook 点设置
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: true
          # description:
          #   en: |-
          #     Fill in the appropriate packet reception hook point according to the actual network card driver.
          #     You can use the command 'lspci -vmmk' to find the network card driver type. For example:
          #     ```
          #     Slot:   04:00.0
          #     Class:  Ethernet controller
          #     Vendor: Intel Corporation
          #     Device: Ethernet Controller XL710 for 40GbE QSFP+
          #     SVendor:        Unknown vendor 1e18
          #     SDevice:        Device 4712
          #     Rev:    02
          #     Driver: igb_uio
          #     Module: i40e
          #     ```
          #     In the example above, "Driver: igb_uio" indicates a DPDK-managed device (other options include
          #     "vfio-pci" and "uio_pci_generic", which are also managed by DPDK). The actual driver is 'i40e'
          #     (derived from 'Module: i40e').
          #
          #     You can use the sustainable profiling feature provided by DeepFlow to perform function profiling
          #     on the DPDK application and check the specific interface names. Alternatively, you can run the
          #     `perf` command on the node where the agent is located:
          #     `perf record -F97 -a -g -p <DPDK application PID> -- sleep 30`
          #     and then use
          #     `perf script | grep -E 'recv|xmit|rx|tx' | grep <drive_name>` (`drive_name` may be `ixgbe/i40e/mlx5`)
          #     to confirm the driver interfaces.
          #
          #     Below are some common interface names for different drivers, for reference only:
          #      1. Physical NIC Drivers:
          #          - Intel Drivers:
          #            - ixgbe:   Supports Intel 82598/82599/X520/X540/X550 series NICs.
          #              - rx: ixgbe_recv_pkts, ixgbe_recv_pkts_vec
          #              - tx: ixgbe_xmit_pkts, ixgbe_xmit_fixed_burst_vec, ixgbe_xmit_pkts_vec
          #            - i40e:    Supports Intel X710, XL710 series NICs.
          #              - rx: i40e_recv_pkts
          #              - tx: i40e_xmit_pkts
          #            - ice:     Supports Intel E810 series NICs.
          #              - rx: ice_recv_pkts
          #              - tx: ice_xmit_pkts
          #          - Mellanox Drivers:
          #            - mlx4:    Supports Mellanox ConnectX-3 series NICs.
          #              - rx: mlx4_rx_burst
          #              - tx: mlx4_tx_burst
          #            - mlx5:    Supports Mellanox ConnectX-4, ConnectX-5, ConnectX-6 series NICs.
          #              - rx: mlx5_rx_burst, mlx5_rx_burst_vec, mlx5_rx_burst_mprq
          #              - tx: Pending confirmation
          #          - Broadcom Drivers:
          #            - bnxt:    Supports Broadcom NetXtreme series NICs.
          #              - rx: bnxt_recv_pkts, bnxt_recv_pkts_vec (x86, Vector mode receive)
          #              - tx: bnxt_xmit_pkts, bnxt_xmit_pkts_vec (x86, Vector mode transmit)
          #       2. Virtual NIC Drivers:
          #          - Virtio Driver:
          #            - virtio:  Supports Virtio-based virtual network interfaces.
          #              - rx: virtio_recv_pkts, virtio_recv_mergeable_pkts_packed, virtio_recv_pkts_packed,
          #                    virtio_recv_pkts_vec, virtio_recv_pkts_inorder, virtio_recv_mergeable_pkts
          #              - tx: virtio_xmit_pkts_packed, virtio_xmit_pkts
          #          - VMXNET3 Driver:
          #            - vmxnet3: Supports VMware's VMXNET3 virtual NICs.
          #              - rx: vmxnet3_recv_pkts
          #              - tx: vmxnet3_xmit_pkts
          #
          #     Example: `rx_hooks: [ixgbe_recv_pkts, i40e_recv_pkts, virtio_recv_pkts, virtio_recv_mergeable_pkts]`
          #
          #     Note: When using the burst mode of the current DPDK driver interface to send and receive packets,
          #     the number of eBPF instructions is limited to 4096 in older Linux kernels (below Linux 5.2). As a
          #     result, during DPDK packet capture, only a maximum of 16 packets can be captured. For Linux kernels
          #     5.2 and above, up to 32 packets can be captured (this is typically the default value for DPDK
          #     burst mode). For kernels older than Linux 5.2, packet loss may occur (if the burst size exceeds 16).
          #
          #   ch: |-
          #     根据实际的网卡驱动填写合适的数据包接收 hook 点，可以利用命令 'lspci -vmmk' 寻找网卡驱动类型例如：
          #     ```
          #     Slot:   04:00.0
          #     Class:  Ethernet controller
          #     Vendor: Intel Corporation
          #     Device: Ethernet Controller XL710 for 40GbE QSFP+
          #     SVendor:        Unknown vendor 1e18
          #     SDevice:        Device 4712
          #     Rev:    02
          #     Driver: igb_uio
          #     Module: i40e
          #     ```
          #     上面的 "Driver: igb_uio" 说明是 DPDP 纳管的设备 (除此之外还有 "vfio-pci", "uio_pci_generic"
          #     也被 DPDK 纳管), 真实驱动是 'i40e' (从 'Module: i40e' 得到)
          #
          #     可以使用 deepflow 提供的可持续剖析功能对 DPDK 应用做函数剖析查看具体接口名字，也可以使用 perf 命令
          #     在agent所在节点上运行 `perf record -F97 -a -g -p <dpdk应用进程号> -- sleep 30`，
          #     `perf script | grep -E 'recv|xmit|rx|tx' | grep <drive_name>` (`drive_name` may be `ixgbe/i40e/mlx5`)
          #     来确认驱动接口。
          #
          #     下面列出了不同驱动对应的接口名称，仅供参考:
          #      1. Physical NIC Drivers:
          #          - Intel Drivers:
          #            - ixgbe:   Supports Intel 82598/82599/X520/X540/X550 series NICs.
          #              - rx: ixgbe_recv_pkts, ixgbe_recv_pkts_vec
          #              - tx: ixgbe_xmit_pkts, ixgbe_xmit_fixed_burst_vec, ixgbe_xmit_pkts_vec
          #            - i40e:    Supports Intel X710, XL710 series NICs.
          #              - rx: i40e_recv_pkts
          #              - tx: i40e_xmit_pkts
          #            - ice:     Supports Intel E810 series NICs.
          #              - rx: ice_recv_pkts
          #              - tx: ice_xmit_pkts
          #          - Mellanox Drivers:
          #            - mlx4:    Supports Mellanox ConnectX-3 series NICs.
          #              - rx: mlx4_rx_burst
          #              - tx: mlx4_tx_burst
          #            - mlx5:    Supports Mellanox ConnectX-4, ConnectX-5, ConnectX-6 series NICs.
          #              - rx: mlx5_rx_burst, mlx5_rx_burst_vec, mlx5_rx_burst_mprq
          #              - tx: Pending confirmation
          #          - Broadcom Drivers:
          #            - bnxt:    Supports Broadcom NetXtreme series NICs.
          #              - rx: bnxt_recv_pkts, bnxt_recv_pkts_vec (x86, Vector mode receive)
          #              - tx: bnxt_xmit_pkts, bnxt_xmit_pkts_vec (x86, Vector mode transmit)
          #       2. Virtual NIC Drivers:
          #          - Virtio Driver:
          #            - virtio:  Supports Virtio-based virtual network interfaces.
          #              - rx: virtio_recv_pkts, virtio_recv_mergeable_pkts_packed, virtio_recv_pkts_packed,
          #                    virtio_recv_pkts_vec, virtio_recv_pkts_inorder, virtio_recv_mergeable_pkts
          #              - tx: virtio_xmit_pkts_packed, virtio_xmit_pkts,
          #          - VMXNET3 Driver:
          #            - vmxnet3: Supports VMware's VMXNET3 virtual NICs.
          #              - rx: vmxnet3_recv_pkts
          #              - tx: vmxnet3_xmit_pkts
          #
          #     配置样例: `rx_hooks: [ixgbe_recv_pkts, i40e_recv_pkts, virtio_recv_pkts, virtio_recv_mergeable_pkts]`
          #
          #     注意：在当前 DPDK 驱动接口的突发模式下发送和接收数据包时，旧版 Linux 内核（低于 5.2）的 eBPF 指令数量限制为 4096。
          #     因此，在 DPDK 捕获数据包期间，最多只能捕获 16 个数据包。对于 Linux 5.2 及以上版本的内核，最多可捕获 32 个数
          #     据包（这通常是 DPDK 突发模式的默认值）。对于低于 Linux 5.2 的内核，如果突发大小超过 16，可能会发生数据包丢失。
          # upgrade_from:
          rx_hooks: []
          # type: string
          # name:
          #   en: DPDK Application TX Hooks Configuration
          #   ch: DPDK 应用数据包发送 hook 点设置
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: true
          # description:
          #   en: |-
          #     Specify the appropriate packet transmission hook point according to the actual network card driver.
          #     To obtain the driver method and configure the transmission hook point, as well as precautions，refer
          #     to the description of `inputs.ebpf.socket.uprobe.dpdk.rx_hooks`.
          #
          #     Example: `tx_hooks: [i40e_xmit_pkts, virtio_xmit_pkts_packed, virtio_xmit_pkts]`
          #   ch: |-
          #     根据实际的网卡驱动填写合适的数据包发送 hook 点, 获取驱动方法和发送hook点设置以及注意事项参考 `inputs.ebpf.socket.uprobe.dpdk.rx_hooks` 的说明.
          #
          #     配置样例: `tx_hooks: [i40e_xmit_pkts, virtio_xmit_pkts_packed, virtio_xmit_pkts]`
          # upgrade_from:
          tx_hooks: []
      # type: section
      # name: Kprobe
      # description:
      kprobe:
        # type: bool
        # name:
        #   en: kprobe disabled
        #   ch: 禁用 kprobe
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     When set to true, kprobe will be disabled.
        #   ch: |-
        #     当设置为 true 时，kprobe 功能将被禁用。
        disabled: false
        # type: bool
        # name:
        #   en: Unix Socket Enabled
        #   ch: 启用 Unix Socket 追踪
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     When set to true, enable tracing of Unix domain sockets.
        #   ch: |-
        #     当设置为 true 时，启用 Unix Socket 追踪。
        enable_unix_socket: false
        # type: section
        # name:
        #   en: Blacklist
        #   ch: 黑名单
        # description:
        blacklist:
          # type: string
          # name:
          #   en: Port Numbers
          #   ch: 端口号
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     TCP&UDP Port Blacklist, Priority higher than kprobe-whitelist.
          #
          #     Example: `ports: 80,1000-2000`
          #   ch: |-
          #     TCP 和 UDP 的端口黑名单列表。端口号列入黑名单的 socket 将被 Kprobe 采集忽略。黑名单
          #     生效优先级高于 kprobe 白名单。
          #
          #     配置样例: `ports: 80,1000-2000`
          # upgrade_from: static_config.ebpf.kprobe-blacklist.port-list
          ports: ""
        # type: section
        # name:
        #   en: Whitelist
        #   ch: 白名单
        # description:
        whitelist:
          # type: string
          # name:
          #   en: Port Numbers
          #   ch: 白名单
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     TCP&UDP Port Whitelist, Priority lower than kprobe-blacklist.
          #     Use kprobe to collect data on ports that are not in the blacklist or whitelist.
          #
          #     Example: `ports: 80,1000-2000`
          #   ch: |-
          #     TCP 和 UDP 的端口白名单列表，白名单生效优先级低于 kprobe 黑名单。
          #     未列入黑名单、白名单的端口用 kprobe 做采集。
          #
          #     配置样例: `ports: 80,1000-2000`
          # upgrade_from: static_config.ebpf.kprobe-whitelist.port-list
          ports: ""
      # type: section
      # name:
      #   en: Tunning
      #   ch: 调优
      # description:
      tunning:
        # type: int
        # name:
        #   en: Max Capture Rate
        #   ch: 最大采集速率
        # unit: Per Second
        # range: [0, 64000000]
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     Default value `0` means no limitation.
        #   ch: |-
        #     eBPF 数据的最大采集速率，设置为 `0` 表示不对 deepflow-agent 的 eBPF 数据采集速率做限制。
        # upgrade_from: static_config.ebpf.global-ebpf-pps-threshold
        max_capture_rate: 0
        # type: bool
        # name:
        #   en: Syscall_trace_id Disabled
        #   ch: 禁用 syscall_trace_id 相关的计算
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     When the trace_id is injected into all requests, the computation logic for all
        #     syscall_trace_id can be turned off. This will significantly reduce the impact of the
        #     eBPF hook on the CPU consumption of the application process.
        #   ch: |-
        #     当 trace_id 注入所有请求时，所有请求的 syscall_trace_id 计算逻辑可以关闭。这将大大减少
        #     eBPF hook 进程的 CPU 消耗。
        syscall_trace_id_disabled: false
        # type: bool
        # name:
        #   en: Disable Pre-allocating Memory
        #   ch: 禁用预分配内存
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     When full map preallocation is too expensive, set this configuration to `true` will
        #     prevent memory pre-allocation during map definition, but it may result in some performance
        #     degradation. This configuration only applies to maps of type 'BPF_MAP_TYPE_HASH'.
        #     Currently applicable to socket trace and uprobe Golang/OpenSSL trace functionalities.
        #     Disabling memory preallocation will approximately reduce memory usage by 45MB.
        #   ch: |-
        #     当完整的map预分配过于昂贵时，将此配置设置为 `true` 可以防止在定义map时进行内存预分配，
        #     但这可能会导致一些性能下降。此配置仅适用于 `BPF_MAP_TYPE_HASH` 类型的 bpf map。
        #     目前适用于 socket trace 和 uprobe Golang/OpenSSL trace 功能。禁用内存预分配大约会减少45M的内存占用。
        # upgrade_from: static_config.ebpf.map-prealloc-disabled
        map_prealloc_disabled: false
        # type: bool
        # name:
        #   en: Enable the fentry/fexit feature
        #   ch: 启用fentry/fexit特性
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Explanation of Using fentry/fexit Features
        #     - Compared to traditional kprobes, fentry and fexit programs offer higher performance and
        #       availability, providing approximately 5%-10% performance improvement.
        #     - Some Linux kernels do not fully support this feature, which may lead to kernel bugs and
        #       node crashes. Known bug fixes include:
        #       - Bug fix for TencentOS Linux kernel 5.4.119
        #         [https://github.com/torvalds/linux/commit/c3d6324f841bab2403be6419986e2b1d1068d423](https://github.com/torvalds/linux/commit/c3d6324f841bab2403be6419986e2b1d1068d423)
        #       - Bug fix for Alibaba Cloud Linux kernel 5.10.23
        #         [https://github.com/gregkh/linux/commit/e21d2b92354b3cd25dd774ebb0f0e52ff04a7861](https://github.com/gregkh/linux/commit/e21d2b92354b3cd25dd774ebb0f0e52ff04a7861)
        #     - Kernel recommendation: To enable the fentry/fexit feature, it is recommended to use Linux
        #       kernel 5.10.28 or later to ensure stability and performance.
        #   ch: |-
        #     使用 fentry/fexit 特性说明
        #     - 相比传统的 kprobes，fentry 和 fexit 程序提供了更高的性能和可用性，可带来约 5%–10% 的性能提升。
        #     - 部分 Linux 内核对该特性支持不够完善，可能导致内核 BUG 和节点崩溃。已知的 BUG 修复包括：
        #       - TencentOS Linux kernel 5.4.119 的修复
        #         [https://github.com/torvalds/linux/commit/c3d6324f841bab2403be6419986e2b1d1068d423](https://github.com/torvalds/linux/commit/c3d6324f841bab2403be6419986e2b1d1068d423)
        #       - Alibaba Cloud Linux kernel 5.10.23 的修复
        #         [https://github.com/gregkh/linux/commit/e21d2b92354b3cd25dd774ebb0f0e52ff04a7861](https://github.com/gregkh/linux/commit/e21d2b92354b3cd25dd774ebb0f0e52ff04a7861)
        #     - 内核建议：若要启用 fentry/fexit 特性，推荐使用 Linux kernel 5.10.28 及以上版本，以确保稳定性和性能。
        # upgrade_from:
        fentry_enabled: false
      # type: section
      # name:
      #   en: Preprocess
      #   ch: 预处理
      # description:
      preprocess:
        # type: int
        # name:
        #   en: OOOR Cache Size
        #   ch: 乱序重排（OOOR）缓冲区大小
        # unit:
        # range: [8, 1024]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     OOOR: Out Of Order Reassembly
        #
        #     When `out_of_order_reassembly_protocols` is enabled, up to `out_of_order_reassembly_cache_size`
        #     eBPF socket events (each event consuming up to `processors.request_log.tunning.payload_truncation` bytes) will be cached
        #     in each TCP/UDP flow to prevent out-of-order events from impacting application protocol
        #     parsing. Since eBPF socket events are sent to user space in batches, out-of-order scenarios
        #     mainly occur when requests and responses within a single session are processed by different
        #     CPUs, causing the response to reach user space before the request.
        #   ch: |-
        #     由于 eBPF socket 事件是以批处理的方式向用户态空间发送数据，同一个应用调用的请求、响应由不同 CPU 处理时，可能
        #     会出现请求、响应乱序的情况，开启 Syscall 数据乱序重排特性后，每个 TCP/UDP 流会缓存一定数量的 eBPF socket
        #     事件，以修正乱序数据对应用调用解析的影响。该参数设置了每个 TCP/UDP 流可以缓存的 eBPF socket 事件数量上限（每
        #     条事件数据占用的字节数上限受 `processors.request_log.tunning.payload_truncation` 控制）。在 Syscall 数据乱序较严重
        #     导致应用调用采集不全的环境中，可适当调大该参数。
        # upgrade_from: static_config.ebpf.syscall-out-of-order-cache-size
        out_of_order_reassembly_cache_size: 16
        # type: string
        # name:
        #   en: OOOR Protocols
        #   ch: 乱序重排（OOOR）协议列表
        # unit:
        # range: []
        # enum_options: [_DYNAMIC_OPTIONS_]
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     OOOR: Out Of Order Reassembly
        #
        #     When this capability is enabled for a specific application protocol, the agent will add
        #     out-of-order-reassembly processing for it. Note that the agent will consume more memory
        #     in this case, so please adjust the syscall-out-of-order-cache-size accordingly and monitor
        #     the agent's memory usage.
        #
        #     Supported protocols: [https://www.deepflow.io/docs/features/l7-protocols/overview/](https://www.deepflow.io/docs/features/l7-protocols/overview/)
        #
        #     Attention: use `HTTP2` for `gRPC` Protocol.
        #   ch: |-
        #     配置后 deepflow-agent 将对指定应用协议的处理增加乱序重排过程。注意：（1）开启特性将消耗更多的内存，因此
        #     需关注 agent 内存用量；（2）如需对`gRPC`协议乱序重排，请配置`HTTP2`协议。
        # upgrade_from: static_config.ebpf.syscall-out-of-order-reassembly
        out_of_order_reassembly_protocols: []
        # type: string
        # name:
        #   en: SR Protocols
        #   ch: 分段重组（SR）协议列表
        # unit:
        # range: []
        # enum_options: [_DYNAMIC_OPTIONS_]
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     SR: Segmentation Reassembly
        #
        #     When this capability is enabled for a specific application protocol, the agent will add
        #     segmentation-reassembly processing to merge application protocol content spread across
        #     multiple syscalls before parsing it. This enhances the success rate of application
        #     protocol parsing. Note that `out_of_order_reassembly_protocols` must also be enabled for
        #     this feature to be effective.
        #     Supported protocols: [https://www.deepflow.io/docs/features/l7-protocols/overview/](https://www.deepflow.io/docs/features/l7-protocols/overview/)
        #     Attention: use `HTTP2` for `gRPC` Protocol.
        #   ch: |-
        #     配置后 deepflow-agent 将对指定应用协议的处理增加分片重组过程，将多个 Syscall 的内容分片重组后再进行
        #     协议解析，以增强应用协议的采集成功率。
        #
        #     注意：
        #     1. 该特性的生效的前提条件是`out_of_order_reassembly_protocols`开启并生效；
        #        - 支持协议：[https://www.deepflow.io/docs/zh/features/l7-protocols/overview/](https://www.deepflow.io/docs/zh/features/l7-protocols/overview/)
        #     2. 如需对`gRPC`协议乱序重排，请配置`HTTP2`协议。
        # upgrade_from: static_config.ebpf.syscall-segmentation-reassembly
        segmentation_reassembly_protocols: []
    # type: section
    # name:
    #   en: File
    #   ch: File
    # description:
    file:
      # type: section
      # name:
      #   en: IO Event
      #   ch: IO 事件
      # description:
      io_event:
        # type: int
        # name:
        #   en: Collect Mode
        #   ch: 采集模式
        # unit:
        # range: []
        # enum_options:
        #   - 0:
        #       en: Disabled
        #       ch: 禁用
        #   - 1:
        #       en: Request Life Cycle
        #       ch: 调用生命周期
        #   - 2:
        #       en: All
        #       ch: 全部
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Collection modes:
        #     - Disabled: Indicates that no IO events are collected.
        #     - Request Life Cycle: Indicates that only IO events within the request life cycle are collected.
        #     - All: Indicates that all IO events are collected.
        #
        #     Note:
        #     - To obtain the full file path, we need to combine it with the process's mount information. However,
        #       some processes exit quickly after completing their tasks. When we attempt to process the file I/O
        #       data generated by such processes, the corresponding /proc/[pid]/mountinfo entry may no longer be
        #       available, resulting in incomplete paths (missing mount points). For processes with a lifetime
        #       shorter than 50 ms, the file path may lack mount point information. This issue does not occur with
        #       long-running processes.
        #   ch: |-
        #     采集模式：
        #     - 禁用：不采集任何文件 IO 事件。
        #     - 调用生命周期：仅采集调用生命周期内的文件 IO 事件。
        #     - 全部：采集所有的文件 IO 事件。
        #
        #     说明：
        #     - 为了获取文件的完整路径，需要结合进程的挂载信息进行路径拼接。然而，一些进程在完成任务后会迅速退出，
        #       此时我们处理其产生的文件读写数据时，可能已无法从 /proc/[pid]/mountinfo 中获取挂载信息，导致路径不
        #       完整（缺少挂载点）。我们对于 50ms 以下生存期的进程，文件路径会缺少挂载点信息。对于长期运行的进程，
        #       则不存在该问题。
        # upgrade_from: static_config.ebpf.io-event-collect-mode
        collect_mode: 1
        # type: duration
        # name:
        #   en: Minimal Duration
        #   ch: 最小耗时
        # unit:
        # range: [1ns, 1s]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Only collect IO events with delay exceeding this threshold.
        #   ch: |-
        #     deepflow-agent 所采集的文件 IO 事件的时延下限阈值，操作系统中时延低于此阈值
        #     的文件 IO 事件将被忽略。
        # upgrade_from: static_config.ebpf.io-event-minimal-duration
        minimal_duration: 1ms
    # type: section
    # name: Profile
    # description:
    profile:
      # type: section
      # name:
      #   en: Unwinding
      #   ch: 栈回溯
      # description:
      unwinding:
        # type: bool
        # name:
        #   en: DWARF unwinding disabled
        #   ch: 禁用 DWARF 栈回溯
        # unit:
        # range: []
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     The default setting is `true`, agent will use frame pointer based unwinding for
        #     all processes. If a process does not contain frame pointers, the stack cannot be
        #     displayed correctly.
        #     Setting it to `false` will enable DWARF based stack unwinding for all processes that
        #     do not contain frame pointers. Agent uses a heuristic algorithm to determine whether
        #     the process being analyzed contains frame pointers.
        #     Additionally, setting `dwarf_regex` to force DWARF based stack unwinding for certain
        #     processes.
        #   ch: |-
        #     默认设置为 `true`，将禁用 DWARF 栈回溯，对所有进程使用基于帧指针的回溯，如果进程不包含帧指针将无法显示正常的栈。
        #     设置为 `false` 将对所有不包含帧指针的进程启用 DWARF 回溯。采集器使用启发式算法判断待剖析进程是否包含帧指针。
        #     设置 `dwarf_regex` 后，将强制对匹配的进程使用 DWARF 回溯。
        # upgrade_from: static_config.ebpf.dwarf-disabled
        dwarf_disabled: true
        # type: string
        # name:
        #   en: DWARF unwinding process matching regular expression
        #   ch: DWARF 回溯进程匹配正则表达式
        # unit:
        # range: []
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     If set to empty, agennt will use a heuristic algorithm to determine whether the process
        #     being analyzed contains frame pointers, and will use DWARF based stack unwinding for
        #     processes that do not contain frame pointers.
        #     If set to a valid regular expression, agent will no longer infer whether a process contains
        #     frame pointers but will instead use the provided regular expression to match process names,
        #     applying DWARF based stack unwinding only to the matching processes.
        #   ch: |-
        #     如设置为空，采集器将使用启发式算法判断待剖析进程是否包含帧指针，并对不包含帧指针的进程使用 DWARF 栈回溯。
        #     如设置为合法正则表达式，采集器将不再自行推断进程是否包含帧指针，改用该正则表达式对进程名进行匹配，仅对匹配的进程使用 DWARF 帧回溯。
        # upgrade_from: static_config.ebpf.dwarf-regex
        dwarf_regex: ""
        # type: int
        # name:
        #   en: DWARF unwinding process map size
        #   ch: DWARF 回溯进程表容量
        # unit:
        # range: [1, 131072]
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     Each process using DWARF unwind has an entry in this map, relating process id to DWARF unwind entries.
        #     The size of each one of these entries is arount 8K, the default setting will allocate around 8M kernel memory.
        #     This is a hash map, so size can be lower than max process id.
        #     The configuration is only effective if DWARF is enabled.
        #   ch: |-
        #     每个需要进行 DWARF 回溯的进程在该表中有一条记录，用于关联进程和回溯记录分片。
        #     每条记录大约占 8K 内存，默认配置大约需要分配 8M 内核内存。
        #     由于是哈希表，配置可以比最大进程号低。
        #     该配置只在 DWARF 功能开启时生效。
        # upgrade_from: static_config.ebpf.dwarf-process-map-size
        dwarf_process_map_size: 1024
        # type: int
        # name:
        #   en: DWARF unwinding shard map size
        #   ch: DWARF 回溯分片表容量
        # unit:
        # range: [1, 4096]
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     The number of unwind entry shards for DWARF unwinding.
        #     The size of each one of these entries is 1M, the default setting will allocate around 128M kernel memory.
        #     The configuration is only effective if DWARF is enabled.
        #   ch: |-
        #     DWARF 回溯记录分片数量。
        #     每条记录大约占 1M 内存，默认配置大约需要分配 128M 内核内存。
        #     该配置只在 DWARF 功能开启时生效。
        # upgrade_from: static_config.ebpf.dwarf-shard-map-size
        dwarf_shard_map_size: 128
      # type: section
      # name: On-CPU
      # description:
      on_cpu:
        # type: bool
        # name: Disabled
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     eBPF On-CPU profile switch.
        #
        #     Note: When enabling this feature, the specific process list must also be specified in `inputs.proc.process_matcher`,
        #     i.e., `ebpf.profile.on_cpu` must be included in `inputs.proc.process_matcher.[*].enabled_features`.
        #   ch: |-
        #     eBPF On-CPU profile 数据的采集开关。
        #
        #     注意：开启此功能时，需要同时在 `inputs.proc.process_matcher` 中进一步指定具体的进程列表，
        #     即 `inputs.proc.process_matcher.[*].enabled_features` 中需要包含 `ebpf.profile.on_cpu`。
        # upgrade_from: static_config.ebpf.on-cpu-profile.disabled
        disabled: false
        # type: int
        # name:
        #   en: Sampling Frequency
        #   ch: 采样频率
        # unit:
        # range: [1, 1000]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     eBPF On-CPU profile sampling frequency.
        #   ch: |-
        #     eBPF On-CPU profile 数据的采样周期。
        # upgrade_from: static_config.ebpf.on-cpu-profile.frequency
        sampling_frequency: 99
        # type: bool
        # name:
        #   en: Aggregate by CPU
        #   ch: 按 CPU 聚合
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Whether to obtain the value of CPUID and decide whether to participate in aggregation.
        #     - `true`: Obtain the value of CPUID and will be included in the aggregation of stack
        #       trace data.
        #     - `false`: It will not be included in the aggregation. Any other value is considered
        #       invalid, the CPU value for stack trace data reporting is a special value
        #       `CPU_INVALID: 0xfff` used to indicate that it is an invalid value.
        #   ch: |-
        #     采集 On-CPU 采样数据时，是否获取 CPUID 的开关。
        #     - `true`: 表示在采集 On-CPU 采样数据时获取 CPUID （On-CPU 剖析时，支持对单个 CPU 的分析）。
        #     - `false`: 表示在采集 On-CPU 采样数据时不获取 CPUID （On-CPU 剖析时，不支持单个 CPU 的分析）。
        # upgrade_from: static_config.ebpf.on-cpu-profile.cpu
        aggregate_by_cpu: false
      # type: section
      # name: Off-CPU
      # description:
      off_cpu:
        # type: bool
        # name: Disabled
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     eBPF Off-CPU profile switch.
        #
        #     Note: When enabling this feature, the specific process list must also be specified in `inputs.proc.process_matcher`,
        #     i.e., `ebpf.profile.off_cpu` must be included in `inputs.proc.process_matcher.[*].enabled_features`.
        #   ch: |-
        #     eBPF Off-CPU profile 数据的采集开关。
        #
        #     注意：开启此功能时，需要同时在 `inputs.proc.process_matcher` 中进一步指定具体的进程列表，
        #     即 `inputs.proc.process_matcher.[*].enabled_features` 中需要包含 `ebpf.profile.off_cpu`。
        # upgrade_from: static_config.ebpf.off-cpu-profile.disabled
        disabled: true
        # type: bool
        # name:
        #   en: Aggregate by CPU
        #   ch: 按 CPU 聚合
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     Whether to obtain the value of CPUID and decide whether to participate in aggregation.
        #     - `true`: Obtain the value of CPUID and will be included in the aggregation of stack
        #       trace data.
        #     - `false`: It will not be included in the aggregation. Any other value is considered
        #       invalid, the CPU value for stack trace data reporting is a special value
        #       `CPU_INVALID: 0xfff` used to indicate that it is an invalid value.
        #   ch: |-
        #     采集 Off-CPU 数据时，是否获取 CPUID 的开关。
        #     - `true`: 表示在采集 Off-CPU 数据时获取 CPUID （Off-CPU 剖析时，支持对单个 CPU 的分析）。
        #     - `false`: 表示在采集 Off-CPU 数据时不获取 CPUID （Off-CPU 剖析时，不支持单个 CPU 的分析）。
        # upgrade_from: static_config.ebpf.off-cpu-profile.cpu
        aggregate_by_cpu: false
        # type: duration
        # name:
        #   en: Minimum Blocking Time
        #   ch: 最小阻塞时间
        # unit:
        # range: [0ns, 1h]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     If set to '0ns', there will be no minimum value limitation. Scheduler events are still
        #     high-frequency events, as their rate may exceed 1 million events per second, so
        #     caution should still be exercised.
        #
        #     If overhead remains an issue, you can configure the 'minblock' tunable parameter here.
        #     If the off-CPU time is less than the value configured in this item, the data will be
        #     discarded. If your goal is to trace longer blocking events, increasing this parameter
        #     can filter out shorter blocking events, further reducing overhead. Additionally, we
        #     will not collect events with a blocking time exceeding 1 hour.
        #   ch: |-
        #     低于'最小阻塞时间'的 Off-CPU 数据将被 deepflow-agent 忽略，'最小阻塞时间'设置为 '0ns' 表示
        #     采集所有的 Off-CPU 数据。由于 CPU 调度事件数量庞大（每秒可能超过一百万次），调小该参数将带来
        #     明显的资源开销，如果需要跟踪大时延的调度阻塞事件，建议调大该参数，以降低资源开销。另外，deepflow-agent
        #     不采集阻塞超过 1 小时的事件。
        # upgrade_from: static_config.ebpf.off-cpu-profile.minblock
        min_blocking_time: 50us
      # type: section
      # name: Memory
      # description:
      memory:
        # type: bool
        # name: Disabled
        # unit:
        # range: []
        # enum_options: []
        # modification: hot_update
        # ee_feature: true
        # description:
        #   en: |-
        #     eBPF memory profile switch.
        #
        #     Note: When enabling this feature, the specific process list must also be specified in `inputs.proc.process_matcher`,
        #     i.e., `ebpf.profile.memory` must be included in `inputs.proc.process_matcher.[*].enabled_features`.
        #   ch: |-
        #     eBPF memory profile 数据的采集开关。
        #
        #     注意：开启此功能时，需要同时在 `inputs.proc.process_matcher` 中进一步指定具体的进程列表，
        #     即 `inputs.proc.process_matcher.[*].enabled_features` 中需要包含 `ebpf.profile.memory`。
        # upgrade_from: static_config.ebpf.memory-profile.disabled
        disabled: true
        # type: duration
        # name:
        #   en: Memory profile report interval
        #   ch: 内存剖析上报间隔
        # unit:
        # range: [1s, 60s]
        # enum_options: []
        # modification: hot_update
        # ee_feature: true
        # description:
        #   en: |-
        #     The interval at which deepflow-agent aggregates and reports memory profile data.
        #   ch: |-
        #     deepflow-agent 聚合和上报内存剖析数据的间隔。
        # upgrade_from: static_config.ebpf.memory-profile.report-interval
        report_interval: 10s
        # type: int
        # name:
        #   en: LRU length for process allocated addresses
        #   ch: 进程分配地址 LRU 长度
        # unit:
        # range: [1024, 4194704]
        # enum_options: []
        # modification: hot_update
        # ee_feature: true
        # description:
        #   en: |-
        #     Agent uses LRU cache to record process allocated addresses to avoid uncontrolled
        #     memory usage. Each record in this LRU is about 80B.
        #   ch: |-
        #     采集器使用 LRU 缓存记录进程分配的地址，以避免内存使用失控。每个 LRU 条目大约占 80B 内存。
        # upgrade_from:
        allocated_addresses_lru_len: 131072
      # type: section
      # name:
      #   en: Preprocess
      #   ch: 预处理
      # description:
      preprocess:
        # type: bool
        # name:
        #   en: Stack Compression
        #   ch: 函数栈压缩
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Compress the call stack before sending data. Compression can effectively reduce the agent's
        #     memory usage, data transmission bandwidth consumption, and ingester's CPU overhead. However,
        #     it also increases the CPU usage of the agent. Tests have shown that compressing the on-cpu
        #     function call stack of the deepflow-agent can reduce bandwidth consumption by `x` times, but
        #     it will result in an additional `y%` CPU usage for the agent.
        #   ch: |-
        #     发送数据之前压缩函数调用栈。压缩能够有效降低 agent 的内存开销、数据传输的带宽消耗、以及
        #     ingester 的 CPU 开销，但是 Agent 也会因此消耗更多的 CPU。测试表明，将deepflow-agent 自身的
        #     on-cpu 函数调用栈压缩，可以将带宽消耗降低 `x` 倍，但会使得 agent 额外消耗 `y%` 的 CPU。
        # upgrade_from: static_config.ebpf.preprocess.stack-compression
        stack_compression: true
    # type: section
    # name:
    #   en: Tunning
    #   ch: 调优
    # description:
    tunning:
      # type: int
      # name:
      #   en: Collector Queue Size
      #   ch: 采集队列大小
      # unit:
      # range: [4096, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The length of the following queues:
      #     - 0-ebpf-to-ebpf-collector
      #     - 1-proc-event-to-sender
      #     - 1-profile-to-sender
      #   ch: |-
      #     以下 deepflow-agent 的 eBPF 数据采集队列大小（分别限制）：
      #     - 0-ebpf-to-ebpf-collector
      #     - 1-proc-event-to-sender
      #     - 1-profile-to-sender
      # upgrade_from: static_config.ebpf-collector-queue-size
      collector_queue_size: 65535
      # type: int
      # name:
      #   en: Userspace Worker Threads
      #   ch: 用户态工作线程数
      # unit:
      # range: [1, 1024]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The number of worker threads refers to how many threads participate
      #     in data processing in user-space. The actual maximal value is the number
      #     of CPU logical cores on the host.
      #   ch: |-
      #     参与用户态数据处理的工作线程数量。实际最大值为主机 CPU 逻辑核心数。
      # upgrade_from: static_config.ebpf.thread-num
      userspace_worker_threads: 1
      # type: int
      # name:
      #   en: Perf Pages Count
      #   ch: Perf Page 数量
      # unit:
      # range: [32, 8192]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The number of page occupied by the shared memory of the kernel. The
      #     value is `2^n (5 <= n <= 13)`. Used for perf data transfer. If the
      #     value is between `2^n` and `2^(n+1)`, it will be automatically adjusted
      #     by the ebpf configurator to the minimum value `2^n`.
      #   ch: |-
      #     内核共享内存占用的页数。值为 `2^n (5 <= n <= 13)`。用于 perf 数据传输。
      #     如果值在 `2^n` 和 `2^(n+1)` 之间，将自动调整到最小值 `2^n`。
      # upgrade_from: static_config.ebpf.perf-pages-count
      perf_pages_count: 128
      # type: int
      # name:
      #   en: Kernel Ring Size
      #   ch: 内核环形队列大小
      # unit:
      # range: [8192, 131072]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The size of the ring cache queue, The value is `2^n (13 <= n <= 17)`.
      #     If the value is between `2^n` and `2^(n+1)`, it will be automatically
      #     adjusted by the ebpf configurator to the minimum value `2^n`.
      #   ch: |-
      #     内核环形队列的大小。值为 `2^n (13 <= n <= 17)`。
      #     如果值在 `2^n` 和 `2^(n+1)` 之间，将自动调整到最小值 `2^n`。
      # upgrade_from: static_config.ebpf.ring-size
      kernel_ring_size: 65536
      # type: int
      # name:
      #   en: Maximum Socket Entries
      #   ch: 最大 Socket 条目数
      # unit:
      # range: [10000, 2000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Set the maximum value of hash table entries for socket tracking, depending
      #     on the number of concurrent requests in the actual scenario
      #   ch: |-
      #     设置 socket tracking 哈希表的最大条目数，根据实际场景中的并发请求数量而定。
      # upgrade_from: static_config.ebpf.max-socket-entries
      max_socket_entries: 131072
      # type: int
      # name:
      #   en: Socket Map Reclaim Threshold
      #   ch: Socket Map 回收阈值
      # unit:
      # range: [8000, 2000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The threshold for cleaning socket map table entries.
      #   ch: |-
      #     Socket map 表条目清理阈值。
      # upgrade_from: static_config.ebpf.socket-map-max-reclaim
      socket_map_reclaim_threshold: 120000
      # type: int
      # name:
      #   en: Maximum Trace Entries
      #   ch: 最大 Trace 条目数
      # unit:
      # range: [10000, 2000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Set the maximum value of hash table entries for thread/coroutine tracking sessions.
      #   ch: |-
      #     线程和协程追踪的最大哈希表条目数。
      # upgrade_from: static_config.ebpf.max-trace-entries
      max_trace_entries: 131072
  # type: section
  # name:
  #   en: Resources
  #   ch: 资源
  # description:
  resources:
    # type: duration
    # name:
    #   en: Push Interval
    #   ch: 推送间隔
    # unit:
    # range: [10s, 3600s]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     The interval at which deepflow-agent actively reports resource information
    #     to deepflow-server.
    #   ch: |-
    #     deepflow-agent 主动向 deepflow-server 上报/同步资源信息的时间间隔。
    # upgrade_from: platform_sync_interval
    push_interval: 10s
    # type: bool
    # name:
    #   en: Workload Resource Sync Enabled
    #   ch: 启用云主机资源同步
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     When enabled, deepflow-server will abstract VM based on the runtime
    #     environment information reported by deepflow-agent.
    #   ch: |-
    #     开启开关后，deepflow-server 基于 deepflow-agent 上报的运行环境信息，生成一个云主机资源。
    #     用于无法通过云平台 API 同步云主机资源的场景，也可用于同步非云环境中普通物理服务器的资源信息。
    workload_resource_sync_enabled: false
    # type: section
    # name:
    #   en: Collect Private Cloud Resource
    #   ch: 采集专有云资源
    # description:
    private_cloud:
      # type: bool
      # name:
      #   en: Hypervisor Resource Enabled
      #   ch: 启用云宿主机资源
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When enabled, deepflow-agent will automatically synchronize virtual
      #     machine and network information on KVM or Linux Host to deepflow-server.
      #     Information collected includes:
      #     - raw_all_vm_xml
      #     - raw_vm_states
      #     - raw_ovs_interfaces
      #     - raw_ovs_ports
      #     - raw_brctl_show
      #     - raw_vlan_config
      #   ch: |-
      #     开启开关后，deepflow-agent 将采集 KVM 或 Linux 宿主机中的 VM 信息和网络信息，并上报/同步至 deepflow-server。
      #     采集的信息包括：
      #     - raw_all_vm_xml
      #     - raw_vm_states
      #     - raw_ovs_interfaces
      #     - raw_ovs_ports
      #     - raw_brctl_show
      #     - raw_vlan_config
      # upgrade_from: platform_enabled
      hypervisor_resource_enabled: false
      # type: int
      # name:
      #   en: VM MAC Source
      #   ch: 虚拟机 MAC 源
      # unit:
      # range: []
      # enum_options:
      #   - 0:
      #       en: Interface MAC Address
      #       ch: 网卡 MAC 地址
      #   - 1:
      #       en: Interface Name
      #       ch: 网卡名称
      #   - 2:
      #       en: Qemu XML File
      #       ch: Qemu XML 文件
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     How to extract the real MAC address of the virtual machine when the
      #     agent runs on the KVM host.
      #
      #     Explanation of the options:
      #     - Interface MAC Address: extracted from tap interface MAC address
      #     - Interface Name: extracted from tap interface name
      #     - Qemu XML File: extracted from the XML file of the virtual machine
      #   ch: |-
      #     配置 deepflow-agent 提取 VM 真实 MAC 地址的方法:
      #     - 网卡 MAC 地址: 从 tap 接口的 MAC 地址中提取 VM 的 MAC 地址
      #     - 网卡名称: 从 tap 接口的名字中提取 MAC 地址
      #     - Qemu XML 文件: 从 VM XML 文件中提取 MAC 地址
      # upgrade_from: if_mac_source
      vm_mac_source: 0
      # type: string
      # name:
      #   en: VM XML Directory
      #   ch: 虚拟机 XML 文件夹
      # unit:
      # range: [0, 100]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     VM XML file directory.
      #   ch: |-
      #     宿主机中存放 VM XML 文件的目录
      # upgrade_from: vm_xml_path
      vm_xml_directory: /etc/libvirt/qemu/
      # type: string
      # name:
      #   en: VM MAC Mapping Script
      #   ch: 虚拟机 MAC 映射脚本
      # unit:
      # range: [0, 100]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The MAC address mapping relationship of TAP NIC in complex environment can be
      #     constructed by writing a script. The following conditions must be met to use this
      #     script:
      #     1. if_mac_source = 2
      #     2. tap_mode = 0
      #     3. The name of the TAP NIC is the same as in the virtual machine XML file
      #     4. The format of the script output is as follows:
      #        - tap2d283dfe,11:22:33:44:55:66
      #        - tap2d283223,aa:bb:cc:dd:ee:ff
      #   ch: |-
      #     复杂环境中，TAP 网卡的 MAC 地址映射关系可以通过编写脚本实现。使用脚本时需要满足以下条件：
      #     1. if_mac_source = 2
      #     2. tap_mode = 0
      #     3. TAP 网卡的名称与虚拟机 XML 文件中的名称相同
      #     4. 脚本输出格式如下：
      #        - tap2d283dfe,11:22:33:44:55:66
      #        - tap2d283223,aa:bb:cc:dd:ee:ff
      # upgrade_from: static_config.tap-mac-script
      vm_mac_mapping_script: ""
    # type: section
    # name:
    #   en: Collect K8s Resource
    #   ch: 采集 K8s 资源
    # description:
    kubernetes:
      # type: string
      # name:
      #   en: K8s Namespace
      #   ch: K8s 命名空间
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Specify the namespace for agent to query K8s resources.
      #   ch: |-
      #     指定采集器获取 K8s 资源时的命名空间
      # upgrade_from: static_config.kubernetes-namespace
      kubernetes_namespace:
      # type: dict
      # name:
      #   en: K8s API Resources
      #   ch: K8s API 资源
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Specify kubernetes resources to watch.
      #
      #     The schematics of entries in list is:
      #     {
      #         name: string
      #         group: string
      #         version: string
      #         disabled: bool
      #         field_selector: string
      #     }
      #
      #     Agent will watch the following resources by default:
      #     - namespaces
      #     - nodes
      #     - pods
      #     - replicationcontrollers
      #     - services
      #     - daemonsets
      #     - deployments
      #     - replicasets
      #     - statefulsets
      #     - ingresses
      #     - configmaps
      #
      #     To disable a resource, add an entry to the list with `disabled: true`:
      #     ```yaml
      #     inputs:
      #       resources:
      #         kubernetes:
      #           api_resources:
      #           - name: services
      #             disabled: true
      #     ```
      #
      #     To enable a resource, add an entry of this resource to the list. Be advised that
      #     this setting overrides the default of the same resource. For example, to enable
      #     `statefulsets` in both group `apps` (the default) and `apps.kruise.io` will require
      #     two entries:
      #     ```yaml
      #     inputs:
      #       resources:
      #         kubernetes:
      #           api_resources:
      #           - name: statefulsets
      #             group: apps
      #           - name: statefulsets
      #             group: apps.kruise.io
      #             version: v1beta1
      #     ```
      #
      #     To watching `routes` in openshift you can use the following settings:
      #     ```yaml
      #     inputs:
      #       resources:
      #         kubernetes:
      #           api_resources:
      #           - name: ingresses
      #             disabled: true
      #           - name: routes
      #     ```
      #   ch: |-
      #     指定采集器采集的 K8s 资源。
      #
      #     列表中的条目格式如下：
      #     {
      #         name: string
      #         group: string
      #         version: string
      #         disabled: bool
      #         field_selector: string
      #     }
      #
      #     默认采集的资源如下：
      #     - namespaces
      #     - nodes
      #     - pods
      #     - replicationcontrollers
      #     - services
      #     - daemonsets
      #     - deployments
      #     - replicasets
      #     - statefulsets
      #     - ingresses
      #     - configmaps
      #
      #     禁用某个资源，在列表中添加 `disabled: true` 的条目：
      #     ```yaml
      #     inputs:
      #       resources:
      #         kubernetes:
      #           api_resources:
      #           - name: services
      #             disabled: true
      #     ```
      #
      #     启用某个资源，在列表中添加该资源的条目。注意该设置会覆盖默认的资源采集。
      #     例如，要启用在 group `apps` 和 `apps.kruise.io` 中的 `statefulsets`，需要添加两个条目：
      #     ```yaml
      #     inputs:
      #       resources:
      #         kubernetes:
      #           api_resources:
      #           - name: statefulsets
      #             group: apps
      #           - name: statefulsets
      #             group: apps.kruise.io
      #             version: v1beta1
      #     ```
      #
      #     要采集 openshift 中的 `routes`，可以使用以下设置：
      #     ```yaml
      #     inputs:
      #       resources:
      #         kubernetes:
      #           api_resources:
      #           - name: ingresses
      #             disabled: true
      #           - name: routes
      #     ```
      # upgrade_from: static_config.kubernetes-resources
      # ---
      # type: string
      # name:
      #   en: Name
      #   ch: 名称
      # unit:
      # range: []
      # enum_options:
      #   - namespaces
      #   - nodes
      #   - pods
      #   - replicationcontrollers
      #   - services
      #   - daemonsets
      #   - deployments
      #   - replicasets
      #   - statefulsets
      #   - ingresses
      #   - routes
      #   - servicerules
      #   - clonesets
      #   - ippools
      #   - opengaussclusters
      #   - configmaps
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     K8s API resource name.
      #   ch: |-
      #     K8s API 资源名
      # upgrade_from: static_config.kubernetes-resources.name
      # ---
      # name: ""
      # ---
      # type: string
      # name:
      #   en: Group
      #   ch: 组
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     K8s API resource group.
      #   ch: |-
      #     K8s API 资源组
      # upgrade_from: static_config.kubernetes-resources.group
      # ---
      # group: ""
      # ---
      # type: string
      # name:
      #   en: Version
      #   ch: 版本
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     K8s API version.
      #   ch: |-
      #     K8s API 版本
      # upgrade_from: static_config.kubernetes-resources.version
      # ---
      # version: ""
      # ---
      # type: bool
      # name: Disabled
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     K8s API resource disabled.
      #   ch: |-
      #     禁用 K8s API 资源
      # upgrade_from: static_config.kubernetes-resources.disabled
      # ---
      # disabled: false
      # ---
      # type: string
      # name: Field Selector
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     K8s API resource field selector.
      #   ch: |-
      #     K8s API 资源字段选择器
      # upgrade_from: static_config.kubernetes-resources.field-selector
      # ---
      # field_selector: ""
      api_resources:
      - name: namespaces
      - name: nodes
      - name: pods
      - name: replicationcontrollers
      - name: services
      - name: daemonsets
      - name: deployments
      - name: replicasets
      - name: statefulsets
      - name: ingresses
      - name: configmaps
      # type: int
      # name:
      #   en: K8s API List Page Size
      #   ch: K8s API List 页大小
      # unit:
      # range: [10, 4294967295]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Used when limit k8s api list entry size.
      #   ch: |-
      #     用于指定 K8s 资源获取分页大小。
      # upgrade_from: static_config.kubernetes-api-list-limit
      api_list_page_size: 1000
      # type: duration
      # name:
      #   en: K8s API List Maximum Interval
      #   ch: K8s API List 最大间隔
      # unit:
      # range: [10m, 30d]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Interval of listing resource when watcher idles
      #   ch: |-
      #     当 watcher 未收到更新时，获取 K8s 资源的间隔时间。
      # upgrade_from: static_config.kubernetes-api-list-interval
      api_list_max_interval: 10m
      # type: string
      # name: Ingress Flavour
      # upgrade_from: static_config.ingress-flavour
      # deprecated: true
      ingress_flavour: kubernetes
      # type: string
      # name:
      #   en: Pod MAC Collection Method
      #   ch: Pod MAC 地址采集方法
      # unit:
      # range: []
      # enum_options: [adaptive, active, passive]
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     In active mode, deepflow-agent enters the netns of other Pods through
      #     setns syscall to query the MAC and IP addresses. In this mode, the setns
      #     operation requires the SYS_ADMIN permission. In passive mode deepflow-agent
      #     calculates the MAC and IP addresses used by Pods by capturing ARP/ND traffic.
      #     When set to adaptive, active mode will be used first.
      #   ch: |-
      #     - passive: deepflow-agent 采集 ARP/ND 数据包 计算其他 POD 的 MAC 和 IP 信息。
      #     - active: deepflow-agent 通过 setns 进入其他 POD 的 netns 查询 MAC 和 IP 信息（部署
      #       时需要 SYS_ADMIN 权限）。
      #     - adaptive: deepflow-agent 优先使用 active 模式获取其他 POD 的 MAC 和 IP 信息。
      # upgrade_from: static_config.kubernetes-poller-type
      pod_mac_collection_method: adaptive
    # type: section
    # name:
    #   en: Pull Resource From Controller
    #   ch: 从控制器拉取资源
    # description:
    #   en: |-
    #     Configurations for deepflow-server on pulling resources from controller.
    #     DeepFlow-agent will not read this section.
    #   ch: |-
    #     DeepFlow-server 从控制器拉取资源的配置。
    #     DeepFlow-agent 不会读取此部分。
    pull_resource_from_controller:
      # type: string
      # name:
      #   en: Domain Filter
      #   ch: 云平台过滤器
      # unit:
      # range: []
      # enum_options: [_DYNAMIC_OPTIONS_: _DYNAMIC_OPTIONS_]
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Default value `0` means all domains, or can be set to a list of lcuuid of a
      #     series of domains, you can get lcuuid through 'deepflow-ctl domain list'.
      #
      #     Note: The list of MAC and IP addresses is used by deepflow-agent to inject tags
      #     into data. This configuration can reduce the number and frequency of MAC and
      #     IP addresses delivered by deepflow-server to deepflow-agent. When there is no
      #     cross-domain service request, deepflow-server can be configured to only deliver
      #     the information in the local domain to deepflow-agent.
      #   ch: |-
      #     在运行过程中 deepflow-agent 周期性从 deepflow-server 获取 IP、MAC 列表，用于
      #     向采集的观测数据注入标签。该参数可以控制向 deepflow-agent 发送的 IP、MAC 数据范围，
      #     以减少下发的数据量。当业务系统中不存在跨云平台的服务访问时，可以配置仅向 deepflow-agent
      #     下发本云平台的数据。参数的默认值为`0`，表示获取所有云平台的数据；也可以设置 lcuuid 列表，
      #     仅获取部分云平台的数据。
      # upgrade_from: domains
      domain_filter: ["0"]
      # type: bool
      # name:
      #   en: Only K8s Pod IP in Local Cluster
      #   ch: 仅下发本集群中的 K8s Pod IP
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     The list of MAC and IP addresses is used by deepflow-agent to inject tags
      #     into data. This configuration can reduce the number and frequency of MAC and IP
      #     addresses delivered by deepflow-server to deepflow-agent. When the Pod IP is not
      #     used for direct communication between the K8s cluster and the outside world,
      #     deepflow-server can be configured to only deliver the information in the local
      #     K8s cluster to deepflow-agent.
      #   ch: |-
      #     运行过程中 deepflow-agent 周期性从 deepflow-server 获取 IP、MAC 列表，用于
      #     向采集的观测数据注入标签。该参数可以控制向 deepflow-agent 发送的 IP、MAC 数据范围，
      #     减少下发的数据量。当 Kubernetes 内部的 POD IP 不会直接与外部通信时，可以配置仅向 deepflow-agent
      #     下发本集群的 POD IP、MAC 数据。参数默认值为 `false`，表示发送全部。
      # upgrade_from: pod_cluster_internal_ip
      only_kubernetes_pod_ip_in_local_cluster: false
  # type: section
  # name:
  #   en: Integration
  #   ch: 集成
  # description:
  integration:
    # type: bool
    # name: Enabled
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     Whether to enable receiving external data sources such as Prometheus,
    #     Telegraf, OpenTelemetry, and SkyWalking.
    #   ch: |-
    #     开关开启后，deepflow-agent 将开启外部数据的接收服务接口，以集成来自 Prometheus、
    #     Telegraf、OpenTelemetry 和 Skywalking 的数据。
    # upgrade_from: external_agent_http_proxy_enabled
    enabled: true
    # type: int
    # name:
    #   en: Listen Port
    #   ch: 监听端口
    # unit:
    # range: [1, 65535]
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     Listen port of the data integration socket.
    #   ch: |-
    #     deepflow-agent 外部数据接收服务的监听端口。
    # upgrade_from: external_agent_http_proxy_port
    listen_port: 38086
    # type: section
    # name:
    #   en: Compression
    #   ch: 压缩
    # description:
    compression:
      # type: bool
      # name:
      #   en: Trace
      #   ch: Trace
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Whether to compress the integrated trace data received by deepflow-agent. The compression
      #     ratio is about 5:1~10:1. Turning on this feature will result in higher CPU consumption
      #     of deepflow-agent.
      #   ch: |-
      #     开启后，deepflow-agent 将对集成的追踪数据进行压缩处理，压缩比例在 5:1~10:1 之间。注意：
      #     开启此特性将增加 deepflow-agent 的 CPU 消耗。
      # upgrade_from: static_config.external-agent-http-proxy-compressed
      trace: true
      # type: bool
      # name:
      #   en: Profile
      #   ch: Profile
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Whether to compress the integrated profile data received by deepflow-agent. The compression
      #     ratio is about 5:1~10:1. Turning on this feature will result in higher CPU consumption
      #     of deepflow-agent.
      #   ch: |-
      #     开启后，deepflow-agent 将对集成的剖析数据进行压缩处理，压缩比例在 5:1~10:1 之间。注意：
      #     开启此特性将增加 deepflow-agent 的 CPU 消耗。
      # upgrade_from: static_config.external-agent-http-proxy-profile-compressed
      profile: true
    # type: section
    # name:
    #   en: Prometheus Extra Labels
    #   ch: Prometheus 额外 Label
    # description:
    #   en: |-
    #     Support for getting extra labels from headers in http requests from RemoteWrite.
    #   ch: |-
    #     deepflow-agent 支持从 Prometheus RemoteWrite 的 http header 中获取额外的 label。
    prometheus_extra_labels:
      # type: bool
      # name: Enabled
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Prometheus extra labels switch.
      #   ch: |-
      #     Prometheus 额外 lable 的获取开关。
      # upgrade_from: static_config.prometheus-extra-config.enabled
      enabled: false
      # type: string
      # name:
      #   en: Extra Labels
      #   ch: 额外 Label
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Labels list. Labels in this list are sent. Label is a string
      #     matching the regular expression `[a-zA-Z_][a-zA-Z0-9_]*`
      #   ch: |-
      #     Prometheus 额外 label 的列表。
      # upgrade_from: static_config.prometheus-extra-config.labels
      extra_labels: []
      # type: int
      # name:
      #   en: Label Key Total Length Limit
      #   ch: Label 键总长度限制
      # unit: byte
      # range: [1024, 1048576]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The limit of the total length of parsed extra Prometheus label keys.
      #   ch: |-
      #     deepflow-agent 对 Prometheus 额外 label 解析并采集时，key 字段长度总和的上限。
      # upgrade_from: static_config.prometheus-extra-config.labels-limit
      label_length: 1024
      # type: int
      # name:
      #   en: Value Total Length Limit
      #   ch: Label 值总长度限制
      # unit: byte
      # range: [4096, 4194304]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The limit of the total length of parsed extra Prometheus label values.
      #   ch: |-
      #     deepflow-agent 对 Prometheus 额外 label 解析并采集时，value 字段长度总和的上限。
      # upgrade_from: static_config.prometheus-extra-config.values-limit
      value_length: 4096
    # type: section
    # name:
    #   en: Feature Control
    #   ch: 特性开关
    # description:
    feature_control:
      # type: bool
      # name:
      #   en: Profile Integration Disabled
      #   ch: 禁用 Profile 集成
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      # upgrade_from: static_config.external-profile-integration-disabled
      profile_integration_disabled: false
      # type: bool
      # name:
      #   en: Trace Integration Disabled
      #   ch: 禁用 Trace 集成
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      # upgrade_from: static_config.external-trace-integration-disabled
      trace_integration_disabled: false
      # type: bool
      # name:
      #   en: Metric Integration Disabled
      #   ch: 禁用 Metric 集成
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      # upgrade_from: static_config.external-metric-integration-disabled
      metric_integration_disabled: false
      # type: bool
      # name:
      #   en: Log Integration Disabled
      #   ch: 禁用 Log 集成
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      # upgrade_from: static_config.external-log-integration-disabled
      log_integration_disabled: false
  # type: section
  # name:
  #   en: Vector
  #   ch: vector
  # description:
  vector:
    # type: bool
    # name:
    #   en: Vector Component Enabled
    #   ch: 启用 Vector 组件
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: true
    # description:
    #   en: |-
    #     The switcher control for Vector component running.
    #   ch: |-
    #     对 Vector 组件的开关控制。
    enabled: false
    # type: dict
    # name:
    #   en: Vector Component Config
    #   ch: Vector 组件配置控制
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: true
    # description:
    #   en: |-
    #     The detail config for Vector Component, all availble config keys could be found in [vector.dev](https://vector.dev/docs/reference/configuration)
    #     Here's an example for how to capture kubernetes logs、host metrics in virtual machine and kubelet metrics in kubernetes. It'll send to DeepFlow-Agent as output.
    #
    #     scrape host metrics:
    #     `K8S_NODE_NAME_FOR_DEEPFLOW` only required in k8s container environment
    #     {{ file: vector_host_metrics.yaml }}
    #
    #     scrape kubernetes metrics
    #     {{ file: vector_k8s_metrics.yaml }}
    #
    #     scrape kubernentes logs (capture DeepFlow Pod logs as example, if other Pod logs is required, update `extra_label_selector` add custom filters)
    #     {{ file: vector_k8s_logs.yaml }}
    #
    #     use http_client or socket to dial a remote server for testing
    #     {{ file: vector_dial.yaml }}
    #
    #   ch: |-
    #     Vector 组件的具体配置，所有可用配置可在此链接中查找：[vector.dev](https://vector.dev/docs/reference/configuration)
    #     以下提供一份抓取 kubernetes 日志、宿主机指标及 kubernetes kubelet 指标的示例，并将这些数据发送到 DeepFlow-Agent。
    #
    #     抓取主机指标
    #     `K8S_NODE_NAME_FOR_DEEPFLOW` 变量仅容器环境必须，非容器环境可以去掉
    #     {{ file: vector_host_metrics.yaml }}
    #
    #     抓取 kubernetes 指标
    #     {{ file: vector_k8s_metrics.yaml }}
    #
    #     抓取 kubernetes 日志(以采集 DeepFlow Pod 日志为例，若需要采集其他 Pod 日志可修改 `extra_label_selector` 并加上具体条件)
    #     {{ file: vector_k8s_logs.yaml }}
    #
    #     使用 http_client 或者 socket 拨测一个远端服务
    #     {{ file: vector_dial.yaml }}
    config:

# type: section
# name:
#   en: Processors
#   ch: 处理器
# description:
processors:
  # type: section
  # name: Packet
  # description:
  packet:
    # type: section
    # name: Policy
    # description:
    policy:
      # type: int
      # name:
      #   en: Fast-path Map Size
      #   ch: Fast-path 字典大小
      # unit:
      # range: [0, 10000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     When set to 0, deepflow-agent will automatically adjust the map size
      #     according to `global.limits.max_memory`.
      #     Note: In practice, it should not be set to less than 8000.
      #   ch: |-
      #     设置为`0`时，deepflow-agent 根据 `global.limits.max_memory` 参数自动调整 Fast-path 字典大小。
      #     注意：实践中不应配置小于 8000 的值。
      # upgrade_from: static_config.fast-path-map-size
      fast_path_map_size: 0
      # type: bool
      # name:
      #   en: Fast-path Disabled
      #   ch: 禁用 Fast-path
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     When set to `true`, deepflow-agent will not use fast path.
      #   ch: |-
      #     设置为 `true` 时，deepflow-agent 不启用 fast path。
      # upgrade_from: static_config.fast-path-disabled
      fast_path_disabled: false
      # type: int
      # name:
      #   en: Forward Table Capacity
      #   ch: Forward 表容量
      # unit:
      # range: [16384, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The size of the forwarding table, which is used to store MAC-IP information，
      #     When this value is larger, the more memory usage may be.
      #   ch: |-
      #     转发表大小，用来存储 MAC-IP 信息，调大该参数，deepflow-agent 将消耗更多的内存。
      # upgrade_from: static_config.forward-capacity
      forward_table_capacity: 16384
      # type: int
      # name:
      #   en: Max First-path Level
      #   ch: 最大 First-path 层级
      # unit:
      # range: [1, 16]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     DDBS algorithm level.
      #
      #     When this value is larger, the memory overhead is smaller, but the
      #     performance of policy matching is worse.
      #   ch: |-
      #     DDBS 算法等级。
      #
      #     该配置越大内存开销越小，但是性能会降低。
      # upgrade_from: static_config.first-path-level
      max_first_path_level: 8
    # type: section
    # name:
    #   en: TCP Header
    #   ch: TCP 包头（时序图）
    # description:
    tcp_header:
      # type: int
      # name:
      #   en: Block Size
      #   ch: Block 大小
      # unit:
      # range: [16, 8192]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     When generating TCP header data, each flow uses one block to compress and
      #     store multiple TCP headers, and the block size can be set here.
      #   ch: |-
      #     压缩和保存多个 TCP 包头的缓冲区大小。
      # upgrade_from: static_config.packet-sequence-block-size
      block_size: 256
      # type: int
      # name:
      #   en: Sender Queue Size
      #   ch: Sender 队列大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     The length of the following queues (to UniformCollectSender):
      #     - 1-packet-sequence-block-to-uniform-collect-sender
      #   ch: |-
      #     TCP 包时序数据的单个发送队列的大小。
      # upgrade_from: static_config.packet-sequence-queue-size
      sender_queue_size: 65536
      # type: int
      # name:
      #   en: Header Fields Flag
      #   ch: 包头字段 Flag
      # unit:
      # range: [0, 255]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     packet-sequence-flag determines which fields need to be reported, the default
      #     value is `0`, which means the feature is disabled, and `255`, which means all fields
      #     need to be reported all fields corresponding to each bit:
      #     ```
      #     | FLAG | SEQ | ACK | PAYLOAD_SIZE | WINDOW_SIZE | OPT_MSS | OPT_WS | OPT_SACK |
      #         7     6     5              4             3         2        1          0
      #     ```
      #   ch: |-
      #     使用一个 8 bit 的 flag 对 deepflow-agent 采集上报的 TCP 报文时序数据内容进行控制，不同
      #     的 bit 位代表不同 TCP 字段的采集开关：
      #     ```
      #     | FLAG | SEQ | ACK | PAYLOAD_SIZE | WINDOW_SIZE | OPT_MSS | OPT_WS | OPT_SACK |
      #         7     6     5              4             3         2        1          0
      #     ```
      #     flag 设置为`0`表示全部关闭，设置为`255`表示全部
      # upgrade_from: static_config.packet-sequence-flag
      header_fields_flag: 0b0000_0000
    # type: section
    # name:
    #   en: PCAP Stream
    #   ch: PCAP 字节流
    # description:
    pcap_stream:
      # type: int
      # name:
      #   en: Receiver Queue Size
      #   ch: Receiver 队列大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     The length of the following queues:
      #     - 1-mini-meta-packet-to-pcap
      #   ch: |-
      #     设置 deepflow-agent 的 1-mini-meta-packet-to-pcap 队列大小。
      # upgrade_from: static_config.pcap.queue-size
      receiver_queue_size: 65536
      # type: int
      # name:
      #   en: Buffer Size Per Flow
      #   ch: 每个 Flow 的缓冲区大小
      # unit:
      # range: [64, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     PCap buffer size per flow. Will flush the flow when reach this limit.
      #   ch: |-
      #     按流的 PCap 缓冲区大小。到达该值时 flush 该条流的 PCap 数据。
      # upgrade_from: static_config.pcap.flow-buffer-size
      buffer_size_per_flow: 65536
      # type: int
      # name:
      #   en: Total Buffer Size
      #   ch: 总体缓冲区大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     Total PCap buffer size. Will flush all flows when reach this limit.
      #   ch: |-
      #     PCap 总缓冲区大小。到达该值时 flush 所有流的 PCap 数据。
      # upgrade_from: static_config.pcap.buffer-size
      total_buffer_size: 88304
      # type: duration
      # name:
      #   en: Flush Interval
      #   ch: Flush 间隔
      # unit:
      # range: [1s, 10m]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     Flushes the PCap buffer of a flow if it has not been flushed for this interval.
      #   ch: |-
      #     如果一条流的 PCap buffer 超过这个时间没有进行过 flush，强制触发一次 flush。
      # upgrade_from: static_config.pcap.flush-interval
      flush_interval: 1m
    # type: section
    # name: TOA (TCP Option Address)
    # description:
    toa:
      # type: int
      # name:
      #   en: Sender Queue Size
      #   ch: Sender 队列大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The length of the following queues:
      #     - 1-socket-sync-toa-info-queue
      #   ch: |-
      #     以下队列的大小：
      #     - 1-socket-sync-toa-info-queue
      # upgrade_from: static_config.toa-sender-queue-size
      sender_queue_size: 65536
      # type: int
      # name:
      #   en: Cache Size
      #   ch: Cache 大小
      # unit:
      # range: [1, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Size of tcp option address info cache size.
      #   ch: |-
      #     TCP Option Address 信息缓存大小。
      # upgrade_from: static_config.toa-lru-cache-size
      cache_size: 65536
  # type: section
  # name:
  #   en: Request Log
  #   ch: 调用日志
  # description:
  request_log:
    # type: section
    # name:
    #   en: Application Protocol Inference
    #   ch: 应用协议推断
    # description:
    application_protocol_inference:
      # type: int
      # name:
      #   en: Inference Maximum Retries
      #   ch: 推断重试最大次数
      # unit:
      # range: [0, 10000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The agent records the application protocol resolution results of each server through a hash table, including the
      #     protocol, the number of continuous resolution failures, and the last resolution time
      #
      #     When an app protocol for Flow has never been successfully resolved, a hash table is used to decide which protocols
      #     to try to resolve:
      #     - If the result is not found in the hash table, or the result is not available (the protocol is unknown, or the
      #       number of failures exceeds the limit, or the time is more than inference_result_ttl from the current time)
      #       - If the number of failures has been exceeded, Flow is marked as prohibited for resolution for a period of
      #         inference_result_ttl
      #       - Otherwise, iterate through all open application protocols and try to parse them
      #         - When the parsing is successful, the protocol, parsing time, and number of failures (0) are updated to the hash
      #           table to keep the successful parsing results fresh
      #         - When parsing fails, the parsing time and number of failures (+1) are updated to the hash table so that the failed
      #           attempts can be accumulated, and subsequent attempts will be prohibited after the accumulation exceeds the threshold
      #       - If a specific, available protocol is found in the hash table, it is attempted using that protocol
      #         - When the parsing is successful, the protocol, parsing time, and number of failures (0) are updated to the hash table
      #           to keep the successful parsing results fresh
      #         - When parsing fails, the parsing time and number of failures (+1) are updated to the hash table so that the failed
      #           attempts can be accumulated, and subsequent attempts will be prohibited after the accumulation exceeds the threshold
      #
      #     Once a Flow is successfully parsed once, it will only use that protocol type to try to parse it once, and there is no need
      #     to query the hash table。
      #     Each time the resolution is successful, the protocol in the hash table (for HTTP2/gRPC needs to be updated), the resolution
      #     time, and the number of failures will be updated.
      #   ch: |-
      #     Agent 通过一张哈希表记录每一个服务端的应用协议解析结果，包括协议、持续解析失败次数、最后一次解析时间。
      #
      #     当一条 Flow 的应用协议从未成功解析过时，使用哈希表决定尝试解析哪些协议：
      #     - 若没有在哈希表中查到结果，或者查到的结果不可用（协议未知，或失败次数超限，或时间距当前超过 inference_result_ttl）
      #       - 若失败次数已经超限，则将 Flow 标记为禁止解析，禁止期为 inference_result_ttl
      #       - 否则，遍历所有开启的应用协议，尝试解析
      #         - 解析成功时会将协议、解析时间、失败次数（0）更新到哈希表中，使得成功的解析结果保鲜
      #         - 解析失败时会将解析时间和失败次数（+1）更新到哈希表中，使得失败的尝试能够累计，累计超过阈值后会禁止后续尝试
      #     - 如果在哈希表中查到了具体的、可用的协议，则使用该协议进行尝试
      #       - 解析成功时会将协议、解析时间、失败次数（0）更新到哈希表中，使得成功的解析结果保鲜
      #       - 解析失败时会将解析时间和失败次数（+1）更新到哈希表中，使得失败的尝试能够累计，累计超过阈值后会禁止后续尝试
      #
      #     当 Flow 一旦成功解析过一次，后续都仅使用该协议类型尝试解析，且无需再查询哈希表。
      #     每次解析成功时，将会更新哈希表中的协议（针对 HTTP2/gRPC 需进行更新）、解析时间、失败次数。
      # upgrade_from: static_config.l7-protocol-inference-max-fail-count
      inference_max_retries: 128
      # type: duration
      # name:
      #   en: Inference Result TTL
      #   ch: 推断结果 TTL
      # unit:
      # range: [0ns, 1d]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     deepflow-agent will mark the application protocol for each
      #     <vpc, ip, protocol, port> tuple. In order to avoid misidentification caused by IP
      #     changes, the validity period after successfully identifying the protocol will be
      #     limited to this value.
      #   ch: |-
      #     deepflow-agent 会周期性标记每一个<vpc, ip, protocol, port>四元组承载的应用协议类型，以加速
      #     后续数据的应用协议采集过程。为避免误判，应用协议类型的标记结果会周期性更新。该参数控制应用协议的更
      #     新周期。
      # upgrade_from: static_config.l7-protocol-inference-ttl
      inference_result_ttl: 60s
      # type: string
      # name:
      #   en: Enabled Protocols
      #   ch: 启用协议列表
      # unit:
      # range: []
      # enum_options: [_DYNAMIC_OPTIONS_]
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Turning off some protocol identification can reduce deepflow-agent resource consumption.
      #     Supported protocols: [https://www.deepflow.io/docs/features/l7-protocols/overview/](https://www.deepflow.io/docs/features/l7-protocols/overview/)
      #     <mark>Oracle and TLS is only supported in the Enterprise Edition.</mark>
      #   ch: |-
      #     deepflow-agent 仅对列表内的应用协议进行数据采集。通过该参数可以控制 agent 的数据采集范围以
      #     降低资源消耗。
      # upgrade_from: static_config.l7-protocol-enabled
      enabled_protocols:
        - HTTP
        - HTTP2
        - MySQL
        - Redis
        - Kafka
        - DNS
        - TLS
      # type: section
      # name:
      #   en: Protocol Special Config
      #   ch: 协议特殊配置
      # description:
      protocol_special_config:
        # type: section
        # name: Oracle
        # description:
        oracle:
          # type: bool
          # name:
          #   en: Integer Byte Order
          #   ch: Integer 字节序
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     Whether the oracle integer encode is big endian.
          #   ch: |-
          #     如果环境中 Oracle 整数编码采用大端字节序，则开启此开关。
          # upgrade_from: static_config.oracle-parse-config.is-be
          is_be: true
          # type: bool
          # name:
          #   en: Integer Compressed
          #   ch: Integer 压缩
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     Whether the oracle integer encode is compress.
          #   ch: |-
          #     如果环境中 Oracle 整数编码采用压缩，则开启此开关。
          # upgrade_from: static_config.oracle-parse-config.int-compress
          int_compressed: true
          # type: bool
          # name:
          #   en: Response 0x04 with Extra Byte
          #   ch: 0x04 响应携带额外字节
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     Due to the response with data id 0x04 has different struct in
          #     different version, it may has one byte before row affect.
          #   ch: |-
          #     在不同的 Oracle 版本中，ID 为 0x04 的响应会有不同的数据结构，如果环境中该响应数据的
          #     `影响行数`前有 1byte 的额外数据，请开启此开关。
          # upgrade_from: static_config.oracle-parse-config.resp-0x04-extra-byte
          resp_0x04_extra_byte: false
        # type: section
        # name: MySQL
        # description:
        mysql:
          # type: bool
          # name:
          #   en: Decompress MySQL Payload
          #   ch: 解压 MySQL 数据包
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     Some MySQL packets have payload compressed with LZ77 algorithm. Enable this option to decompress payload on parsing.
          #     Set to false to disable decompression for better performance.
          #     ref: [MySQL Source Code Documentation](https://dev.mysql.com/doc/dev/mysql-server/latest/page_protocol_basic_compression.html)
          #   ch: |-
          #     部分 MySQL 数据包采用 LZ77 压缩，开启此选项后，agent 在解析时会对数据包进行解压。
          #     设置为 false 以关闭解压，提升性能。
          #     参考：[MySQL Source Code Documentation](https://dev.mysql.com/doc/dev/mysql-server/latest/page_protocol_basic_compression.html)
          decompress_payload: true
        # type: section
        # name: Grpc
        # description:
        grpc:
          # type: bool
          # name:
          #   en: Enable gRPC stream data
          #   ch: 开启解析 gRPC stream 数据
          # unit:
          # range: []
          # enum_options: []
          # modification: agent_restart
          # ee_feature: false
          # description:
          #   en: |-
          #     When enabled, all gRPC packets are considered to be of the `stream` type, and the `data` will be reported,
          #     and the rrt calculation of the response will use the `grpc-status` field.
          #   ch: |-
          #     开启后所有 gRPC 数据包都认为是 `stream` 类型，并且会将 `data` 类型数据包上报，同时延迟计算的响应使用带有 `grpc-status` 字段的。
          streaming_data_enabled: false
      # type: dict
      # name:
      #   en: custom protocol parsing
      #   ch: 自定义协议解析
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     Custom protocol parsing configuration, which can be used to parse custom protocols through simple rules.
      #     Example:
      #     ```yaml
      #     - protocol_name: "your_protocol_name" # Protocol name, corresponding to l7_flow_log.l7_protocol_str
      #       pre_filter:
      #         port_list: 1-65535 # Pre-filter port, which can improve parsing performance
      #       request_characters:  # Multiple features are ORed
      #         - character: # Multiple match_keywords are ANDed
      #           - match_keyword: abc  # Feature string
      #             match_type: "string" # Possible values: "string", "hex"
      #             match_ignore_case: false # wheather ignore case when match keywords, when match_type == string effected, default: false
      #             match_from_beginning: false # Whether to match from the beginning of the payload
      #       response_characters:
      #         - character:
      #           - match_keyword: 0123af
      #             match_type: "hex"
      #             match_from_beginning: false
      #     ```
      #   ch: |-
      #     自定义协议解析配置，支持通过简单的规则识别用户自定义的 L7 协议。
      #     示例：
      #     ```yaml
      #     - protorol_name: "your_protocol_name" # 协议名称，对应 l7_flow_log.l7_protocol_str，注意：必须存在一个 `processors.request_log.tag_extraction.custom_field_policies` 配置，否则无法上报识别结果
      #       pre_filter:
      #         port_list: 1-65535 # 预过滤端口，可以提高解析性能
      #       request_characters:  # 多个特征之间是 OR 的关系
      #         - character: # 多个 match_keyword 之间是 AND 的关系
      #           - match_keyword: abc  # 特征字符串
      #             match_type: "string" # 取值："string", "hex"
      #             match_ignore_case: false # 匹配特征字符串是否忽略大小写，当 match_type == string 时生效，默认值: false
      #             match_from_begining: false # 是否需要从 Payload 头部开始匹配
      #       response_characters:
      #         - character:
      #           - match_keyword: 0123af
      #             match_type: "hex"
      #             match_from_begining: false
      #     ```
      custom_protocols: []
    # type: section
    # name:
    #   en: Filters
    #   ch: 过滤器
    # description:
    filters:
      # type: dict
      # name:
      #   en: Port Number Pre-filters
      #   ch: 端口号预过滤器
      # unit:
      # range: []
      # enum_options: [_DYNAMIC_OPTIONS_]
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Port-list example:
      #     ```
      #     HTTP: 80,1000-2000
      #     HTTP2: 1-65535
      #     ```
      #
      #     NOTE:
      #     1. HTTP2 and TLS are only used for Kprobe, not applicable to Uprobe.
      #        All data obtained through Uprobe is not subject to port restrictions.
      #        - Supported protocols: [https://www.deepflow.io/docs/features/l7-protocols/overview/](https://www.deepflow.io/docs/features/l7-protocols/overview/)
      #        - <mark>Oracle and TLS is only supported in the Enterprise Edition.</mark>
      #     2. Attention: use `HTTP2` for `gRPC` Protocol.
      #   ch: |-
      #     配置样例:
      #     ```
      #     HTTP: 80,1000-2000
      #     HTTP2: 1-65535
      #     ```
      #
      #     注意：
      #     1. 该参数中，HTTP2 和 TLS 协议的配置仅对 Kprobe 有效，对 Uprobe 无效；
      #        - 支持协议：[https://www.deepflow.io/docs/zh/features/l7-protocols/overview/](https://www.deepflow.io/docs/zh/features/l7-protocols/overview/)
      #        - <mark>Oracle 和 TLS 仅在企业版中支持。</mark>
      #     2. 如需控制 `gRPC` 协议，请使用 `HTTP2` 配置。
      # upgrade_from: static_config.l7-protocol-ports
      port_number_prefilters:
        HTTP: 1-65535
        HTTP2: 1-65535
        Dubbo: 1-65535
        SofaRPC: 1-65535
        FastCGI: 1-65535
        bRPC: 1-65535
        Tars: 1-65535
        SomeIP: 1-65535
        MySQL: 1-65535
        PostgreSQL: 1-65535
        Oracle: 1521
        Redis: 1-65535
        MongoDB: 1-65535
        Memcached: 11211
        Kafka: 1-65535
        MQTT: 1-65535
        AMQP: 1-65535
        OpenWire: 1-65535
        NATS: 1-65535
        Pulsar: 1-65535
        ZMTP: 1-65535
        RocketMQ: 1-65535
        DNS: 53,5353
        TLS: 443,6443
        PING: 1-65535
        Custom: 1-65535 # plugins
      # type: dict
      # name:
      #   en: Tag Filters
      #   ch: Tag 过滤器
      # unit:
      # range: []
      # enum_options: [_DYNAMIC_OPTIONS_]
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Tag filter example:
      #     ```yaml
      #     processors:
      #       request_log:
      #         filters:
      #           tag_filters:
      #             HTTP:
      #               - field_name: request_resource  # endpoint, request_type, request_domain, request_resource
      #                 operator: equal               # equal, prefix
      #                 value: somevalue
      #             HTTP2: []
      #             # other protocols
      #     ```
      #     A l7_flow_log blacklist can be configured for each protocol, preventing request logs matching
      #     the blacklist from being collected by the agent or included in application performance metrics.
      #     It's recommended to only place non-business request logs like heartbeats or health checks in this
      #     blacklist. Including business request logs might lead to breaks in the distributed tracing tree.
      #
      #     Supported protocols: [https://www.deepflow.io/docs/features/l7-protocols/overview/](https://www.deepflow.io/docs/features/l7-protocols/overview/)
      #
      #     <mark>Oracle and TLS is only supported in the Enterprise Edition.</mark>
      #   ch: |-
      #     控制不同应用协议数据采集时的 Tag。协议名不区分大小写。
      #     Tag filter 配置例子:
      #     ```yaml
      #     processors:
      #       request_log:
      #         filters:
      #           tag_filters:
      #             HTTP:
      #               - field_name: request_resource  # endpoint, request_type, request_domain, request_resource
      #                 operator: equal               # equal, prefix
      #                 value: somevalue
      #             HTTP2: []
      #             # 其他协议
      #     ```
      # upgrade_from: static_config.l7-log-blacklist
      tag_filters:
        # type: dict
        # name:
        #   en: "$HTTP Tag Filters"
        #   ch: "$HTTP Tag 过滤器"
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     HTTP Tag filter example:
        #     ```yaml
        #     processors:
        #       request_log:
        #         filters:
        #           tag_filters:
        #             HTTP:
        #               - field_name: request_resource  # endpoint, request_type, request_domain, request_resource
        #                 operator: equal               # equal, prefix
        #                 value: somevalue
        #     ```
        #     A l7_flow_log tag_filter can be configured for each protocol, preventing request logs matching
        #     the blacklist from being collected by the agent or included in application performance metrics.
        #     It's recommended to only place non-business request logs like heartbeats or health checks in this
        #     blacklist. Including business request logs might lead to breaks in the distributed tracing tree.
        #
        #     Supported protocols: https://www.deepflow.io/docs/features/l7-protocols/overview/
        #
        #     <mark>Oracle and TLS is only supported in the Enterprise Edition.</mark>
        # upgrade_from: static_config.l7-log-blacklist.$protocol
        # ---
        # type: string
        # name:
        #   en: Field Name
        #   ch: 字段名
        # unit:
        # range: []
        # enum_options: [endpoint, request_type, request_domain, request_resource]
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Match field name.
        #   ch: |-
        #     匹配字段名
        # upgrade_from: static_config.l7-log-blacklist.$protocol.field-name
        # ---
        # field_name: ""
        # ---
        # type: string
        # name:
        #   en: Operator
        #   ch: 匹配操作符
        # unit:
        # range: []
        # enum_options: [equal, prefix]
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Match operator.
        #   ch: |-
        #     匹配操作符
        # upgrade_from: static_config.l7-log-blacklist.$protocol.operator
        # ---
        # operator: ""
        # ---
        # type: string
        # name:
        #   en: Field Value
        #   ch: 字段值
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Match field value.
        #   ch: |-
        #     匹配字段值。
        # upgrade_from: static_config.l7-log-blacklist.$protocol.value
        # ---
        # field_value: ""
        HTTP: []
        HTTP2: []
        Dubbo: []
        gRPC: []
        SOFARPC: []
        FastCGI: []
        bRPC: []
        Tars: []
        SomeIP: []
        MySQL: []
        PostgreSQL: []
        Oracle: []
        Redis: []
        MongoDB: []
        Memcached: []
        Kafka: []
        MQTT: []
        AMQP: []
        OpenWire: []
        NATS: []
        Pulsar: []
        ZMTP: []
        RocketMQ: []
        DNS: []
        TLS: []
        PING: []
        Custom: []
      # type: string
      # name:
      #   en: Unconcerned DNS NXDOMAIN
      #   ch: 不关心的 DNS NXDOMAIN 错误
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     You might not be concerned about certain DNS NXDOMAIN errors and may wish to ignore
      #     them. For example, when a K8s Pod tries to resolve an external domain name, it first
      #     concatenates it with the internal domain suffix of the cluster and attempts to resolve
      #     it. All these attempts will receive an NXDOMAIN reply before it finally requests the
      #     original domain name directly, and these errors may not be of concern to you. In such
      #     cases, you can configure their `response_result` suffix here, so that the corresponding
      #     `response_status` in the l7_flow_log is forcibly set to `Success`.
      #   ch: |-
      #     配置该参数后，当系统中 DNS 响应异常为 `Non-Existent Domain`，且响应结果中的后缀与参数中的字段
      #     匹配时， deepflow-agent 会将 DNS 响应码置为`0`，响应状态置为`正常`。
      #     该特性用于忽略特定的 `Non-Existent Domain` 类型的 DNS 响应，比如 K8s Pod 解析外部域名时，会将
      #     待解析域名与 cluster 内的域名后缀做拼接并多次尝试解析，因而会产生多次的 `Non-Existent Domain`
      #     的响应结果，干扰数据分析。
      # upgrade_from: static_config.l7-protocol-advanced-features.unconcerned-dns-nxdomain-response-suffixes
      unconcerned_dns_nxdomain_response_suffixes: []
      # type: bool
      # name: cBPF data disabled
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When disabled, deepflow-agent will not generate request_log from packet data.
      #   ch: |-
      #     关闭后 deepflow-agent 将停止从 packet 数据生成调用日志。
      cbpf_disabled: false
    # type: section
    # name:
    #   en: Timeouts
    #   ch: 超时设置
    # description:
    timeouts:
      # type: duration
      # name:
      #   en: TCP Request Timeout
      #   ch: TCP 调用超时时间
      # unit:
      # range: [10s, 3600s]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The timeout of l7 log info rrt calculate, when rrt exceed the value will act as timeout and will not
      #     calculate the sum and average and will not merge the request and response in session aggregate. the value
      #     must greater than the timeout period of the TCP type in configured `processors.request_log.timeouts.session_aggregate`
      #     (For example, the HTTP2 default is 120s) and less than 3600s on tcp.
      #   ch: |-
      #     deepflow-agent 采集 TCP 承载的应用调用时等待响应消息的最大时长，如果响应与请求之间的时间差超过
      #     该参数值，该次调用将被识别为超时。该参数需大于配置 `processors.request_log.timeouts.session_aggregate`
      #     中 TCP 类型的超时时间（例如 HTTP2 默认值 120s），并小于 3600s。
      # upgrade_from: static_config.rrt-tcp-timeout
      tcp_request_timeout: 300s
      # type: duration
      # name:
      #   en: UDP Request Timeout
      #   ch: UDP 调用超时时间
      # unit:
      # range: [10s, 300s]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The timeout of l7 log info rrt calculate, when rrt exceed the value will act as timeout and will not
      #     calculate the sum and average and will not merge the request and response in session aggregate. the value
      #     must greater than the timeout period of the UDP type in configured `processors.request_log.timeouts.session_aggregate`
      #     (For example, the DNS default is 15s) and less than 300 on udp.
      #   ch: |-
      #     deepflow-agent 采集 UDP 承载的应用调用时等待响应消息的最大时长，如果响应与请求之间的时间差超过该参数值，该次调用将被识别为超时。
      #     该参数需大于配置 `processors.request_log.timeouts.session_aggregate` 中 UDP 类型的超时时间（例如 DNS 默认值 15s），并小于 300s。
      # upgrade_from: static_config.rrt-udp-timeout
      udp_request_timeout: 150s
      # type: duration
      # name:
      #   en: Session Aggregate Window Duration
      #   ch: 会话合并窗口时长
      # unit:
      # range: [20s, 300s]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     l7_flow_log aggregate window.
      #   ch: |-
      #     应用调用日志请求、响应合并的时间窗口，超出该时间窗口的响应将不与请求合并，而是单独生成一条调用日志。
      # upgrade_from: static_config.l7-log-session-aggr-timeout
      # deprecated: true
      session_aggregate_window_duration: 120s
      # type: dict
      # name:
      #   en: Application Session Aggregate Timeouts
      #   ch: 应用会话合并超时设置
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Set the aggregation timeout for each application.
      #     The default values is 15s for DNS and TLS, 120s for others.
      #
      #     Example:
      #     ```yaml
      #     processors:
      #       request_log:
      #         timeouts:
      #           session_aggregate:
      #           - protocol: DNS
      #             timeout: 15s
      #           - protocol: HTTP2
      #             timeout: 120s
      #     ```
      #
      #   ch: |-
      #     设置每个应用的超时时间。
      #     DNS 和 TLS 默认 15s，其他协议默认 120s。
      #
      #     示例:
      #     ```yaml
      #     processors:
      #       request_log:
      #         timeouts:
      #           session_aggregate:
      #           - protocol: DNS
      #             timeout: 15s
      #           - protocol: HTTP2
      #             timeout: 120s
      #     ```
      #
      # upgrade_from:
      # ---
      # type: string
      # name:
      #   en: Protocol
      #   ch: 协议
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Protocol Name for timeout setting.
      #   ch: |-
      #     用于设置超时时间的协议名称。
      # upgrade_from:
      # ---
      # protocol: ""
      # ---
      # type: duration
      # name:
      #   en: Timeout
      #   ch: 超时时间
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Set the timeout for the application. The timeout period of TCP application protocols must be less than
      #     `processors.request_log.timeouts.tcp_request_timeout`, and the timeout period of UDP must be less than
      #     `processors.request_log.timeouts.udp_request_timeout`.
      #   ch: |-
      #     设置应用的超时时间。TCP 类型的应用协议超时时间需要小于 `processors.request_log.timeouts.tcp_request_timeout`，
      #     UDP 类型的应用协议超时时间需要小于 `processors.request_log.timeouts.udp_request_timeout`。
      # upgrade_from:
      # ---
      # timeout: 0
      session_aggregate: []
    # type: section
    # name:
    #   en: Tag Extraction
    #   ch: 标签提取
    # description:
    tag_extraction:
      # type: section
      # name:
      #   en: Tracing Tag
      #   ch: Tracing 标签
      # description:
      tracing_tag:
        # type: string
        # name:
        #   en: HTTP Real Client
        #   ch: HTTP 真实客户端
        # unit:
        # range: []
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     It is used to extract the real client IP field in the HTTP header,
        #     such as X-Forwarded-For, etc. Leave it empty to disable this feature.
        #     If multiple values are specified, the first match will be used.
        #     Fields rewritten by plugins have the highest priority.
        #   ch: |-
        #     配置该参数后，deepflow-agent 会尝试从 HTTP header 中匹配特征字段，并将匹配到
        #     的结果填充到应用调用日志的`http_proxy_client`字段中，作为调用链追踪的特征值。
        #     如果指定多个值，优先级从前到后降低。插件重写的字段优先级最高。
        # upgrade_from: http_log_proxy_client
        http_real_client: [X_Forwarded_For]
        # type: string
        # name: X-Request-ID
        # unit:
        # range: []
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     It is used to extract the fields in the HTTP header that are used
        #     to uniquely identify the same request before and after the gateway,
        #     such as X-Request-ID, etc. This feature can be turned off by setting
        #     it to empty.
        #     If multiple values are specified, the first match will be used.
        #     Fields rewritten by plugins have the highest priority.
        #   ch: |-
        #     配置该参数后，deepflow-agent 会尝试从 HTTP header 中匹配特征字段，并将匹配到
        #     的结果填充到应用调用日志的`x_request_id`字段中，作为调用链追踪的特征值。
        #     如果指定多个值，优先级从前到后降低。插件重写的字段优先级最高。
        # upgrade_from: http_log_x_request_id
        x_request_id: [X_Request_ID]
        # type: string
        # name: APM TraceID
        # unit:
        # range: []
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     Used to extract the TraceID field in HTTP and RPC headers, supports filling
        #     in multiple values separated by commas. This feature can be turned off by
        #     setting it to empty.
        #     If multiple values are specified, the first match will be used.
        #     Fields rewritten by plugins have the highest priority.
        #   ch: |-
        #     配置该参数后，deepflow-agent 会尝试从 HTTP 和 RPC header 中匹配特征字段，并将匹配到
        #     的结果填充到应用调用日志的`trace_id`字段中，作为调用链追踪的特征值。参数支持填写多个不同的
        #     特征字段，中间用`,`分隔。
        #     如果指定多个值，优先级从前到后降低。插件重写的字段优先级最高。
        # upgrade_from: http_log_trace_id
        apm_trace_id: [traceparent, sw8]
        # type: string
        # name: APM SpanID
        # unit:
        # range: []
        # enum_options: []
        # modification: hot_update
        # ee_feature: false
        # description:
        #   en: |-
        #     Used to extract the SpanID field in HTTP and RPC headers, supports filling
        #     in multiple values separated by commas. This feature can be turned off by
        #     setting it to empty.
        #     If multiple values are specified, the first match will be used.
        #     Fields rewritten by plugins have the highest priority.
        #   ch: |-
        #     配置该参数后，deepflow-agent 会尝试从 HTTP 和 RPC header 中匹配特征字段，并将匹配到
        #     的结果填充到应用调用日志的`span_id`字段中，作为调用链追踪的特征值。参数支持填写多个不同的
        #     特征字段，中间用`,`分隔。
        #     如果指定多个值，优先级从前到后降低。插件重写的字段优先级最高。
        # upgrade_from: http_log_span_id
        apm_span_id: [traceparent, sw8]
      # type: section
      # name:
      #   en: HTTP Endpoint
      #   ch: HTTP 端点
      # description:
      http_endpoint:
        # type: bool
        # name:
        #   en: Extraction Disabled
        #   ch: 禁用提取
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     HTTP endpoint extration is enabled by default.
        #   ch: |-
        #     默认值为`false`，表示开启 HTTP 协议的 endpoint 提取功能；设置为`true`时，表示关闭该功能。
        # upgrade_from: static_config.l7-protocol-advanced-features.http-endpoint-extraction.disabled
        extraction_disabled: false
        # type: dict
        # name:
        #   en: Match Rules
        #   ch: 匹配规则
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Extract endpoint according to the following rules:
        #     - Find a longest prefix that can match according to the principle of
        #       "longest prefix matching"
        #     - Intercept the first few paragraphs in URL (the content between two
        #       / is regarded as one paragraph) as endpoint
        #
        #     By default, two segments are extracted from the URL. For example, the
        #     URL is `/a/b/c?query=xxx`, whose segment is 3, extracts `/a/b` as the
        #     endpoint.
        #   ch: |-
        #     HTTP 协议的 endpoint 提取遵循如下规则：
        #     - 最长匹配原则：优先匹配最长的前缀；
        #     - 提取 URL 最前的数段（段数由参数确定，默认值为 2）作为 endpoint。
        #     比如，URL 为 `/a/b/c?query=xxx`，deepflow-agent 默认提取 `/a/b` 作为 endpoint。
        # upgrade_from: static_config.l7-protocol-advanced-features.http-endpoint-extraction.match-rules
        # ---
        # type: string
        # name:
        #   en: URL Prefix
        #   ch: URL 前缀
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     HTTP URL prefix.
        #   ch: |-
        #     HTTP URL 前缀。
        # upgrade_from: static_config.l7-protocol-advanced-features.http-endpoint-extraction.match-rules.prefix
        # ---
        # url_prefix: ""
        # ---
        # type: int
        # name:
        #   en: Keep Segments
        #   ch: 截取 Segment 数
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Keep how many segments.
        #   ch: |-
        #     截取 URL 的段数。
        # upgrade_from: static_config.l7-protocol-advanced-features.http-endpoint-extraction.match-rules.keep-segments
        # ---
        # keep_segments: 0
        match_rules:
        - url_prefix: ""
          keep_segments: 2
      # type: dict
      # name:
      #   en: Custom Fields
      #   ch: 自定义字段
      # unit:
      # range: []
      # enum_options: [HTTP, HTTP2]
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Configuration to extract the customized header fields of HTTP, HTTP2, gRPC protocol etc.
      #
      #     Example:
      #     ```yaml
      #     processors:
      #       request_log:
      #         tag_extraction:
      #           custom_fields:
      #             HTTP:
      #             - field_name: "user-agent"
      #             - field_name: "cookie"
      #     ```
      #
      #     Attention: use `HTTP2` for `gRPC` Protocol.
      #   ch: |-
      #     配置 HTTP、HTTP2、gRPC 等协议的额外提取字段。
      #
      #     示例:
      #     ```yaml
      #     processors:
      #       request_log:
      #         tag_extraction:
      #           custom_fields:
      #             HTTP:
      #             - field_name: "user-agent"
      #             - field_name: "cookie"
      #     ```
      #
      #     注意：如需配置`gRPC`协议，使用`HTTP2`匹配。
      # upgrade_from: static_config.l7-protocol-advanced-features.extra-log-fields
      custom_fields:
        # type: dict
        # name:
        #   en: "$HTTP Custom Fields"
        #   ch: "$HTTP 自定义字段"
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Configuration to extract the customized header fields of HTTP, HTTP2, gRPC protocol etc.
        #
        #     Example:
        #     ```yaml
        #     processors:
        #       request_log:
        #         tag_extraction:
        #           custom_fields:
        #             HTTP:
        #             - field_name: "user-agent"
        #             - field_name: "cookie"
        #     ```
        #
        #     Attention: use `HTTP2` for `gRPC` Protocol.
        #   ch: |-
        #     配置 HTTP、HTTP2、gRPC 等协议的额外提取字段。
        #
        #     示例:
        #     ```yaml
        #     processors:
        #       request_log:
        #         tag_extraction:
        #           custom_fields:
        #             HTTP:
        #             - field_name: "user-agent"
        #             - field_name: "cookie"
        #     ```
        #
        #     注意：如需配置`gRPC`协议，使用`HTTP2`。
        # upgrade_from: static_config.l7-protocol-advanced-features.extra-log-fields.$protocol
        # ---
        # type: string
        # name:
        #   en: Field Name
        #   ch: 字段名
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Field name.
        #   ch: |-
        #     字段名
        # upgrade_from: static_config.l7-protocol-advanced-features.extra-log-fields.$protocol.field-name
        # ---
        # field_name: ""
        HTTP: []
        HTTP2: []
      # type: dict
      # name:
      #   en: Custom Fields Policies
      #   ch: 自定义字段提取策略
      # unit:
      # range: []
      # modification: agent_restart
      # ee_feature: true
      # description:
      #   en: |-
      #     Custom field extraction policies, used to extract custom fields from L7 protocols
      #     Example:
      #     ```yaml
      #     - policy_name: "my_policy" # name of current policy
      #       protocol_name: HTTP # protocol name, if protocol is Grpc, please set it to HTTP2, optional values: HTTP/HTTP2/Dubbo/SofaRPC/Custom/...
      #       custom_protocol_name: "my_protocol"  # when protocol_name is Custom are effected, and there must be a `processors.request_log.application_protocol_inference.custom_protocols` configuration with the same name, otherwise it cannot be parsed
      #       port_list: 1-65535
      #       fields:
      #       - field_name: "my_field"
      #         field_match_type: "string"  # optional values: "string"
      #         field_match_ignore_case: "false" # wheather ignore case when match field, default: false
      #         field_match_keyword: "abc"  # can be filled with additional characters to improve accuracy, for example `"\"abc\": \""`
      #
      #         subfield_match_keyword: "y" # in some cases, we need to extract a subfield, for example, in the HTTP Cookie field, we only need to extract part of it, such as extracting the value corresponding to y from `abc: x=1,y=2,z=3` (the value is `x=1,y=2,z=3`)
      #         separator_between_subfield_kv_pair: "," # default: empty
      #         separator_between_subfield_key_and_value: "=" # default: empty
      #
      #         field_type: "http_url_field" # field type of extraction, optional values: http_url_field/header_field/payload_json_value/payload_xml_value/payload_hessian2_value, default value: header_field
      #         traffic_direction: request # could be limited to search only in request (or only in response), optional values: request/response/both, default value: both
      #         check_value_charset: false # used for checking whether the extracted result is legal
      #         value_primary_charset: ["digits", "alphabets", "chinese"] # used for checking the character set of the extracted result, optional values: digits/alphabets/chinese
      #         value_special_charset: ".-_" # used for checking the character set of the extracted result
      #         attribute_name: "xyz" # this field will appear in the calling log's attribute.xyz, default value is empty, if empty, this field will not be added to attribute
      #         rewrite_native_tag: version # rewrite can fill in one of the following fields to overwrite the corresponding field value: version/request_type/request_domain/request_resource/request_id/endpoint/response_code/response_exception/response_result/trace_id/span_id/x_request_id/http_proxy_client
      #         rewrite_response_status: # rewrite response_status field, when response_code is in success_values array, response_status will be set to success, otherwise set to server_error
      #           success_values: []
      #         metric_name: "xyz"  # this field will appear in the calling log's metrics.xyz, default value is empty
      #     ```
      #     notice: the different values of field_type will affect the extraction method of the field, as follows:
      #     - `http_url_field`: extract field from HTTP URL parameters at the end of the URL, such as `?key=value&key2=value2`
      #     - `header_field`: extract field from the Header part of HTTP/Dubbo/SofaRPC/... protocols, such as HTTP Header like `key: value`
      #     - `payload_json_value`: extract field from Json Payload, such as `"key": 1`, or `"key": "value"`, or `"key": None`, etc.
      #     - `payload_xml_value`: extract field from XML Payload, such as `<key attr="xxx">value</key>`
      #     - `payload_hessian2_value`: extract field from Payload encoded with Hessian2
      #   ch: |-
      #     自定义字段提取策略，用于通过简单的规则提取 L7 协议中可能存在的自定义字段
      #     示例：
      #     ```yaml
      #     - policy_name: "my_policy" # 策略名称
      #       protocol_name: HTTP # 协议名称，如要解析 Grpc 请配置为 HTTP2，可选值： HTTP/HTTP2/Dubbo/SofaRPC/Custom/...
      #       custom_protocol_name: "my_protocol"  # 当 protocol_name 为 Custom 时生效，注意：此时必须存在一个 `processors.request_log.application_protocol_inference.custom_protocols` 配置，且自定义名称协议名称相等，否则无法解析
      #       port_list: 1-65535
      #       fields:
      #       - field_name: "my_field" # 配置的字段
      #         field_match_type: "string" # 可选值："string"
      #         field_match_ignore_case: "false" # 当匹配 field 时是否忽略大小写，默认值：false
      #         field_match_keyword: "abc" # 可以填写额外的字符以提升匹配准确率，例如 `"\"abc\": \""`
      #
      #         subfield_match_keyword: "y" # 有些情况下，我们需要提取一个子字段，例如 HTTP 的 Cookie 字段中，我们仅仅只需要提取其中的一部分，例如，我们要从 `abc: x=1,y=2,z=3` 的 Value（`x=1,y=2,z=3`）中提取 y 对应的值
      #         separator_between_subfield_kv_pair: "," # 用于分割 key-value 键值对的分隔符，默认值：空
      #         separator_between_subfield_key_and_value: "=" # 用于分割 key 和 value 的分隔符，默认值：空
      #
      #         field_type: "http_url_field" # 字段的提取类型，可选值：http_url_field/header_field/payload_json_value/payload_xml_value/payload_hessian2_value，默认值为 `header_field`，含义见下方说明
      #         traffic_direction: request # 可以限定仅在请求（或仅在响应）中搜索，默认值为 both，可选值：request/response/both
      #         check_value_charset: false # 可用于检查提取结果是否合法
      #         value_primary_charset: ["digits", "alphabets", "chinese"] # 提取结果校验字符集，可选值：digits/alphabets/chinese
      #         value_special_charset: ".-_" # 提取结果校验字符集，额外校验这些特殊字符
      #         attribute_name: "xyz" # 此时该字段将会出现在调用日志的 attribute.xyz 中，默认值为空，为空时该字段不会加入到 attribute 内
      #         rewrite_native_tag: version # rewrite 可以填写以下几种字段之一，用于覆写对应字段的值：version/request_type/request_domain/request_resource/request_id/endpoint/response_code/response_exception/response_result/trace_id/span_id/x_request_id/http_proxy_client
      #         rewrite_response_status: # rewrite response_status 字段，当 response_code 在 success_values 数组中时，会将 response_status 设置为 success，否则设置为 server_error
      #           success_values: []
      #         metric_name: "xyz" # 此时该字段将会出现在调用日志的 metrics.xyz 中，默认值为空
      #     ```
      #     注意，其中 field_type 的不同值会影响到该字段的提取方式，具体如下：
      #     - `http_url_field`：从 HTTP URL 末尾的参数中提取字段，URL 末尾形如：`?key=value&key2=value2`
      #     - `header_field`：从 HTTP/Dubbo/SofaRPC/...等协议的 Header 部分提取字段，例如 HTTP 的 Header 形如：`key: value`
      #     - `payload_json_value`：从 Json Payload 中提取字段，形如：`"key": 1`,  或者 `"key": "value"`,  或者 `"key": None`, 等等 ...
      #     - `payload_xml_value`：从 XML Payload 中提取字段，形如：`<key attr="xxx">value</key>`
      #     - `payload_hessian2_value`：Payload 使用 Hessian2 编码，从中提取字段
      custom_field_policies: []
      # type: string
      # name:
      #   en: Obfuscate Protocols
      #   ch: 脱敏协议列表
      # unit:
      # range: []
      # enum_options: [MySQL, PostgreSQL, HTTP, HTTP2, Redis]
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     For the sake of data security, the data of the protocol that needs
      #     to be desensitized is configured here and is not processed by default.
      #     Obfuscated fields mainly include:
      #     - Authorization information
      #     - Value information in various statements
      #   ch: |-
      #     配置该参数后，deepflow-agent 将在采集时对特定应用协议的关键数据做脱敏处理。
      #     脱敏字段主要包括：
      #     - 授权信息
      #     - 各类语句中的 value 信息
      # upgrade_from: static_config.l7-protocol-advanced-features.obfuscate-enabled-protocols
      obfuscate_protocols: [Redis]
    # type: section
    # name:
    #   en: Tunning
    #   ch: 调优
    # description:
    tunning:
      # type: int
      # name:
      #   en: Payload Truncation
      #   ch: Payload 截取
      # unit: byte
      # range: [256, 65535]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     The maximum data length used for application protocol identification,
      #     note that the effective value is less than the value of
      #     `inputs.cbpf.tunning.max_capture_packet_size`.
      #
      #     NOTE: For eBPF data, the largest valid value is 16384.
      #   ch: |-
      #     应用调用日志采集解析的最大 payload 长度。注意实际的值小于 `inputs.cbpf.tunning.max_capture_packet_size`。
      #     注意：eBPF 数据的 payload 可解析长度上限为 16384 Byte。
      # upgrade_from: l7_log_packet_size
      payload_truncation: 1024
      # type: int
      # name:
      #   en: Session Aggregate Slot Capacity
      #   ch: 会话聚合桶容量
      # unit:
      # range: [1024, 1000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     By default, unidirectional l7_flow_log is aggregated into bidirectional
      #     request_log (session) with a caching time window of 2 minutes. During this
      #     period, every 5 seconds is considered as a time slot (i.e., a LRU). This
      #     configuration is used to specify the maximum number of unidirectional l7_flow_log
      #     entries that can be cached in each time slot.
      #
      #     If the number of l7_flow_log entries cached in a time slot exceeds this
      #     configuration, 10% of the data in that time slot will be evicted based on the
      #     LRU strategy to reduce memory consumption. Note that the evicted data will not be
      #     discarded; instead, they will be sent to the deepflow-server as unidirectional
      #     request_log.
      #
      #     The following metrics can be used as reference data for adjusting this configuration:
      #     - Metric `deepflow_system.deepflow_agent_l7_session_aggr.cached-request-resource`
      #       Used to record the total memory occupied by the request_resource field of the
      #       unidirectional l7_flow_log cached in all time slots at the current moment, in bytes.
      #     - Metric `deepflow_system.deepflow_agent_l7_session_aggr.over-limit`
      #       Used to record the number of times eviction is triggered due to reaching the
      #       LRU capacity limit.
      #   ch: |-
      #     默认情况下，2 分钟缓存窗口中的单向 l7_flow_log 将被聚合成双向的 request_log（会话）。
      #     聚合时的槽位大小为 5 秒。该配置用于指定每个时间槽中最多可以缓存多少个单向的 l7_flow_log 条目。
      #
      #     如果某个时间槽中的 l7_flow_log 条目数量超过该配置，则该时间槽中 10% 的 l7_flow_log 条目将被
      #     LRU 策略淘汰以减少内存占用。注意，被淘汰的 l7_flow_log 条目不会被丢弃，而是作为单向的 request_log
      #     发送给 deepflow-server。
      #
      #     以下指标可以作为调整该配置的参考数据：
      #     - Metric `deepflow_tenant.deepflow_agent_l7_session_aggr.cached-request-resource`
      #       用于记录当前时刻所有时间槽中缓存的 request_resource 字段占用的总内存，单位为字节。
      #     - Metric `deepflow_tenant.deepflow_agent_l7_session_aggr.over-limit`
      #       用于记录达到 LRU 容量限制并触发淘汰的次数。
      # upgrade_from: static_config.l7-log-session-slot-capacity
      # deprecated: true
      session_aggregate_slot_capacity: 1024
      # type: int
      # name:
      #   en: Session Aggregate Max Entries
      #   ch: 会话聚合最大条目数
      # unit:
      # range: [16384, 10000000]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     The maximum number of l7_flow_log entries cached for merging into a session.
      #     If the total number of l7_flow_log entries exceeds this configuration,
      #     the oldest entry will be sent without merging, setting its response status to `Unknown`.
      #   ch: |-
      #     会话聚合最大条目数。
      #     如果 l7_flow_log 条目总数超过该配置，最老的条目将被丢弃，并设置其 response 状态为 `Unknown`。
      session_aggregate_max_entries: 65536
      # type: bool
      # name:
      #   en: Consistent Timestamp in L7 Metrics
      #   ch: 应用指标时间一致性开关
      # unit:
      # range: []
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     When this configuration is enabled, for the same session, response-related metrics (such as response
      #     count, latency, exceptions) are recorded in the time slot corresponding to when the request occurred,
      #     rather than the time slot of the response itself. This means that when calculating metrics for
      #     requests and responses within a session, a consistent timestamp based on the time of the request
      #     occurrence is used.
      #   ch: |-
      #     当开关打开时对于同一个会话的请求和响应, 它们对应的指标数据会全部统计在请求所在的时间戳里
      consistent_timestamp_in_l7_metrics: false
  # type: section
  # name:
  #   en: Flow Log
  #   ch: 流日志
  # description:
  flow_log:
    # type: section
    # name:
    #   en: Time Window
    #   ch: 时间窗口
    # description:
    time_window:
      # type: duration
      # name:
      #   en: Maximum Tolerable Packet Delay
      #   ch: 最大可容忍的 Packet 延迟
      # unit:
      # range: [1s, 20s]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The timestamp carried by the packet captured by AF_PACKET may be delayed
      #     from the current clock, especially in heavy traffic scenarios, which may be
      #     as high as nearly 10s.
      #     This also affects FlowMap aggregation window size.
      #   ch: |-
      #     捕获的包携带的时间戳可能比当前时间晚，尤其是在流量高峰期可能延迟高达 10s。
      #     该配置也会影响 FlowMap 聚合窗口的大小。
      # upgrade_from: static_config.packet-delay
      max_tolerable_packet_delay: 1s
      # type: duration
      # name:
      #   en: Extra Tolerable Flow Delay
      #   ch: 额外可容忍的 Flow 延迟
      # unit:
      # range: [0s, 20s]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Extra tolerance for QuadrupleGenerator receiving flows.
      #     Affects 1s/1m QuadrupleGenerator aggregation window size.
      #   ch: |-
      #     QuadrupleGenerator 接收 flow 的额外时间延迟。
      #     该配置会影响秒级和分钟级 QuadrupleGenerator 聚合窗口的大小。
      # upgrade_from: static_config.second-flow-extra-delay-second
      extra_tolerable_flow_delay: 0s
    # type: section
    # name:
    #   en: Conntrack (a.k.a. Flow Map)
    #   ch: Conntrack（即 Flow Map）
    # description:
    conntrack:
      # type: duration
      # name:
      #   en: Flow Flush Interval
      #   ch: Flow Flush 间隔
      # unit:
      # range: [1s, 1m]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Flow generation delay time in FlowMap, used to increase the window size
      #     in downstream processing units to avoid pushing the window too fast.
      #   ch: |-
      #     FlowMap 中流产生延迟时间，用于在下游处理单元中增加窗口大小，避免窗口推动过快。
      # upgrade_from: static_config.flow.flush-interval
      flow_flush_interval: 1s
      # type: section
      # name:
      #   en: Flow Generation
      #   ch: Flow 生成逻辑
      # description:
      flow_generation:
        # type: int
        # name:
        #   en: Server Ports
        #   ch: 服务端口号
        # unit:
        # range: [1, 65535]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Service port list, priority lower than TCP SYN flags.
        #   ch: |-
        #     deepflow-agent 有可能会错误的判断长流的方向，如果某个端口一定是服务端端口，
        #     可配置在此处避免误判断。
        # upgrade_from: static_config.server-ports
        server_ports: []
        # type: bool
        # name:
        #   en: Cloud Traffic Ignore MAC
        #   ch: 云流量忽略 MAC
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     When the MAC addresses of the two-way traffic collected at the same
        #     location are asymmetrical, the traffic cannot be aggregated into a Flow.
        #     You can set this value at this time. Only valid for Cloud (not IDC) traffic.
        #   ch: |-
        #     默认情况下，对云流量采集做流聚合时，deepflow-agent 会校验 MAC 地址，如果同一位置、同一条流的
        #     上、下行数据包中的 MAC 地址不一致（非对称），将导致会话的上、下行数据无法聚合为同一条流。开启此
        #     开关后，deepflow-agent 将在流聚合过程中不校验 MAC 地址。
        # upgrade_from: static_config.flow.ignore-tor-mac
        cloud_traffic_ignore_mac: false
        # type: bool
        # name:
        #   en: Ignore L2End
        #   ch: 忽略 L2End
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     For Cloud traffic, only the MAC address corresponding to the side with
        #     L2End = true is matched when generating the flow. Set this value to `true` to
        #     force a double-sided MAC address match and only aggregate traffic with
        #     exactly equal MAC addresses.
        #   ch: |-
        #     对于虚拟网络流量，流聚合仅匹配 l2end 为 true 的一端的 MAC 地址, 设置为 `true`
        #     流聚合会使用全部MAC地址。
        # upgrade_from: static_config.flow.ignore-l2-end
        ignore_l2_end: false
        # type: bool
        # name:
        #   en: IDC Traffic Ignore VLAN
        #   ch: IDC 流量忽略 VLAN
        # unit:
        # range: []
        # enum_options: []
        # modification: agent_restart
        # ee_feature: true
        # description:
        #   en: |-
        #     When the VLAN of the two-way traffic collected at the same location
        #     are asymmetrical, the traffic cannot be aggregated into a Flow. You can
        #     set this value at this time. Only valid for IDC (not Cloud) traffic.
        #   ch: |-
        #     当在同一位置采集的双向流量的 VLAN 不对称时，流量无法聚合为同一条流。您可以
        #     此时设置此值。仅适用于 IDC（非云）流量。
        # upgrade_from: static_config.flow.ignore-idc-vlan
        idc_traffic_ignore_vlan: false
      # type: section
      # name:
      #   en: Timeouts
      #   ch: 超时设置
      # description:
      timeouts:
        # type: duration
        # name: Established
        # unit:
        # range: [1s, 1d]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Timeouts for TCP State Machine - Established.
        #   ch: |-
        #     TCP 状态机的建连状态超时时长。
        # upgrade_from: static_config.flow.established-timeout
        established: 300s
        # type: duration
        # name: Closing RST
        # unit:
        # range: [1s, 1d]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Timeouts for TCP State Machine - Closing Reset.
        #   ch: |-
        #     Closing Reset 类型的 TCP 状态机超时。
        # upgrade_from: static_config.flow.closing-rst-timeout
        closing_rst: 35s
        # type: duration
        # name: Opening RST
        # unit:
        # range: [1s, 1d]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Timeouts for TCP State Machine - Opening Reset.
        #   ch: |-
        #     Opening Reset 类型的 TCP 状态机超时。
        # upgrade_from: static_config.flow.opening-rst-timeout
        opening_rst: 1s
        # type: duration
        # name: Others
        # unit:
        # range: [1s, 1d]
        # enum_options: []
        # modification: agent_restart
        # ee_feature: false
        # description:
        #   en: |-
        #     Timeouts for TCP State Machine - Others.
        #   ch: |-
        #     其他类型的 TCP 状态机超时。
        # upgrade_from: static_config.flow.others-timeout
        others: 5s
    # type: section
    # name:
    #   en: Tunning
    #   ch: 调优
    # description:
    tunning:
      # type: int
      # name:
      #   en: FlowMap Hash Slots
      #   ch: FlowMap 哈希桶
      # unit:
      # range: [1024, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Since FlowAggregator is the first step in all processing, this value
      #     is also widely used in other hash tables such as QuadrupleGenerator,
      #     Collector, etc.
      #   ch: |-
      #     由于 FlowAggregator 是所有处理流程的第一步，该值也被广泛用于其他哈希表，如
      #     QuadrupleGenerator、Collector 等。
      # upgrade_from: static_config.flow.flow-slots-size
      flow_map_hash_slots: 131072
      # type: int
      # name:
      #   en: Concurrent Flow Limit
      #   ch: 并发 Flow 数量限制
      # unit:
      # range: [1024, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Maximum number of flows that can be stored in FlowMap, It will also affect the capacity of
      #     the RRT cache, Example: `rrt-cache-capacity` = `flow-count-limit`. When `rrt-cache-capacity`
      #     is not enough, it will be unable to calculate the rrt of l7. When `inputs.cbpf.common.capture_mode`
      #     is `Physical Mirror` and concurrent_flow_limit is less than or equal to 65535, it will be forced to u32::MAX.
      #   ch: |-
      #     FlowMap 中存储的最大并发 Flow 数量。该配置同时影响 RRT 缓存容量。
      #     例如：`rrt-cache-capacity` = `flow-count-limit`。当 `rrt-cache-capacity` 不足时，
      #     将无法计算 L7 的 RRT。当 `inputs.cbpf.common.capture_mode` 为 `物理网络镜像` 并且该配置值小于等于 65535 时，
      #     将会被强制设置为 u32::MAX。
      # upgrade_from: static_config.flow.flow-count-limit
      concurrent_flow_limit: 65535
      # type: int
      # name:
      #   en: Memory Pool Size
      #   ch: 内存池大小
      # unit:
      # range: [1024, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     This value is used to set max length of memory pool in FlowMap
      #     Memory pools are used for frequently create and destroy objects like
      #     FlowNode, FlowLog, etc.
      #   ch: |-
      #     FlowMap 内存池的大小。
      # upgrade_from: static_config.flow.memory-pool-size
      memory_pool_size: 65536
      # type: int
      # name:
      #   en: Maximum Size of Batched Buffer
      #   ch: Batched Buffer 最大大小
      # unit:
      # range: [1024, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     Only TaggedFlow allocation is affected at the moment.
      #     Structs will be allocated in batch to minimalize malloc calls.
      #     Total memory size of a batch will not exceed this limit.
      #     A number larger than 128K is not recommended because the default
      #     MMAP_THRESHOLD is 128K, allocating chunks larger than 128K will
      #     result in calling mmap and more page faults.
      #   ch: |-
      #     目前只影响 TaggedFlow 批量分配。
      #     为避免大量的 malloc 调用，生命周期短且数量多的结构体用批量分配进行优化。
      #     一次分配的总内存大小不会超过这个限制。
      #     由于默认的 MMAP_THRESHOLD 是 128K，分配的内存块超过 128K 会导致
      #     mmap 调用和页错误增加，反而降低性能，所以不推荐将该配置设置大于 128K。
      # upgrade_from: static_config.batched-buffer-size-limit
      max_batched_buffer_size: 131072
      # type: int
      # name:
      #   en: FlowAggregator Queue Size
      #   ch: FlowAggregator 队列大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The length of the following queues:
      #     - 2-second-flow-to-minute-aggrer
      #   ch: |-
      #     以下队列的大小：
      #     - 2-second-flow-to-minute-aggrer
      # upgrade_from: static_config.flow.flow-aggr-queue-size
      flow_aggregator_queue_size: 65535
      # type: int
      # name:
      #   en: FlowGenerator Queue Size
      #   ch: FlowGenerator 队列大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The length of the following queues:
      #     - 1-tagged-flow-to-quadruple-generator
      #     - 1-tagged-flow-to-app-protocol-logs
      #     - 0-{flow_type}-{port}-packet-to-tagged-flow (flow_type: sflow, netflow)
      #   ch: |-
      #     以下队列的大小：
      #     - 1-tagged-flow-to-quadruple-generator
      #     - 1-tagged-flow-to-app-protocol-logs
      #     - 0-{flow_type}-{port}-packet-to-tagged-flow (flow_type: sflow, netflow)
      # upgrade_from: static_config.flow-queue-size
      flow_generator_queue_size: 65536
      # type: int
      # name:
      #   en: QuadrupleGenerator Queue Size
      #   ch: QuadrupleGenerator 队列大小
      # unit:
      # range: [262144, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The length of the following queues:
      #     - 2-flow-with-meter-to-second-collector
      #     - 2-flow-with-meter-to-minute-collector
      #   ch: |-
      #     以下队列的大小：
      #     - 2-flow-with-meter-to-second-collector
      #     - 2-flow-with-meter-to-minute-collector
      # upgrade_from: static_config.quadruple-queue-size
      quadruple_generator_queue_size: 262144

# type: section
# name:
#   en: Outputs
#   ch: 输出
# description:
outputs:
  # type: section
  # name: Socket
  # description:
  socket:
    # type: string
    # name:
    #   en: Data Socket Type
    #   ch: Data Socket 类型
    # unit:
    # range: []
    # enum_options: [TCP, UDP, FILE]
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     It can only be set to FILE in standalone mode, in which case
    #     l4_flow_log and l7_flow_log will be written to local files.
    #   ch: |-
    #     配置 deepflow-agent 向 deepflow-server 回传数据所用的 Socket 类型。在独立部署
    #     模式下，需配置为 FILE 类型，agent 将 l4_flow_log 和 l7_flow_log 写入本地文件。
    # upgrade_from: collector_socket_type
    data_socket_type: TCP
    # type: string
    # name:
    #   en: NPB Socket Type
    #   ch: NPB Socket 类型
    # unit:
    # range: []
    # enum_options: [UDP, RAW_UDP, TCP, ZMQ]
    # modification: hot_update
    # ee_feature: true
    # description:
    #   en: |-
    #     RAW_UDP uses RawSocket to send UDP packets, which has the highest
    #     performance, but there may be compatibility issues in some environments.
    #   ch: |-
    #     设置 NPB 分发时使用的 Socket 类型。RAW_UDP 使用 RawSocket 发送 UDP 数据，有更高的
    #     分发性能，但是可能存在一些环境不兼容的情况。
    # upgrade_from: npb_socket_type
    npb_socket_type: RAW_UDP
    # type: bool
    # name: RAW_UDP QoS Bypass
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     When sender uses RAW_UDP to send data, this feature can be enabled to
    #     improve performance. Linux Kernel >= 3.14 is required. Note that the data
    #     sent when this feature is enabled cannot be captured by tcpdump.
    #   ch: |-
    #     当使用 RAW_UDP 发送数据时，可以开启该特性以提升数据发送的性能。注意：（1）该特性需要
    #     Linux Kernel >= 3.14；（2）特性开启后，发送的数据包无法被 tcpdump 捕获。
    # upgrade_from: static_config.enable-qos-bypass
    raw_udp_qos_bypass: false
    # type: bool
    # name:
    #   en: Multiple Sockets To Ingester
    #   ch: 使用多个 Ingester Socket
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     When set to true, deepflow-agent will send data with multiple sockets to Ingester,
    #     which has higher performance, but will bring more impact to the firewall.
    #   ch: |-
    #     当设置为 true 时，deepflow-agent 将使用多个套接字将数据发送到 Ingester，
    #     其发送性能更高，但会给防火墙带来更大的影响。
    # upgrade_from: static_config.multiple-sockets-to-ingester
    multiple_sockets_to_ingester: false
  # type: section
  # name:
  #   en: Flow Log and Request Log
  #   ch: 流日志及调用日志
  # description:
  flow_log:
    # type: section
    # name:
    #   en: Filters
    #   ch: 过滤器
    # description:
    filters:
      # type: int
      # name:
      #   en: Capture Network Types for L4
      #   ch: 流日志采集网络类型
      # unit:
      # range: []
      # enum_options:
      #   - -1:
      #       en: Disabled
      #       ch: 关闭
      #   - 0:
      #       en: All TAPs
      #       ch: 所有网络类型
      #   - _DYNAMIC_OPTIONS_: _DYNAMIC_OPTIONS_
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     The list of TAPs to collect l4_flow_log, you can also set a list of TAPs to
      #     be collected.
      #   ch: |-
      #     将被存储的流日志采集网络类型列表。
      # upgrade_from: l4_log_tap_types
      l4_capture_network_types: [0]
      # type: int
      # name:
      #   en: Capture Network Types for L7
      #   ch: 调用日志采集网络类型
      # unit:
      # range: []
      # enum_options:
      #   - -1:
      #       en: Disabled
      #       ch: 关闭
      #   - 0:
      #       en: All TAPs
      #       ch: 所有网络类型
      #   - _DYNAMIC_OPTIONS_: _DYNAMIC_OPTIONS_
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     The list of TAPs to collect l7_flow_log, you can also set a list of TAPs to
      #     be collected.
      #   ch: |-
      #     将被存储的调用日志采集网络类型列表。
      # upgrade_from: l7_log_store_tap_types
      l7_capture_network_types: [0]
      # type: int
      # name:
      #   en: Ignored Observation Points for L4
      #   ch: 流日志忽略的观测点
      # unit:
      # range: []
      # enum_options:
      #   - 0:
      #       en: rest, Other NIC
      #       ch: rest，其他网卡
      #   - 1:
      #       en: c, Client NIC
      #       ch: c，客户端网卡
      #   - 2:
      #       en: s, Server NIC
      #       ch: s，服务端网卡
      #   - 4:
      #       en: local, Local NIC
      #       ch: local，本机网卡
      #   - 9:
      #       en: c-nd, Client K8s Node
      #       ch: c-nd，客户端容器节点
      #   - 10:
      #       en: s-nd, Server K8s Node
      #       ch: s-nd，服务端容器节点
      #   - 17:
      #       en: c-hv, Client VM Hypervisor
      #       ch: c-hv，客户端宿主机
      #   - 18:
      #       en: s-hv, Server VM Hypervisor
      #       ch: s-hv，服务端宿主机
      #   - 25:
      #       en: c-gw-hv, Client-side Gateway Hypervisor
      #       ch: c-gw-hv, 客户端到网关宿主机
      #   - 26:
      #       en: s-gw-hv, Server-side Gateway Hypervisor
      #       ch: s-gw-hv, 网关宿主机到服务端
      #   - 33:
      #       en: c-gw, Client-side Gateway
      #       ch: c-gw，客户端到网关
      #   - 34:
      #       en: s-gw, Server-side Gateway
      #       ch: s-gw, 网关到服务端
      #   - 41:
      #       en: c-p, Client Process
      #       ch: c-p，客户端进程
      #   - 42:
      #       en: s-p, Server Process
      #       ch: s-p, 服务端进程
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Use the value of tap_side to control which l4_flow_log should be ignored for
      #     collection. This configuration also applies to tcp_sequence and pcap data in
      #     the Enterprise Edition. Default value `[]` means store everything.
      #   ch: |-
      #     配置该参数后，deepflow-agent将不采集列表中观测点的流日志（同时 TCP 时序数据、Pcap 数据
      #     的采集也将被忽略）。默认值`[]`表示所有观测点均采集。
      # upgrade_from: l4_log_ignore_tap_sides
      l4_ignored_observation_points: []
      # type: int
      # name:
      #   en: Ignored Observation Points for L7
      #   ch: 调用日志忽略的观测点
      # unit:
      # range: []
      # enum_options:
      #   - 0:
      #       en: rest, Other NIC
      #       ch: rest，其他网卡
      #   - 1:
      #       en: c, Client NIC
      #       ch: c，客户端网卡
      #   - 2:
      #       en: s, Server NIC
      #       ch: s，服务端网卡
      #   - 4:
      #       en: local, Local NIC
      #       ch: local，本机网卡
      #   - 9:
      #       en: c-nd, Client K8s Node
      #       ch: c-nd，客户端容器节点
      #   - 10:
      #       en: s-nd, Server K8s Node
      #       ch: s-nd，服务端容器节点
      #   - 17:
      #       en: c-hv, Client VM Hypervisor
      #       ch: c-hv，客户端宿主机
      #   - 18:
      #       en: s-hv, Server VM Hypervisor
      #       ch: s-hv，服务端宿主机
      #   - 25:
      #       en: c-gw-hv, Client-side Gateway Hypervisor
      #       ch: c-gw-hv, 客户端到网关宿主机
      #   - 26:
      #       en: s-gw-hv, Server-side Gateway Hypervisor
      #       ch: s-gw-hv, 网关宿主机到服务端
      #   - 33:
      #       en: c-gw, Client-side Gateway
      #       ch: c-gw，客户端到网关
      #   - 34:
      #       en: s-gw, Server-side Gateway
      #       ch: s-gw, 网关到服务端
      #   - 41:
      #       en: c-p, Client Process
      #       ch: c-p，客户端进程
      #   - 42:
      #       en: s-p, Server Process
      #       ch: s-p, 服务端进程
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Use the value of observation points to control which l7_flow_log should be ignored for
      #     collection. The default value `[]` means that all observation points are collected.
      #   ch: |-
      #     配置该参数后，deepflow-agent将不采集列表中观测点的应用调用日志。默认值`[]`表示所有观测点均采集。
      # upgrade_from: l7_log_ignore_tap_sides
      l7_ignored_observation_points: []
    # type: section
    # name:
    #   en: Aggregators
    #   ch: 聚合器
    # description:
    aggregators:
      # type: bool
      # name:
      #   en: Health Check Flow Log Aggregation
      #   ch: 聚合健康检查流日志
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Agent will mark the following types of flows as `close_type = normal end-client reset`:
      #     - Client sends SYN, server replies SYN-ACK, client sends RST
      #     - Client sends SYN, server replies SYN-ACK, client sends ACK, client sends RST
      #
      #     This type of traffic is normal load balancer backend host inspection traffic and does not carry any meaningful application layer payload.
      #
      #     When this configuration item is set to `true`, Agent will reset the client port number of the flow log to 0 before aggregating the output,
      #     thereby reducing bandwidth and storage overhead.
      #   ch: |-
      #     Agent 会将如下类型的流标记为 `close_type = 正常结束-客户端重置`：
      #     - 客户端发送 SYN，服务端回复 SYN-ACK，客户端发送 RST
      #     - 客户端发送 SYN，服务端回复 SYN-ACK，客户端发送 ACK，客户端发送 RST
      #
      #     此类流量是正常的负载均衡器后端主机检查检查流量，不会携带任何有意义的应用层载荷。
      #
      #     本配置项设置为 `true` 时，Agent 会将流日志的客户端端口号重置为 0 之后再聚合输出，
      #     从而降低带宽和存储开销。
      aggregate_health_check_l4_flow_log: true
    # type: section
    # name:
    #   en: Throttles
    #   ch: 限速器
    # description:
    throttles:
      # type: int
      # name:
      #   en: L4 Throttle
      #   ch: 流日志限速器
      # unit: Per Second
      # range: [100, 1000000]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     The maximum number of rows of l4_flow_log sent per second, when the actual
      #     number of upstream rows exceeds this value, reservoir sampling is applied to
      #     limit the actual number of rows sent.
      #   ch: |-
      #     deepflow-agent 每秒发送的 l4_flow_log 数量上限，实际产生的日志数量超过阈值时，将
      #     使用水库采样限制实际发送数量不超过阈值。
      # upgrade_from: l4_log_collect_nps_threshold
      l4_throttle: 10000
      # type: int
      # name:
      #   en: L7 Throttle
      #   ch: 调用日志限速器
      # unit: Per Second
      # range: [100, 1000000]
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     The maximum number of rows of l7_flow_log sent per second, when the actual
      #     number of rows exceeds this value, sampling is triggered.
      #   ch: |-
      #     deepflow-agent 每秒发送的 l7_flow_log 数量上限，实际发送数量超出参数值后，将开启采样。
      # upgrade_from: l7_log_collect_nps_threshold
      l7_throttle: 10000
    # type: section
    # name:
    #   en: Tunning
    #   ch: 调优
    # description:
    tunning:
      # type: int
      # name:
      #   en: Collector Queue Size
      #   ch: Collector 队列大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The length of the following queues:
      #     - 3-flow-to-collector-sender
      #     - 3-protolog-to-collector-sender
      #   ch: |-
      #     设置如下队列的长度:
      #     - 3-flow-to-collector-sender
      #     - 3-protolog-to-collector-sender
      # upgrade_from: static_config.flow-sender-queue-size
      collector_queue_size: 65536
  # type: section
  # name:
  #   en: Flow Metrics
  #   ch: Flow 性能指标
  # description:
  flow_metrics:
    # type: bool
    # name: Enabled
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: false
    # description:
    #   en: |-
    #     When disabled, deepflow-agent will not send metrics and logging data
    #     collected using eBPF and cBPF.
    #
    #     Attention: set to false will also disable l4_flow_log and l7_flow_log.
    #   ch: |-
    #     指标数据采集总开关。关闭后 deepflow-agent 将停止所有应用调用指标、网络指标、应用
    #     调用日志、流日志、TCP 包时序数据、Pcap 数据的采集。
    # upgrade_from: collector_enabled
    enabled: true
    # type: section
    # name:
    #   en: Filters
    #   ch: 过滤器
    # description:
    filters:
      # type: bool
      # name:
      #   en: Inactive Server Port Aggregation
      #   ch: 不活跃服务端端口号聚合
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When enabled, deepflow-agent will not generate detailed metrics for each
      #     inactive port (ports that only receive data, not send data), and the data of
      #     all inactive ports will be aggregated into the metrics with a tag
      #     'server_port = 0'.
      #   ch: |-
      #     开启功能后 deepflow-agent 将对非活跃的端口（仅接收数据，不发送数据）的指标数据采集
      #     做聚合处理，所有非活跃端口的数据聚合生成一条'server_port = 0'的指标，而不再生成每个
      #     server_port 单独的指标。
      # upgrade_from: inactive_server_port_enabled
      inactive_server_port_aggregation: false
      # type: bool
      # name:
      #   en: Inactive IP Aggregation
      #   ch: 不活跃 IP 地址聚合
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When enabled, deepflow-agent will not generate detailed metrics for each
      #     inactive IP address (IP addresses that only receive data, not send data), and
      #     the data of all inactive IP addresses will be aggregated into the metrics with
      #     a tag 'ip = 0'.
      #   ch: |-
      #     开启功能后 deepflow-agent 将对非活跃 IP（仅接收数据，不发送数据）的指标数据采集做聚合
      #     处理，所有非活跃 IP 的数据聚合生成一条'ip = 0'的指标，而不再生成每个 IP 单独的指标。
      # upgrade_from: inactive_ip_enabled
      inactive_ip_aggregation: false
      # type: bool
      # name:
      #   en: NPM Metrics
      #   ch: NPM 指标
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When closed, deepflow-agent only collects some basic throughput metrics.
      #   ch: |-
      #     网络指标的采集开关。关闭后 deepflow-agent 停止采集除基本的吞吐类指标外的其他网络指标。
      # upgrade_from: l4_performance_enabled
      npm_metrics: true
      # type: bool
      # name:
      #   en: NPM Concurrent Metrics
      #   ch: NPM 活跃连接指标
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When closed, deepflow-agent does not calculate metrics concurrent.
      #   ch: |-
      #     当关闭时，deepflow-agent 不计算活跃连接指标。
      npm_metrics_concurrent: true
      # type: bool
      # name:
      #   en: APM Metrics
      #   ch: APM 指标
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     When closed, deepflow-agent will not collect RED (request/error/delay) metrics.
      #   ch: |-
      #     应用调用指标的采集开关。关闭后 deepflow-agent 停止采集全部应用调用指标。
      # upgrade_from: l7_metrics_enabled
      apm_metrics: true
      # type: bool
      # name:
      #   en: Second Metrics
      #   ch: 秒粒度指标
      # unit:
      # range: []
      # enum_options: []
      # modification: hot_update
      # ee_feature: false
      # description:
      #   en: |-
      #     Second granularity metrics.
      #   ch: |-
      #     秒级指标的采集开关。关闭后 deepflow-agent 将停止采集秒粒度的网络指标和应用调用指标。
      # upgrade_from: vtap_flow_1s_enabled
      second_metrics: true
    # type: section
    # name:
    #   en: Tunning
    #   ch: 调优
    # description:
    tunning:
      # type: int
      # name:
      #   en: Sender Queue Size
      #   ch: Sender 队列大小
      # unit:
      # range: [65536, 64000000]
      # enum_options: []
      # modification: agent_restart
      # ee_feature: false
      # description:
      #   en: |-
      #     The length of the following queues:
      #     - 3-doc-to-collector-sender
      #   ch: |-
      #     配置如下队列的大小:
      #     - 3-doc-to-collector-sender
      # upgrade_from: static_config.collector-sender-queue-size
      sender_queue_size: 65536
  # type: section
  # name: NPB (Network Packet Broker)
  # description:
  npb:
    # type: int
    # name:
    #   en: Maximum MTU
    #   ch: 最大 MTU
    # unit: byte
    # range: [500, 10000]
    # enum_options: []
    # modification: hot_update
    # ee_feature: true
    # description:
    #   en: |-
    #     Maximum MTU allowed when using UDP for NPB.
    #
    #     Attention: Public cloud service providers may modify the content of the
    #     tail of the UDP packet whose packet length is close to 1500 bytes. When
    #     using UDP transmission, it is recommended to set a slightly smaller value.
    #   ch: |-
    #     NPB 分发时的 UDP 传输的 MTU 值。注意：当 UDP 报文长度接近 1500 字节后，云平台可能会
    #     修改数据包的尾部数据，因此建议`max_mtu`的值小于 1500。
    # upgrade_from: mtu
    max_mtu: 1500
    # type: int
    # name:
    #   en: RAW_UDP VLAN Tag
    #   ch: RAW_UDP 的 VLAN 标签
    # unit:
    # range: [0, 4095]
    # enum_options: []
    # modification: hot_update
    # ee_feature: true
    # description:
    #   en: |-
    #     When using RAW_UDP Socket to transmit UDP data, this value can be used to
    #     set the VLAN tag. Default value `0` means no VLAN tag.
    #   ch: |-
    #     当使用 RAW_UDP Socket 发送 NPB 数据时，通过该参数设置数据包 VLAN 标签。默认值为`0`，表示
    #     不使用 VLAN 标签。
    # upgrade_from: output_vlan
    raw_udp_vlan_tag: 0
    # type: int
    # name:
    #   en: Extra VLAN Header
    #   ch: 额外的 VLAN 头
    # unit:
    # range: []
    # enum_options:
    #   - 0:
    #       en: None
    #       ch: 无
    #   - 1: 802.1Q
    #   - 2: QinQ
    # modification: hot_update
    # ee_feature: true
    # description:
    #   en: |-
    #     Whether to add an extra 802.1Q header to NPB traffic, when this value is
    #     set, deepflow-agent will insert a VLAN Tag into the NPB traffic header, and
    #     the value is the lower 12 bits of TunnelID in the VXLAN header.
    #   ch: |-
    #     设置 NPB 分发数据的 VLAN 模式。`无`表示不加 VLAN；`802.1Q`表示添加 802.1Q header；
    #     `QinQ`表示添加 QinQ。
    # upgrade_from: npb_vlan_mode
    extra_vlan_header: 0
    # type: bool
    # name:
    #   en: Traffic Global Dedup
    #   ch: 流量全局去重
    # unit:
    # range: []
    # enum_options: []
    # modification: hot_update
    # ee_feature: true
    # description:
    #   en: |-
    #     Whether to enable global (distributed) traffic deduplication for the
    #     NPB feature.
    #   ch: |-
    #     NPB 数据去重开关。开启开关后，将对 NPB 分发做全局去重，避免一份流量在客户端、服务端分发两次。
    # upgrade_from: npb_dedup_enabled
    traffic_global_dedup: true
    # type: int
    # name:
    #   en: Target Port
    #   ch: 目的端口号
    # unit:
    # range: [1, 65535]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: true
    # description:
    #   en: |-
    #     Server port for NPB.
    #   ch: |-
    #     NPB 分发使用的目标端口号。
    # upgrade_from: static_config.npb-port
    target_port: 4789
    # type: int
    # name:
    #   en: Custom VXLAN Flags
    #   ch: 自定义 VXLAN Flags
    # unit:
    # range: [0, 255]
    # enum_options: []
    # modification: agent_restart
    # ee_feature: true
    # description:
    #   en: |-
    #     NPB uses the first byte of the VXLAN Flag to identify the sending traffic to
    #     prevent the traffic sent by NPB from being collected by deepflow-agent.
    #
    #     Attention: To ensure that the VNI bit is set, the value configured here will
    #     be used after |= 0b1000_0000. Therefore, this value cannot be directly
    #     configured as 0b1000_0000.
    #   ch: |-
    #     使用 VXLAN 分发时设置 VXLAN 内的 Flags 为该值。采集器不会采集分发流量。
    #
    #     这个配置默认会或上0b1000_0000，所以不能配置为 0b1000_0000。
    # upgrade_from: static_config.vxlan-flags
    custom_vxlan_flags: 0b1111_1111
    # type: bool
    # name:
    #   en: Overlay VLAN Header Trimming
    #   ch: Overlay VLAN 头剥离
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: true
    # description:
    #   en: |-
    #     This configuration only ignores the VLAN header in the captured original message
    #     and does not affect the configuration item: npb_vlan_mode
    #   ch: |-
    #     开启开关后，deepflow-agent 在 NPB 分发时会剥离 overlay 原始数据包中的 VLAN 头。
    # upgrade_from: static_config.ignore-overlay-vlan
    overlay_vlan_header_trimming: false
    # type: int
    # name:
    #   en: Maximum Tx Throughput
    #   ch: 最大 Tx 吞吐量
    # unit: Mbps
    # range: [1, 100000]
    # enum_options: []
    # modification: hot_update
    # ee_feature: true
    # description:
    #   en: |-
    #     Maximum traffic rate allowed for npb sender.
    #   ch: |-
    #     设置 deepflow-agent 做 NPB 分发的最大吞吐率。
    # upgrade_from: max_npb_bps
    max_tx_throughput: 1000
  # type: section
  # name:
  #   en: Compression
  #   ch: 压缩
  # description:
  compression:
    # type: bool
    # name:
    #   en: Application_Log
    #   ch: Application_Log
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Whether to compress the integrated application log data received by deepflow-agent. The compression
    #     ratio is about 5:1~20:1. Turning on this feature will result in higher CPU consumption
    #     of deepflow-agent.
    #   ch: |-
    #     开启后，deepflow-agent 将对集成的应用日志数据进行压缩处理，压缩比例在 5:1~20:1 之间。注意：
    #     开启此特性将增加 deepflow-agent 的 CPU 消耗。
    application_log: true
    # type: bool
    # name:
    #   en: Pcap
    #   ch: Pcap
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Whether to compress the captured pcap data received by deepflow-agent. The compression
    #     ratio is about 5:1~10:1. Turning on this feature will result in higher CPU consumption
    #     of deepflow-agent.
    #   ch: |-
    #     开启后，deepflow-agent 将对抓取的 Pcap 数据进行压缩处理，压缩比例在 5:1~10:1 之间。注意：
    #     开启此特性将增加 deepflow-agent 的 CPU 消耗。
    pcap: true
    # type: bool
    # name:
    #   en: Request Log
    #   ch: 调用日志
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Whether to compress the l7 flow log. The compression ratio is about 8:1.
    #     Turning on this feature will result in higher CPU consumption of deepflow-agent.
    #   ch: |-
    #     开启后，deepflow-agent 将对调用日志进行压缩处理，压缩比例在 8:1 左右。注意：
    #     开启此特性将增加 deepflow-agent 的 CPU 消耗。
    l7_flow_log: true
    # type: bool
    # name:
    #   en: Flow Log
    #   ch: 流日志
    # unit:
    # range: []
    # enum_options: []
    # modification: agent_restart
    # ee_feature: false
    # description:
    #   en: |-
    #     Whether to compress the l4 flow log.
    #   ch: |-
    #     开启后，deepflow-agent 将对网络流日志进行压缩处理。注意：
    #     开启此特性将增加 deepflow-agent 的 CPU 消耗。
    l4_flow_log: false

# type: section
# name:
#   en: Plugins
#   ch: 插件
# description:
plugins:
  # type: string
  # name:
  #   en: Wasm Plugins
  #   ch: Wasm 插件列表
  # unit:
  # range: []
  # enum_options: [_DYNAMIC_OPTIONS_: _DYNAMIC_OPTIONS_]
  # modification: hot_update
  # ee_feature: false
  # description:
  #   en: |-
  #     Wasm plugin need to load in agent
  #   ch: |-
  #     需要加载的 Wasm 插件列表。
  # upgrade_from: wasm_plugins
  wasm_plugins: []
  # type: string
  # name:
  #   en: SO Plugins
  #   ch: SO 插件列表
  # unit:
  # range: []
  # enum_options: [_DYNAMIC_OPTIONS_: _DYNAMIC_OPTIONS_]
  # modification: hot_update
  # ee_feature: false
  # description:
  #   en: |-
  #     so plugin need to load in agent. so plugin use dlopen flag RTLD_LOCAL
  #     and RTLD_LAZY to open the so file, it mean that the so must solve the
  #     link problem by itself
  #   ch: |-
  #     需要加载的 so 插件列表。
  # upgrade_from: so_plugins
  so_plugins: []

# type: section
# name:
#   en: Dev
#   ch: 开发
# description:
dev:
  # type: string
  # name: Feature Flags
  # unit:
  # range: []
  # enum_options: []
  # modification: agent_restart
  # ee_feature: false
  # description:
  #   en: |-
  #     Unreleased deepflow-agent features can be turned on by setting this switch.
  #   ch: |-
  #     未发布的采集器特性可以通过该选项开启。
  # upgrade_from: static_config.feature-flags
  feature_flags: []
