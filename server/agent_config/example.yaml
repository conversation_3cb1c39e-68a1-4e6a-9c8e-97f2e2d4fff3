## Agent Group ID
vtap_group_id: g-xxxxxx

####################
## Resource Limit ##
####################
# CPU Limit (in CPU Cores)
# Unit: number of logical cores. Default: 1. Range: [1, 1000]
# Note: deepflow-agent uses cgroups to limit CPU usage. 1 cpu = 1 core.
#   The actual CPU limit is based on the lesser of max_cpus and max_millicpus.
#   For example, if max_cpus = 2 and max_millicpus = 1500, the actual CPU limit
#   would be 1.5 cores.
max_cpus: 1

# CPU Limit (in MilliCPUs)
# Unit: number of millicpus. Default: 1000. Range: [1, 1000000]
# Note: deepflow-agent uses cgroups to limit CPU usage. 1 millicpu = 1 millicore = 0.001 core.
#   The actual CPU limit is based on the lesser of max_cpus and max_millicpus.
#   For example, if max_cpus = 2 and max_millicpus = 1500, the actual CPU limit
#   would be 1.5 cores.
max_millicpus: 1000

# Memory Limit
# Unit: M bytes. Default: 768. Range: [128, 100000]
# Note: deepflow-agent uses cgroups to limit memory usage.
max_memory: 768

# System Free Memory Limit
# Unit: %. Default: 0. Range: [0, 100]
# Note: The limit of the percentage of system free memory.
#   Setting sys_memory_limit to 0 indicates that the system free/available memory ratio is not checked.
#   1. When the current system free/available memory ratio is below sys_memory_limit * 70%,
#      the agent will automatically restart.
#   2. When the current system free/available memory ratio is below sys_memory_limit but above 70%,
#      the agent enters the disabled state.
#   3. When the current system free/available memory ratio remains above sys_memory_limit * 110%,
#      the agent recovers from the disabled state.
sys_free_memory_limit: 0

# System Free Memory Metric
# Default: free
# Supported values: free, available
sys_free_memory_metric: free

# Packet Capture Rate Limit
# Unit: Kpps. Default: 200. Range: [1, 1000000]
max_collect_pps: 200

# NPB (Packet Broker) Traffic Limit
# Unit: Mbps. Default: 1000. Range: [1, 100000]
max_npb_bps: 1000

# System Load Circuit Breaker Threshold
# Default: 1.0
# Range: [0, 10.0]
# Note: When the load of the Linux system divided by the number of
#   CPU cores exceeds this value, the agent automatically enters
#   the disabled state. It will automatically recover if it remains
#   below 90% of this value for a continuous 5 minutes. Setting it
#   to 0 disables this feature.
system_load_circuit_breaker_threshold: 1.0

# System Load Circuit Breaker Recover
# Default: 0.9
# Range: [0, 10.0]
# Note: When the system load of the Linux system divided by the
#   number of CPU cores is continuously below this value for 5
#   minutes, the agent can recover from the circuit breaker
#   disabled state, and setting it to 0 means turning off the
#   circuit breaker feature.
system_load_circuit_breaker_recover: 0.9

# System Load Circuit Breaker Metric
# Default: load15
# Supported values: load1, load5, load15
# Note: The system load circuit breaker mechanism uses this metric,
#   and the agent will check this metric every 10 seconds by default.
system_load_circuit_breaker_metric: load15

# NPB (Packet Broker) Circuit Breaker Threshold
# Unit: Mbps. Default: 0. Range: [0, 100000]
# Note: When the outbound direction of the NPB interface
#   reaches or exceeds the threshold, the distribution will be
#   stopped, and then the distribution will be resumed if the
#   value is lower than (max_tx_bandwidth - max_npb_bps)*90%
#   within 5 consecutive monitoring intervals.
# Attention: When configuring this value, it must be greater
#   than max_npb_bps. 0 means disable this feature.
max_tx_bandwidth: 0

# NPB Circuit Breaker Monitoring Interval
# Unit: second. Default: 10. Range: [1, 60]
# Note: monitoring interval for outbound traffic rate of NPB interface
bandwidth_probe_interval: 10

# Remote Log Rate
# Unit: lines/hour. Default: 300. Range: [0, 10000]
# Note: deepflow-agent will send logs to deepflow-server, 0 means no limit.
log_threshold: 300

# Log Level
# Default: INFO. options: DEBUG, INFO, WARNING, ERROR
log_level: INFO

# Log File Size
# Unit: M bytes. Default: 1000. Range: [10, 10000]
log_file_size: 1000

# Thread Limit
# Default: 500. Range: [1, 1000]
# Note: Maximum number of threads that deepflow-agent is allowed to launch.
thread_threshold: 500

# Process Limit
# Default: 10. Range: [1, 100]
# Note: Maximum number of processes that deepflow-agent is allowed to launch.
process_threshold: 10

#########################
## Basic Configuration ##
#########################
# Regular Expression for TAP (Traffic Access Point)
# Length: [0, 65535]
# Default:
#   Localhost:   lo
#   Common NIC:  eth.*|en[osipx].*
#   QEMU VM NIC: tap.*
#   Flannel:     veth.*
#   Calico:      cali.*
#   Cilium:      lxc.*
#   Kube-OVN:    [0-9a-f]+_h$
# Note: Regular expression of NIC name for collecting traffic
#   When the `tap_interface_regex` is not configured, it indicates
#   that network card traffic is not being collected.
tap_interface_regex: ^(tap.*|cali.*|veth.*|eth.*|en[osipx].*|lxc.*|lo|[0-9a-f]+_h)$

# Extra Capture Network Namespace
# Default: "", means no extra network namespace (default namespace only)
# Description: Traffic will be captured in regex matched namespaces besides the default
#    namespace. NICs captured in extra namespaces are also filtered with `tap_interface_regex`.
extra_netns_regex:

# Traffic Capture Filter
# Length: [1, 512]
# Note: If not configured, all traffic will be collected. Please
#   refer to BPF syntax: https://biot.com/capstats/bpf.html
capture_bpf:

# Maximum Packet Capture Length
# Unit: bytes. Default: 65535. Range: [128, 65535]
# Note: DPDK environment does not support this configuration.
capture_packet_size: 65535

# Traffic Capture API
# Default: 0, means adaptive. Options: 0, 2 (AF_PACKET V2), 3 (AF_PACKET V3)
# Description: Traffic capture API in Linux environment
capture_socket_type: 0

# Traffic Tap Mode
# Default: 0, means local.
# Options: 0, 1 (virtual mirror), 2 (physical mirror, aka. analyzer mode)
# Note: Mirror mode is used when deepflow-agent cannot directly capture the
#   traffic from the source. For example:
#   - in the K8s macvlan environment, capture the Pod traffic through the Node NIC
#   - in the Hyper-V environment, capture the VM traffic through the Hypervisor NIC
#   - in the ESXi environment, capture traffic through VDS/VSS local SPAN
#   - in the DPDK environment, capture traffic through DPDK ring buffer
#   Use Analyzer mode when deepflow-agent captures traffic through physical switch
#   mirroring.
tap_mode: 0

# Decapsulation Tunnel Protocols
# Default: [1, 2], means VXLAN and IPIP. Options: 1 (VXLAN), 2 (IPIP), 3 (GRE), 4 (Geneve) 5 (VXLAN-NSH)
# Note: Only the Enterprise Edition supports decap GRE and VXLAN-NSH.
decap_type:
- 1
- 2

# VM MAC Address Extraction
# Default: 0
# Options:
#   0: extracted from tap interface MAC address
#   1: extracted from tap interface name
#   2: extracted from the XML file of the virtual machine
# Note: How to extract the real MAC address of the virtual machine when the
#   agent runs on the KVM host
if_mac_source: 0

# VM XML File Directory
# Default: /etc/libvirt/qemu/
# Length: [0, 100]
vm_xml_path: /etc/libvirt/qemu/

# Active Sync Interval
# Unit: second. Default: 60. Range: [10, 3600]
# Note: The interval at which deepflow-agent actively requests configuration and
#   tag information from deepflow-server.
sync_interval: 60

# Platform Sync Interval
# Unit: second. Default: 10. Range: [10, 3600]
# Note: The interval at which deepflow-agent actively reports resource information
#   to deepflow-server.
platform_sync_interval: 10

# Maximum Escape Time
# Unit: seconds. Default: 3600. Range: [600, 2592000]
# Note: The maximum time that the agent is allowed to work normally when it
#   cannot connect to the server. After the timeout, the agent automatically
#   enters the disabled state.
max_escape_seconds: 3600

# UDP maximum MTU, unit: bytes, default value: 1500, value range [500, 10000]
# Note: Maximum MTU allowed when using UDP to transfer data.
# Attention: Public cloud service providers may modify the content of the
#   tail of the UDP packet whose packet length is close to 1500 bytes. When
#   using UDP transmission, it is recommended to set a slightly smaller value.
mtu: 1500

# Raw UDP VLAN Tag
# Default: 0, means no VLAN tag. Range: [0, 4095]
# Note: When using Raw Socket to transmit UDP data, this value can be used to
#   set the VLAN tag
output_vlan: 0

# Request NAT IP
# Default: 0. Options: 0, 1
# Note: Used when deepflow-agent uses an external IP address to access
#   deepflow-server. For example, when deepflow-server is behind a NAT gateway,
#   or the host where deepflow-server is located has multiple node IP addresses
#   and different deepflow-agents need to access different node IPs, you can
#   set an additional NAT IP for each deepflow-server address, and modify this
#   value to 1.
nat_ip_enabled: 0

# Log Retention Time
# Unit: days. Default: 30. Range: [7, 365]
log_retention: 300

# Control Plane Server Port
# Default: 30035. Range: 1-65535
# Note: The control plane port used by deepflow-agent to access deepflow-server.
#   The default port within the same K8s cluster is 20035, and the default port
#   of deepflow-agent outside the cluster is 30035.
proxy_controller_port: 30035

# Data Plane Server Port
# Default: 30033. Range: 1-65535
# Note: The data plane port used by deepflow-agent to access deepflow-server.
#   The default port within the same K8s cluster is 20033, and the default port
#   of deepflow-agent outside the cluster is 30033.
analyzer_port: 30033

# Fixed Control Plane Server IP
# Note: When this value is set, deepflow-agent will use this IP to access the
#   control plane port of deepflow-server, which is usually used when
#   deepflow-server uses an external load balancer.
proxy_controller_ip:

# Fixed Data Plane Server IP
# Note: When this value is set, deepflow-agent will use this IP to access the
#   data plane port of deepflow-server, which is usually used when
#   deepflow-server uses an external load balancer.
analyzer_ip:

#############################
## Collector Configuration ##
#############################
# Data Socket Type
# Default: TCP. Options: TCP, UDP, FILE
# Note: It can only be set to FILE in standalone mode, in which case
#   l4_flow_log and l7_flow_log will be written to local files.
collector_socket_type: TCP

# PCAP Socket Type
# Default: TCP. Options: TCP, UDP, RAW_UDP
# Note: RAW_UDP uses RawSocket to send UDP packets, which has the highest
#   performance, but there may be compatibility issues in some environments.
compressor_socket_type: TCP

# HTTP Real Client Key
# Default: X-Forwarded-For.
# Note: It is used to extract the real client IP field in the HTTP header,
#   such as X-Forwarded-For, etc. Leave it empty to disable this feature.
http_log_proxy_client: X-Forwarded-For

# HTTP X-Request-ID Key
# Default: X-Request-ID
# Note: It is used to extract the fields in the HTTP header that are used
#   to uniquely identify the same request before and after the gateway,
#   such as X-Request-ID, etc. This feature can be turned off by setting
#   it to empty.
http_log_x_request_id: X-Request-ID

# TraceID Keys
# Default: traceparent, sw8.
# Note: Used to extract the TraceID field in HTTP and RPC headers, supports filling
#   in multiple values separated by commas. This feature can be turned off by
#   setting it to empty.
http_log_trace_id: traceparent, sw8

# SpanID Keys
# Default: traceparent, sw8.
# Note: Used to extract the SpanID field in HTTP and RPC headers, supports filling
#   in multiple values separated by commas. This feature can be turned off by
#   setting it to empty.
http_log_span_id: traceparent, sw8

# Protocol Identification Maximun Packet Length
# Default: 1024. Bpf Range: [256, 65535], Ebpf Range: [256, 16384]
# Note: The maximum data length used for application protocol identification,
#   note that the effective value is less than or equal to the value of
#   capture_packet_size.
l7_log_packet_size: 1024

# Maximum Sending Rate for l4_flow_log
# Default: 10000. Range: [100, [1000000]
# Note: The maximum number of rows of l4_flow_log sent per second, when the actual
#   number of rows exceeds this value, sampling is triggered.
l4_log_collect_nps_threshold: 10000

# Maximum Sending Rate for l7_flow_log
# Default: 10000. Range: [100, [1000000]
# Note: The maximum number of rows of l7_flow_log sent per second, when the actual
#   number of rows exceeds this value, sampling is triggered.
l7_log_collect_nps_threshold: 10000

#######################
## NPB Configuration ##
#######################
# NPB Socket Type
# Default: RAW_UDP. Options: UDP, RAW_UDP, TCP, ZMQ
# Note: RAW_UDP uses RawSocket to send UDP packets, which has the highest
#   performance, but there may be compatibility issues in some environments.
npb_socket_type: RAW_UDP

# Inner Additional Header
# Default: 0, means none. Options: 0, 1 (Additional 802.1Q Header), 2 (QinQ)
# Note: Whether to add an extra 802.1Q header to NPB traffic, when this value is
#   set, deepflow-agent will insert a VLAN Tag into the NPB traffic header, and
#   the value is the lower 12 bits of TunnelID in the VXLAN header.
npb_vlan_mode: 0

##############################
## Management Configuration ##
##############################
# KVM/Host Metadata Collection
# Default: 0, means disabled. Options: 0 (disabled), 1 (enabled).
# Node: When enabled, deepflow-agent will automatically synchronize virtual
#   machine and network information on the KVM (or Host) to deepflow-server.
platform_enabled: 0

# Self Log Sending
# Default: 1, means enabled. Options: 0 (disabled), 1 (enabled).
# Note: When enabled, deepflow-agent will send its own logs to deepflow-server.
rsyslog_enabled: 1

# NTP Synchronization
# Default: 0, means enabled. Options: 0 (disabled), 1 (enabled).
# Note: Whether to synchronize the clock to the deepflow-server, this behavior
#   will not change the time of the deepflow-agent running environment.
ntp_enabled: 0

# Resource MAC/IP Address Delivery
# Default: 0, which means all domains, or can be set to a list of lcuuid of a
#   series of domains, you can get lcuuid through 'deepflow-ctl domain list'.
# Note: The list of MAC and IP addresses is used by deepflow-agent to inject tags
#   into data. This configuration can reduce the number and frequency of MAC and
#   IP addresses delivered by deepflow-server to deepflow-agent. When there is no
#   cross-domain service request, deepflow-server can be configured to only deliver
#   the information in the local domain to deepflow-agent.
domains:
- 0

# Pod MAC/IP Address Delivery
# Default: 0, which means all K8s cluster.
# Options: 0 (all K8s cluster), 1 (local K8s cluster).
# Note: The list of MAC and IP addresses is used by deepflow-agent to inject tags
#   into data. This configuration can reduce the number and frequency of MAC and IP
#   addresses delivered by deepflow-server to deepflow-agent. When the Pod IP is not
#   used for direct communication between the K8s cluster and the outside world,
#   deepflow-server can be configured to only deliver the information in the local
#   K8s cluster to deepflow-agent.
pod_cluster_internal_ip: 0

########################
## Collector Switches ##
########################
# AutoMetrics & AutoLogging
# Default: 1. Options: 0 (disabled), 1 (enabled).
# Note: When disabled, deepflow-agent will not send metrics and logging data
#   collected using eBPF and cBPF.
collector_enabled: 1

# Detailed Metrics for Inactive Port
# Default: 1. Options: 0 (disabled), 1 (enabled).
# Note: When closed, deepflow-agent will not generate detailed metrics for each
#   inactive port (ports that only receive data, not send data), and the data of
#   all inactive ports will be aggregated into the metrics with a tag
#   'server_port = 0'.
inactive_server_port_enabled: 1

# Detailed Metrics for Inactive IP Address
# Default: 1. Options: 0 (disabled), 1 (enabled).
# Note: When closed, deepflow-agent will not generate detailed metrics for each
#   inactive IP address (IP addresses that only receive data, not send data), and
#   the data of all inactive IP addresses will be aggregated into the metrics with
#   a tag 'ip = 0'.
inactive_ip_enabled: 1

# NPM Metrics
# Default: 1. Options: 0 (disabled), 1 (enabled).
# Note: When closed, deepflow-agent only collects some basic throughput metrics.
l4_performance_enabled: 1

# APM Metrics
# Default: 1. Options: 0 (disabled), 1 (enabled).
# Note: When closed, deepflow-agent will not collect RED (request/error/delay) metrics.
l7_metrics_enabled: 1

# Second Granularity Metrics
# Default: 1. Options: 0 (disabled), 1 (enabled).
vtap_flow_1s_enabled: 1

# TAPs Collect l4_flow_log
# Default: 0, which means all TAPs. Options: -1 (disabled), 0 (all TAPs)
# Note: The list of TAPs to collect l4_flow_log, you can also set a list of TAPs to
#   be collected.
l4_log_tap_types:
- 0

# TAPs Collect l7_flow_log
# Default: 0, which means all TAPs. Options: -1 (disabled), 0 (all TAPs)
# Note: The list of TAPs to collect l7_flow_log, you can also set a list of TAPs to
#   be collected.
l7_log_store_tap_types:
- 0

# L4 flow log ignored tap sides
# Default: [], stores everything.
# Note: Use the value of tap_side to control which l4_flow_log should be ignored for
# collection. This configuration also applies to tcp_sequence and pcap data in
# the Enterprise Edition.
# Supported values:
# - 0 (rest: Other NIC)
# - 1 (c: Client NIC)
# - 2 (s: Server NIC)
# - 4 (local: Local NIC)
# - 9 (c-nd: Client K8s Node)
# - 10 (s-nd: Server K8s Node)
# - 17 (c-hv: Client VM Hypervisor)
# - 18 (s-hv: Server VM Hypervisor)
# - 25 (c-gw-hv: Client-side Gateway Hypervisor)
# - 26 (s-gw-hv: Server-side Gateway Hypervisor)
# - 33 (c-gw: Client-side Gateway)
# - 34 (s-gw: Server-side Gateway)
# - 41 (c-p: Client Process)
# - 42 (s-p: Server Process)
l4_log_ignore_tap_sides: []

# L7 flow log ignored tap sides
# Default: [], stores everything.
# Note: Use the value of tap_side to control which l7_flow_log should be ignored for
# collection.
# Supported values: See `l4_log_ignore_tap_sides`.
l7_log_ignore_tap_sides: []

############
## plugin ##
############
# wasm plugin need to load in agent
wasm_plugins: []

# so plugin need to load in agent
# Note: so plugin use dlopen flag RTLD_LOCAL and RTLD_LAZY to open the so file, it mean that
# the so must solve the link problem by itself
so_plugins: []

# Data Integration Socket
# Default: 1. Options: 0 (disabled), 1 (enabled).
# Note: Whether to enable receiving external data sources such as Prometheus,
#   Telegraf, OpenTelemetry, and SkyWalking.
external_agent_http_proxy_enabled: 1

# Listen Port of the Data Integration Socket
# Default: 38086. Options: [1, 65535]
external_agent_http_proxy_port: 38086

##################
## NPB Switches ##
##################
# Global Deduplication
# Default: 1. Options: 0 (disabled), 1 (enabled).
# Note: Whether to enable global (distributed) traffic deduplication for the
#   NPB feature.
npb_dedup_enabled: 1

############################
## Advanced Configuration ##
############################
static_config:

  ###################
  ## K8s apiserver ##
  ###################
  # K8s Namespace
  # Note: Used when deepflow-agent has only one k8s namespace query permission.
  kubernetes-namespace:

  # K8s api list limit
  # Default: 1000. Options: [10, 4294967296)
  # Note: Used when limit k8s api list entry size.
  kubernetes-api-list-limit: 1000

  # K8s api list interval
  # Default: 10m. Must be larger than or equal to 10m.
  # Note: Interval of listing resource when watcher idles
  kubernetes-api-list-interval: 10m

  # K8s api resources
  # Note: Specify kubernetes resources to watch.
  #    The schematics of entries in list is:
  #    {
  #        name: string
  #        group: string
  #        version: string
  #        disabled: bool
  #        field-selector: string
  #    }
  #
  #    Agent will watch the following resources by default:
  #    - namespaces
  #    - nodes
  #    - pods
  #    - replicationcontrollers
  #    - services
  #    - daemonsets
  #    - deployments
  #    - replicasets
  #    - statefulsets
  #    - ingresses
  #
  #    To disable a resource, add an entry to the list with `disabled: true`:
  #
  #        kubernetes-resources:
  #        - name: services
  #          disabled: true
  #
  #    To enable a resource, add an entry of this resource to the list. Be advised that
  #    this setting overrides the default of the same resource. For example, to enable
  #    `statefulsets` in both group `apps` (the default) and `apps.kruise.io` will require
  #    two entries:
  #
  #        kubernetes-resources:
  #        - name: statefulsets
  #          group: apps
  #        - name: statefulsets
  #          group: apps.kruise.io
  #          version: v1beta1
  #
  #    The old `ingress-flavour` setting is deprecated. Watching `routes` in openshift will
  #    use these settings:
  #
  #        kubernetes-resources:
  #        - name: ingresses
  #          disabled: true
  #        - name: routes
  #
  kubernetes-resources: []

  # [Deprecated] Type of Ingress
  # Note: This config is deprecated. Use `kubernetes-resources` instead.
  ingress-flavour: kubernetes

  # Pod MAC/IP Address Query Method
  # Default: adaptive. Options: adaptive, active, passive.
  # Note: In active mode, deepflow-agent enters the netns of other Pods through
  #   setns syscall to query the MAC and IP addresses. In this mode, the setns
  #   operation requires the SYS_ADMIN permission. In passive mode deepflow-agent
  #   calculates the MAC and IP addresses used by Pods by capturing ARP/ND traffic.
  #   When set to adaptive, active mode will be used first.
  kubernetes-poller-type: adaptive

  #########################
  ## Debug Configuration ##
  #########################
  # Golang Profiler
  # Note: Only available for Trident (Golang version of Agent).
  profiler: false

  # Client Port for deepflow-agent-ctl
  # Default: 0, which means use a random client port number.
  # Note: Only available for Trident (Golang version of Agent).
  debug-listen-port: 0

  # StatsD Counters For Sniffer
  # Note: Only available for Trident (Golang version of Agent).
  enable-debug-stats: false

  ###############
  ## AF_PACKET ##
  ###############
  # AF_PACKET Blocks Switch
  # Note: When tap_mode != 2, you need to explicitly turn on this switch to
  #   configure 'afpacket-blocks'.
  afpacket-blocks-enabled: false

  # AF_PACKET Blocks
  # Default: 128, Range: [8, +oo)
  # Note: deepflow-agent will automatically calculate the number of blocks
  #   used by AF_PACKET according to max_memory, which can also be specified
  #   using this configuration item. The size of each block is fixed at 1MB.
  afpacket-blocks: 128

  ###################
  ## Analyzer Mode ##
  ###################
  # Mirror Traffic Dedup
  # Note: Whether to enable mirror traffic deduplication when tap_mode = 2.
  analyzer-dedup-disabled: false

  # Buffer block size used to store raw packet.
  # Larger value will reduce memory allocation for raw packet, but will also
  # delay memory free.
  # Default: 65536, Range: [65536: +oo)
  analyzer-raw-packet-block-size: 65536

  # Default TAP for Mirror Traffic
  # Default: 3, means Cloud Network
  # Options: 1-2,4-255 (IDC Network), 3 (Cloud Network)
  # Note: deepflow-agent will mark the TAP (Traffic Access Point) location
  #   according to the outer vlan tag in the mirrored traffic of the physical
  #   switch. When the vlan tag has no corresponding TAP value, or the vlan
  #   pcp does not match the 'mirror-traffic-pcp', it will assign the TAP value.
  #   This configuration item.
  default-tap-type: 3

  # Mirror Traffic PCP
  # Default: 0, Range: [0, 9]
  # Note: When mirror-traffic-pcp <= 7 calculate TAP value from vlan tag only if vlan pcp matches this value.
  #   when mirror-traffic-pcp is 8 calculate TAP value from outer vlan tag, when mirror-traffic-pcp is 9
  #   calculate TAP value from inner vlan tag.
  mirror-traffic-pcp: 0

  # NFVGW Traffic
  # Note: Whether it is the mirrored traffic of NFVGW (cloud gateway).
  cloud-gateway-traffic: false

  ############
  ## Sender ##
  ############
  # RAW_UDP Sender Performance Optimization
  # Note: When sender uses RAW_UDP to send data, this feature can be enabled to
  #   improve performance. Linux Kernel >= 3.14 is required. Note that the data
  #   sent when this feature is enabled cannot be captured by tcpdump.
  enable-qos-bypass: false

  # Multiple Sockets To Ingester
  # Note: When set to true, deepflow-agent will send data with multiple sockets to Ingester,
  #   which has higher performance, but will bring more impact to the firewall.
  multiple-sockets-to-ingester: false

  #####################
  ## NPB/PCAP Policy ##
  #####################
  # Fast Path Map Size
  # Note: When set to 0, deepflow-agent will automatically adjust the map size
  #   according to max_memory.
  fast-path-map-size: 0

  # Fast Path Disabled
  # Note: When set to true, deepflow-agent will not use fast path.
  fast-path-disabled: false

  # Forward Table Capacity
  # Default: 16384. Range: [16384, +oo)
  # Note: When this value is larger, the more memory usage may be
  forward-capacity: 16384

  # Fast Path Level
  # Default: 8. Range: [1, 16]
  # Note: When this value is larger, the memory overhead is smaller, but the
  #   performance of policy matching is worse.
  first-path-level: 8

  ################
  ## Dispatcher ##
  ################
  # TAP NICs when tap_mode != 0
  # Note: Deprecated and instead use tap_interface_regex
  src-interfaces:
  - dummy0
  - dummy1

  # Bond sub interface configuration
  # Default: []
  # Note: Packets of interfaces in the same group can be aggregated together,
  #   Only effective when tap_mode is 0.
  tap-interface-bond-groups:
  - tap-interfaces: []

  # Local dispatcher count
  # Default: 1. Range: [1, +oo)
  # Note: The configuration takes effect when tap_mode is 0 and extra_netns_regex is null,
  #   PACKET_FANOUT is to enable load balancing and parallel processing, which can improve
  #   the performance and scalability of network applications. When the `local-dispatcher-count`
  #   is greater than 1, multiple dispatcher threads will be launched, consuming more CPU and
  #   memory. Increasing the `local-dispatcher-count` helps to reduce the operating system's
  #   software interrupts on multi-core CPU servers.
  local-dispatcher-count: 1

  # Packet fanout mode
  # Note: The configuration is a parameter used with the PACKET_FANOUT feature in the Linux
  #   kernel to specify the desired packet distribution algorithm. Refer to
  #   https://github.com/torvalds/linux/blob/afcd48134c58d6af45fb3fdb648f1260b20f2326/include/uapi/linux/if_packet.h#L71
  #   https://www.stackpath.com/blog/bpf-hook-points-part-1/
  # Default: 0. Range: [0, 7]
  # PACKET_FANOUT_HASH = 0
  # PACKET_FANOUT_LB = 1
  # PACKET_FANOUT_CPU = 2
  # PACKET_FANOUT_ROLLOVER = 3
  # PACKET_FANOUT_RND = 4
  # PACKET_FANOUT_QM = 5
  # PACKET_FANOUT_CBPF = 6
  # PACKET_FANOUT_EBPF = 7
  packet-fanout-mode: 0

  # Dispatcher queue
  # Note: The configuration takes effect when tap_mode is 0 or 2, dispatcher-queue is always true when tap_mode is 2
  dispatcher-queue: false

  ####################
  ## InMemory Queue ##
  ####################
  # Queue Size of FlowGenerator Output
  # Default: 65536. Range: [65536, +oo)
  # Note: the length of the following queues:
  #   - 1-tagged-flow-to-quadruple-generator
  #   - 1-tagged-flow-to-app-protocol-logs
  #   - 0-{flow_type}-{port}-packet-to-tagged-flow, flow_type: sflow, netflow
  flow-queue-size: 65536

  # Queue Size of QuadrupleGenerator Output
  # Default: 262144. Range: [262144, +oo)
  # Note: the length of the following queues:
  #   - 2-flow-with-meter-to-second-collector
  #   - 2-flow-with-meter-to-minute-collector
  quadruple-queue-size: 262144

  # Queue Size of Collector Output
  # Default: 65536. Range: [65536, +oo)
  # Note: the length of the following queues:
  #   - 2-doc-to-collector-sender
  collector-sender-queue-size: 65536

  # Queue Count of Collector Output
  # Default: 1. Range: [1, +oo)
  # Note: The number of replicas for each output queue of the collector.
  collector-sender-queue-count: 1

  # Queue Size of tcp option address info sync queue size
  # Default: 65536. Range: [1, +oo)
  # Note: The number of replicas for each output queue of the collector.
  toa-sender-queue-size: 65536

  # Queue Size of FlowAggregator/SessionAggregator Output
  # Default: 65536. Range: [65536, +oo)
  # Note: the length of the following queues:
  #   - 3-flow-to-collector-sender
  #   - 3-protolog-to-collector-sender
  flow-sender-queue-size: 65536

  # Queue Count of FlowAggregator/SessionAggregator Output
  # Default: 1. Range: [1, +oo)
  # Note: The number of replicas for each output queue of the
  #   FlowAggregator/SessionAggregator.
  flow-sender-queue-count: 1

  # Queue Size for Analyzer Mode
  # Default: 131072. Range: [65536, +oo)
  # Note: the length of the following queues (only for tap_mode = 2):
  #   - 0.1-bytes-to-parse
  #   - 0.2-packet-to-flowgenerator
  #   - 0.3-packet-to-pipeline
  analyzer-queue-size: 131072

  #########
  ## LRU ##
  #########

  # Size of tcp option address info cache size
  # Default: 65536. Range: [1, +oo)
  toa-lru-cache-size: 65536

  ###########################
  ## Time Window Tolerance ##
  ###########################
  # Extra Tolerance for QuadrupleGenerator Receiving 1s-FlowLog
  # Format: ${number}${time_unit}
  # Example: 1s, 2m, 10h
  second-flow-extra-delay-second: 0s

  # Maximum Tolerable Packet Delay
  # Default: 1s
  # Format: $number$time_unit
  # Example: 1s, 2m, 10h
  # Note: The timestamp carried by the packet captured by AF_PACKET may be delayed
  #   from the current clock, especially in heavy traffic scenarios, which may be
  #   as high as nearly 10s.
  packet-delay: 1s

  # l7_flow_log Aggregate Window
  # Default: 120s. Range: [20s, 300s]
  # Format: $number$time_unit
  # Example: 1s, 2m, 10h
  l7-log-session-aggr-timeout: 120s

  # Capacity of Each l7_flow_log Aggregation Time Slot
  # Default: 1024. Range: [1024, +∞)
  # Note: By default, unidirectional l7_flow_log is aggregated into bidirectional
  #   request_log (session) with a caching time window of 2 minutes. During this
  #   period, every 5 seconds is considered as a time slot (i.e., a LRU). This
  #   configuration is used to specify the maximum number of unidirectional l7_flow_log
  #   entries that can be cached in each time slot.
  #       If the number of l7_flow_log entries cached in a time slot exceeds this
  #   configuration, 10% of the data in that time slot will be evicted based on the
  #   LRU strategy to reduce memory consumption. Note that the evicted data will not be
  #   discarded; instead, they will be sent to the deepflow-server as unidirectional
  #   request_log.
  #       The following metrics can be used as reference data for adjusting this
  #   configuration:
  #   - Metric `deepflow_system.deepflow_agent_l7_session_aggr.cached-request-resource`
  #     Used to record the total memory occupied by the request_resource field of the
  #     unidirectional l7_flow_log cached in all time slots at the current moment, in bytes
  #   - Metric `deepflow_system.deepflow_agent_l7_session_aggr.over-limit`
  #     Used to record the number of times eviction is triggered due to reaching the
  #     LRU capacity limit
  l7-log-session-slot-capacity: 1024

  # Consistent Timestamp in L7 Metrics
  # NOTE: When this configuration is enabled, for the same session, response-related metrics (such as
  #   response count, latency, exceptions) are recorded in the time slot corresponding to when the request
  #   occurred, rather than the time slot of the response itself. This means that when calculating metrics
  #   for requests and responses within a session, a consistent timestamp based on the time of the request
  #   occurrence is used.
  consistent-timestamp-in-l7-metrics: false

  # Packet segmentation reassembly
  # NOTE: After activation, it will aggregate two consecutive TCP packets together for application log parsing
  packet-segmentation-reassembly: []

  ##########
  ## PCAP ##
  ##########
  pcap:
    # Queue Size to PCAP Generator
    # Default: 65536. Range: [65536, +oo)
    # Note: the length of the following queues:
    #   - 1-mini-meta-packet-to-pcap
    queue-size: 65536

    # Pcap buffer size for each flow
    # Default: 64K
    # Note: buffer flushes when one of the flows reach this limit
    flow-buffer-size: 65536

    # Total pcap buffer size
    # Default: 96K
    # Note: buffer flushes when total data size reach this limit
    #       cannot exceed sender buffer size 128K
    buffer-size: 98304

    # Flow flush interval
    # Default: 1m
    # Note: flushes a flow if its first packet were older then this interval
    flush-interval: 1m

  #############################
  ## FlowMap (FlowGenerator) ##
  #############################
  flow:
    # HashSlot Size of FlowMap
    # Default: 131072
    # Note: Since FlowAggregator is the first step in all processing, this value
    #   is also widely used in other hash tables such as QuadrupleGenerator,
    #   Collector, etc.
    flow-slots-size: 131072

    # Maximum Flow
    # Default: 65535
    # Note: Maximum number of flows that can be stored in FlowMap, It will also affect the capacity of
    #   the RRT cache, Example: rrt-cache-capacity = flow-count-limit. When rrt-cache-capacity is
    #   not enough, it will be unable to calculate the rrt of l7.
    flow-count-limit: 65535

    # Queue Size of FlowAggregator (1s->1m)
    # Default: 65536. Range: [65536, +oo)
    # Note: the length of the following queues:
    #   - 2-second-flow-to-minute-aggrer
    flow-aggr-queue-size: 65535

    # Flush Interval of FlowMap Output Queue
    # Format: $number$time_unit
    # Example: 1s, 2m, 10h
    # Note: Flush interval of the queue connected to the collector.
    flush-interval: 1s

    # Ignore MAC when Generate Flow
    # Note: When the MAC addresses of the two-way traffic collected at the same
    #   location are asymmetrical, the traffic cannot be aggregated into a Flow.
    #   You can set this value at this time. Only valid for Cloud (not IDC) traffic.
    ignore-tor-mac: false

    # Ignore L2End when Generate Flow
    # Note: For Cloud traffic, only the MAC address corresponding to the side with
    #   L2End = true is matched when generating the flow. Set this value to true to
    #   force a double-sided MAC address match and only aggregate traffic with
    #   exactly equal MAC addresses.
    ignore-l2-end: false

    # Ignore VLAN when Generate Flow
    # Note: When the VLAN of the two-way traffic collected at the same location
    #   are asymmetrical, the traffic cannot be aggregated into a Flow. You can
    #   set this value at this time. Only valid for IDC (not Cloud) traffic.
    ignore-idc-vlan: false

    # Timeouts for TCP State Machine
    # Format: $number$time_unit
    # Example: 1s, 2m, 10h
    established-timeout: 300s
    closing-rst-timeout: 35s
    others-timeout: 5s
    opening-rst-timeout: 1s

    # Size of memory pool used in flow_map
    # Default: 65536
    # Note: This value is used to set max length of memory pool in FlowMap
    #   Memory pools are used for frequently create and destroy objects like
    #   FlowNode, FlowLog, etc.
    memory-pool-size: 65536

  # Service port list, priority lower than TCP SYN flags
  # Example:
  # server-ports:
  # - 80
  # - 443
  server-ports: []

  # Max size of batched buffer
  # Default: 131072. Range: [1024, +oo)
  # Note: Only TaggedFlow allocation is affected at the moment.
  #   Structs will be allocated in batch to minimalize malloc calls.
  #   Total memory size of a batch will not exceed this limit.
  #   A number larger than 128K is not recommended because the default
  #   MMAP_THRESHOLD is 128K, allocating chunks larger than 128K will
  #   result in calling mmap and more page faults.
  batched-buffer-size-limit: 131072

  #####################
  ## DPDK RecvEngine ##
  #####################
  # Enable for DPDK RecvEngine
  # Note: The DPDK RecvEngine is only started when this configuration item is turned on.
  #   Note that you also need to set tap_mode to 1. Please refer to
  #   https://dpdk-docs.readthedocs.io/en/latest/prog_guide/multi_proc_support.html
  dpdk-enabled: false

  ########################
  ## Libpcap RecvEngine ##
  ########################
  # Enable for Libpcap RecvEngine
  # Note: Supports running on Windows and Linux, Low performance when using multiple interfaces.
  #   Default to true in Windows, false in Linux.
  libpcap-enabled: false

  ###########################
  ## vHost User RecvEngine ##
  ###########################
  # Enable for vHost User RecvEngine
  # Note: Supports running on Linux with mirror mode.
  vhost-socket-path:

  #################################
  ## sFlow / NetFlow / NetStream ##
  #################################
  # sFlow & NetFlow Server Ports
  xflow-collector:
    # sFlow Server Ports
    # Default: [], means that no sFlow data will be collected.
    # Note: This feature is only supported by the Enterprise Edition of Trident.
    #   In general, sFlow uses port 6343.
    sflow-ports: []

    # NetFlow Server Ports
    # Default: [], means that no NetFlow data will be collected.
    # Note: This feature is only supported by the Enterprise Edition of Trident.
    #   Additionally, only NetFlow v5 is currently supported. In general, NetFlow
    #   uses port 2055.
    netflow-ports: []

  #########
  ## NPB ##
  #########
  # Server Port for NPB
  npb-port: 4789

  # Reserve Flags for VXLAN
  # Default: 0xff. Range: [0x00, 0xff], except 0x8.
  # Note: NPB uses the first byte of the VXLAN Flag to identify the sending traffic to
  #   prevent the traffic sent by NPB from being collected by deepflow-agent. To ensure
  #   that the VNI bit is set, the value configured here will be used after |= 0x8.
  vxlan-flags: 0xff

  # Capture bpf without npb filter.
  # Default: false. Range: [true, false]
  # Note:
  #   If the NIC on the data plane has ERSPAN tunnel traffic but does not NPB traffic,
  # enable the switch to collect ERSPAN traffic.
  skip-npb-bpf: false

  # NPB Packet ignoring VLAN Header in overlay
  # Default: false. Range: [true, false]
  # Note:
  #   This configuration only ignores the VLAN header in the captured original message
  # and does not affect the configuration item: npb_vlan_mode
  ignore-overlay-vlan: false

  ############
  ## Tunnel ##
  ############
  # Remove Tunnel Header
  # Default: [], Range: [ERSPAN, VXLAN, TEB]
  # Note: Whether to remove the tunnel header in mirrored traffic.
  #   Only the Enterprise Edition supports decap ERSPAN and TEB.
  trim-tunnel-types: []

  ##########
  ## gRPC ##
  ##########
  # gRPC Socket Buffer Size
  # Default: 5. Unit: MB
  grpc-buffer-size: 5

  #############################
  ## TAP MAC Address Mapping ##
  #############################
  # TAP MAC Mapping Script
  # Note: The MAC address mapping relationship of TAP NIC in complex environment can be
  #   constructed by writing a script. The following conditions must be met to use this
  #   script:
  #   1. if_mac_source = 2
  #   2. tap_mode = 0
  #   3. The name of the TAP NIC is the same as in the virtual machine XML file
  #   4. The format of the script output is as follows:
  # tap2d283dfe,11:22:33:44:55:66
  # tap2d283223,aa:bb:cc:dd:ee:ff
  tap-mac-script: ""

  #########
  ## BPF ##
  #########
  # BPF Filter
  # Note: It is found that there may be bugs in BPF traffic filtering under some
  #   versions of Linux Kernel. After this configuration is enabled, deepflow-agent
  #   will not use the filtering capabilities of BPF, and will filter by itself after
  #   capturing full traffic. Note that this may significantly increase the resource
  #   overhead of deepflow-agent.
  bpf-disabled: false

  #################
  ## L7 Protocol ##
  #################
  # L7 perf rrt calculate timeout
  # Note: the timeout of l7 log info rrt calculate, when rrt exceed the value will act as timeout and will not
  #   calculate the sum and average and will not merge the request and response in session aggregate. the value
  #   must greater than session aggregate SLOT_TIME(const 10s) and less than 3600 on tcp or 300 on udp.
  rrt-tcp-timeout: 1800s
  rrt-udp-timeout: 150s

  # Maximum Fail Count
  # Note: deepflow-agent will mark the long live stream and application protocol for each
  #   <vpc, ip, protocol, port> tuple, when the traffic corresponding to a tuple fails
  #   to be identified for many times (for multiple packets, Socket Data, Function Data),
  #   the tuple will be marked as an unknown type to avoid deepflow-agent continuing to
  #   try (incurring significant computational overhead) until the duration exceeds
  #   l7-protocol-inference-ttl.
  l7-protocol-inference-max-fail-count: 5

  # TTL of Protocol Identification
  # Unit: second
  # Note: deepflow-agent will mark the application protocol for each
  #   <vpc, ip, protocol, port> tuple. In order to avoid misidentification caused by IP
  #   changes, the validity period after successfully identifying the protocol will be
  #   limited to this value.
  l7-protocol-inference-ttl: 60

  # List of Application Protocols
  # Note: Turning off some protocol identification can reduce deepflow-agent resource consumption.
  l7-protocol-enabled:
  - HTTP
  - HTTP2 ## for both HTTP2 and gRPC
  #- Dubbo
  #- SofaRPC
  #- FastCGI
  #- bRPC
  #- Tars
  #- SomeIP
  - MySQL
  #- PostgreSQL
  #- Oracle
  - Redis
  #- MongoDB
  #- Memcached
  - Kafka
  #- MQTT
  #- AMQP
  #- OpenWire
  #- NATS
  #- Pulsar
  #- ZMTP
  #- RocketMQ
  - DNS
  - TLS
  #- Custom ## custom protocol from plugin

  # Application Protocol Port Numbers
  # Default: 53,5353 for DNS, 443,6443 for TLS, 1-65535 for other Protocols.
  # Format: map<protocol-name, port-list>
  # Example: "HTTP": 80,1000-2000
  # Note: HTTP2 and TLS are only used for Kprobe, not applicable to Uprobe.
  #   All data obtained through Uprobe is not subject to port restrictions.
  l7-protocol-ports:
    "HTTP": "1-65535"
    "HTTP2": "1-65535" # for both HTTP2 and gRPC
    "Dubbo": "1-65535"
    "SofaRPC": "1-65535"
    "FastCGI": "1-65535"
    "bRPC": "1-65535"
    "Tars": "1-65535"
    "SomeIP": "1-65535"
    "MySQL": "1-65535"
    "PostgreSQL": "1-65535"
    "Oracle": "1521"
    "Redis": "1-65535"
    "MongoDB": "1-65535"
    "Memcached": "11211"
    "Kafka": "1-65535"
    "MQTT": "1-65535"
    "AMQP": "1-65535"
    "OpenWire": "1-65535"
    "NATS": "1-65535"
    "Pulsar": "1-65535"
    "ZMTP": "1-65535"
    "RocketMQ": "1-65535"
    "DNS": "53,5353"
    "TLS": "443,6443"
    "Custom": "1-65535" # plugins

  # l7_flow_log Blacklist
  # Example:
  #   l7-log-blacklist:
  #     HTTP:
  #     - field-name: request_resource  # endpoint, request_type, request_domain, request_resource
  #       operator: equal  # equal, prefix
  #       value: somevalue
  # Note: A l7_flow_log blacklist can be configured for each protocol, preventing request logs matching
  #   the blacklist from being collected by the agent or included in application performance metrics.
  #   It's recommended to only place non-business request logs like heartbeats or health checks in this
  #   blacklist. Including business request logs might lead to breaks in the distributed tracing tree.
  l7-log-blacklist:
    HTTP: []
    HTTP2: []
    Dubbo: []
    gRPC: []
    SOFARPC: []
    FastCGI: []
    bRPC: []
    Tars: []
    SomeIP: []
    MySQL: []
    PostgreSQL: []
    Oracle: []
    Redis: []
    MongoDB: []
    Kafka: []
    MQTT: []
    AMQP: []
    OpenWire: []
    NATS: []
    Pulsar: []
    ZMTP: []
    RocketMQ: []
    DNS: []
    TLS: []

  # L7 Protocol Advanced Features
  l7-protocol-advanced-features:

    # Extract the configuration of http endpoint
    http-endpoint-extraction:
      # Disable the switch to extract http endpoint
      # Default: false, by default, http endpoint is extracted
      disabled: false

      # Extract endpoint according to the following rules
      match-rules:
      # Find a longest prefix that can match according to the principle of "longest prefix matching"
      - prefix: ""

        # Note: Intercept the first few paragraphs in URL (the content between two / is regarded as one paragraph) as endpoint
        # Default: 2, by default, two segments are extracted from the URL. For example, the URL is /a/b/c?query=xxx", whose segment is 3, extracts "/a/b" as the endpoint
        keep-segments: 2

    # List of L7 protocols that need to be obfuscated
    # Note: For the sake of data security, the data of the protocol that needs
    # to be desensitized is configured here and is not processed by default.
    # If you want to desensitize http url, sql statements and redis commands, you can set it to:
    # obfuscate-enabled-protocols:
    # - MySQL
    # - PostgreSQL
    # - HTTP
    # - HTTP2
    # - Redis
    obfuscate-enabled-protocols:
    - Redis

    # Configuration to extract the customized header fields of HTTP, HTTP2, GRPC protocol etc
    extra-log-fields:
    # for example:
    # http:
    # - field-name: "user-agent"
    # - field-name: "cookie"
      http: []
      http2: []

    # Unconcerned DNS NXDOMAIN Responses
    # Note: You might not be concerned about certain DNS NXDOMAIN errors and may wish to ignore
    #   them. For example, when a K8s Pod tries to resolve an external domain name, it first
    #   concatenates it with the internal domain suffix of the cluster and attempts to resolve
    #   it. All these attempts will receive an NXDOMAIN reply before it finally requests the
    #   original domain name directly, and these errors may not be of concern to you. In such
    #   cases, you can configure their `response_result` suffix here, so that the corresponding
    #   `response_status` in the l7_flow_log is forcibly set to `Success`.
    unconcerned-dns-nxdomain-response-suffixes: []

  oracle-parse-config:
    is-be: true
    int-compress: true
    resp-0x04-extra-byte: false

  ########################
  ## L4 Packet Sequence ##
  ########################
  # Block Size
  # Default: 256. Unit: Byte.
  # Note: When generating TCP header data, each flow uses one block to compress and
  #   store multiple TCP headers, and the block size can be set here.
  packet-sequence-block-size: 256

  # Queue Size of PacketSequence Output
  # Default: 65536. Range: [65536, +oo)
  # Note: the length of the following queues (to UniformCollectSender):
  #   - 1-packet-sequence-block-to-uniform-collect-sender
  packet-sequence-queue-size: 65536

  # Queue Count of PacketSequence Output
  # Default: 1. Range: [1, +oo)
  # Note: The number of replicas for each output queue of the PacketSequence.
  packet-sequence-queue-count: 1

  # Reported Header Fields
  # Default: 0, means to disable this feature.
  # Note: packet-sequence-flag determines which fields need to be reported, the default
  #   value is 0, which means the feature is disabled, and 255, which means all fields
  #   need to be reported all fields corresponding to each bit:
  #   | FLAG | SEQ | ACK | PAYLOAD_SIZE | WINDOW_SIZE | OPT_MSS | OPT_WS | OPT_SACK |
  #   8      7     6     5              4             3         2        1          0
  packet-sequence-flag: 0

  #################
  ## Integration ##
  #################
  # Compress Integration Data
  # Note: Whether to compress the integrated data received by deepflow-agent. Currently,
  #   only opentelemetry data is supported, and the compression ratio is about 5:1~10:1.
  #   Turning on this feature will result in higher CPU consumption of deepflow-agent.
  external-agent-http-proxy-compressed: false

  # Compress Integration Profile Data
  # Note: Whether to compress the integrated profile data received by deepflow-agent. The compression
  #   ratio is about 5:1~10:1. Turning on this feature will result in higher CPU consumption
  #   of deepflow-agent.
  external-agent-http-proxy-profile-compressed: true

  # Prometheus Extra Labels
  # Note: Support for getting extra labels from headers in http requests from remoteWrite.
  prometheus-extra-config:
    # Note: Prometheus Extra Labels Switch
    enabled: false
    # Note: Labels list. Labels in this list are sent. Label is a string matching the regular expression [a-zA-Z_][a-zA-Z0-9_]*
    labels:
    - label1
    - label2
    # Note: The size limit of the parsed key
    # Default: 1024. Unit: B. Range: [1024, 1048576]
    labels-limit: 1024
    # Note: The size limit of the parsed value
    # Default: 4096. Unit: B. Range: [4096, 4194304]
    values-limit: 4096

  ##################################
  ## eBPF Collector Configuration ##
  ##################################
  # Queue Size of eBPF Collector
  # Default: 65536. Range: [4096, +oo)
  # Note: the length of the following queues:
  #   - 0-ebpf-to-ebpf-collector
  #   - 1-proc-event-to-sender
  #   - 1-profile-to-sender
  ebpf-collector-queue-size: 65535

  ########################
  ## eBPF Configuration ##
  ########################
  ebpf:
    # eBPF Switch
    # Default: false
    disabled: false

    # eBPF Packet Capture Rate Limit
    # Default: 0. Range: [0, +oo]
    global-ebpf-pps-threshold: 0

    # Enabled uprobe golang tracing
    # Default: false
    # Note: If this configuration option is set to false, the Golang application process will not
    #   be traced, and the maximum number of entries in the associated BPF map will be set to 1 to
    #   ensure minimal memory usage.
    uprobe-golang-trace-enabled: false

    # Enabled uprobe openssl library tracing
    # Default: false
    # Note: If this configuration option is set to false, the openssl library will not be traced,
    #   and the maximum number of entries in the associated BPF map will be set to 1 to ensure
    #   minimal memory usage.
    uprobe-openssl-trace-enabled: false

    # Regex for Process Name
    # Note: The name of the process where each feature of ebpf uprobe takes effect,
    #   which is configured using regular expressions
    uprobe-process-name-regexs:

      # Process name to enable Golang-specific symbol table parsing.
      # Default: "", which means that this feature is not enabled for any process.
      # Note: This feature acts on Golang processes that have trimmed the standard symbol
      #   table. When this feature is enabled, for processes with Golang
      #   version >= 1.13 and < 1.18, when the standard symbol table is missing, the
      #   Golang-specific symbol table will be parsed to complete uprobe data collection.
      #   Note that enabling this feature may cause the eBPF initialization process to
      #   take ten minutes. The `golang-symbol` configuration item depends on the `golang`
      #   configuration item, the `golang-symbol` is a subset of the `golang` configuration item.
      # Example:
      #   - Ensure that the regular expression matching for the 'golang' configuration
      #     item is enabled, for example: `golang: .*`
      #   - You've encountered the following warning log:
      #     ```
      #     [eBPF] WARNING: func resolve_bin_file() [user/go_tracer.c:558] Go process pid 1946
      #     [path: /proc/1946/root/usr/local/bin/kube-controller-manager] (version: go1.16). Not find any symbols!
      #     ```
      #     Suppose there is a Golang process with a process ID of '1946.'
      #   - To initially confirm whether the executable file for this process has a symbol table:
      #     - Retrieve the executable file's path using the process ID:
      #       ```
      #       # ls -al /proc/1946/exe
      #       /proc/1946/exe -> /usr/local/bin/kube-controller-manager
      #       ```
      #     - Check if there is a symbol table:
      #       ```
      #       # nm /proc/1946/root/usr/local/bin/kube-controller-manager
      #       nm: /proc/1946/root/usr/local/bin/kube-controller-manager: no symbols
      #       ```
      #   - If "no symbols" is encountered, it indicates the absence of a symbol table. In such a
      #     scenario, we need to configure the "golang-symbol" setting. To configure this:
      #     ```
      #     golang-symbol: ^(kube-controller-.*)$
      #     ```
      #     Explanation: Configure a regular expression for the trailing part 'kube-controller-manager'
      #     following the executable file path of the process. It can also be a regular expression for
      #     the process name (obtained through `/proc/<PID>/status` to extract the process `Name`).
      #   - During the agent startup process, you will observe the following log information: (The entry
      #     address for the function `crypto/tls.(*Conn).Write` has already been resolved, i.e., entry:0x25fca0).
      #     ```
      #     [eBPF] INFO Uprobe [/proc/1946/root/usr/local/bin/kube-controller-manager] pid:1946 go1.16.0
      #     entry:0x25fca0 size:1952 symname:crypto/tls.(*Conn).Write probe_func:uprobe_go_tls_write_enter rets_count:0
      #     ```
      #     The logs indicate that the Golang program has been successfully hooked.
      #
      golang-symbol: ""

      # Note: The name of the Golang process that enables HTTP2/HTTPS protocol data collection
      #   and auto-tracing. go auto-tracing also dependent go-tracing-timeout.
      #   The default value is "", which means it is disabled for all Golang processes.
      golang: ""

      # The name of the process that uses the openssl library to enable HTTPS protocol data collection.
      # Default: "",  which means that it is disabled for all processes that use the openssl library.
      # Note: One can use the following method to determine whether an application process can use
      #   `Uprobe hook openssl library` to access encrypted data:
      #   Use the command `cat /proc/<PID>/maps | grep "libssl.so"` to check if it contains
      #   information about openssl. If it does, it indicates that this process is using the
      #   openssl library. After configuring the openssl options, deepflow-agent will retrieve process
      #   information that matches the regular expression, hooking the corresponding encryption/decryption
      #   interfaces of the openssl library.
      #   In the logs, you will encounter a message similar to the following:
      #   `[eBPF] INFO openssl uprobe, pid:1005, path:/proc/1005/root/usr/lib64/libssl.so.1.0.2k`
      openssl: ""

    kprobe-blacklist:
      # TCP&UDP Port Blacklist, Priority higher than kprobe-whitelist.
      # Default: null, means no port
      # Format: x-y, z
      port-list:

    kprobe-whitelist:
      # TCP&UDP Port Whitelist, Priority lower than kprobe-blacklist.
      # Default: null, means no port
      # Format: x-y, z
      port-list:

    # eBPF work-thread number
    # Default: 1. Range: [1, Number of CPU logical cores on the host]
    # Note: The number of worker threads refers to how many threads participate in data processing in user-space.
    thread-num: 1

    # eBPF perf pages count
    # Default: 128. Range: [32, 8192]
    # Note: The number of page occupied by the shared memory of the kernel. The value is 2^n ( n range [5, 13] ). Used for perf data transfer.
    #   If the value is between 2^n and 2^(n+1), it will be automatically adjusted by the ebpf configurator to the minimum value (2^n).
    perf-pages-count: 128

    # eBPF dispatch ring size
    # Default: 65536. Range: [8192, 131072]
    # Note: The size of the ring cache queue, The value is 2^n ( n range [13, 17] ).
    #   If the value is between 2^n and 2^(n+1), it will be automatically adjusted by the ebpf configurator to the minimum value (2^n).
    ring-size: 65536

    # eBPF max socket entries
    # Default: 131072. Range: [10000, 2000000]
    # Note: Set the maximum value of hash table entries for socket tracking, depending on the number of concurrent requests in the actual scenario
    max-socket-entries: 131072

    # eBPF socket map max reclaim
    # Default: 120000. Range: [8000, 2000000]
    # Note: The maximum threshold for cleaning socket map table entries.
    socket-map-max-reclaim: 120000

    # eBPF max trace entries
    # Default: 131072. Range: [10000, 2000000]
    # Note: Set the maximum value of hash table entries for thread/coroutine tracking sessions.
    max-trace-entries: 131072

    # eBPF go trace timeout
    # Default: 120 second[s]. Range: [0, +]
    # Note: The expected maximum time interval between the server receiving the request and returning the response,
    #   If the value is 0, this feature is disabled. Tracing only considers the thread number.
    go-tracing-timeout: 120

    # eBPF IO event collect mode
    # Default: 1 . Range: [0, 1, 2]
    # Note:
    #   0: Indicates that no IO events are collected.
    #   1: Indicates that only IO events within the request life cycle are collected.
    #   2: Indicates that all IO events are collected.
    io-event-collect-mode: 1

    # eBPF IO event minimal duration
    # Default: 1ms. Range: [1ns, 1s]
    # Note: Only collect IO events with delay exceeding this threshold, the default value is 1ms.
    io-event-minimal-duration: 1ms

    # Disable DWARF unwinding
    # Default: true
    # Note:
    #   Processes without frame pointers will be unwinded with DWARF, which will consume more system resources
    #   Set to "false" to enable DWARF
    dwarf-disabled: true

    # DWARF unwinding regex
    # Default: ""
    # Note:
    #   Only processes that match this regex will use DWARF unwinding.
    #   Agent will use heuristic methods to determine whether a process needs to be unwinded with DWARF,
    #   i.e., the process does not have frame pointer, if this setting is left blank
    dwarf-regex: ""

    # DWARF unwind process hash map size
    # Default: 1024. Range: [1, u32max]
    # Note:
    #   Each process using DWARF unwind has an entry in this map, relating process id to DWARF unwind entries.
    #   The size of each one of these entries is arount 8K, the default setting will allocate around 8M kernel memory.
    #   This is a hash map, so size can be lower than max process id.
    #   The configuration is only effective if DWARF is enabled.
    dwarf-process-map-size: 1024

    # DWARF unwind entry shard size
    # Default: 128. Range: [1, u32max]
    # Note:
    #   The number of unwind entry shards for DWARF unwinding
    #   The size of each one of these entries is 1M, the default setting will allocate around 128M kernel memory.
    #   The configuration is only effective if DWARF is enabled.
    dwarf-shard-map-size: 128

    # Java compliant update latency time
    # Default: 60s. Range: [5, 3600]s
    # Note:
    #   When deepflow-agent finds that an unresolved function name appears in the function call stack
    #   of a Java process, it will trigger the regeneration of the symbol file of the process.
    #   Because Java utilizes the Just-In-Time (JIT) compilation mechanism, to obtain more symbols for
    #   Java processes, the regeneration will be deferred for a period of time.
    #
    #   At the startup of a Java program, the JVM and JIT compiler are in a "warm-up" phase. During this
    #   period, symbol changes are typically frequent due to the dynamic compilation and optimization
    #   processes. Therefore, deepflow-agent delay symbol collection for one minute after the Java program
    #   starts, allowing the JVM and JIT to "warm up" and for symbol churn to be minimized before proceeding
    #   with the collection.
    java-symbol-file-refresh-defer-interval: 60s

    # On-cpu profile configuration
    on-cpu-profile:
      # eBPF on-cpu Profile Switch
      # Default: false
      disabled: false

      # Sampling frequency
      # Default: 99
      frequency: 99

      # Whether to obtain the value of CPUID and decide whether to participate in aggregation.
      # Set to 1:
      #    Obtain the value of CPUID and will be included in the aggregation of stack trace data.
      # Set to 0:
      #    It will not be included in the aggregation. Any other value is considered invalid,
      #    the CPU value for stack trace data reporting is a special value (CPU_INVALID:0xfff)
      #    used to indicate that it is an invalid value.
      # Default: 0
      cpu: 0

      # Sampling process name
      # Default: ^deepflow-.*
      regex: ^deepflow-.*

    # Off-cpu profile configuration, Enterprise Edition Only.
    off-cpu-profile:
      # eBPF off-cpu Profile Switch
      # Default: false
      disabled: false

      # Off-cpu trace process name
      # Default: ^deepflow-.*
      regex: ^deepflow-.*

      # Whether to obtain the value of CPUID and decide whether to participate in aggregation.
      # Set to 1:
      #    Obtain the value of CPUID and will be included in the aggregation of stack trace data.
      # Set to 0:
      #    It will not be included in the aggregation. Any other value is considered invalid,
      #    the CPU value for stack trace data reporting is a special value (CPU_INVALID:0xfff)
      #    used to indicate that it is an invalid value.
      # Default: 0
      cpu: 0

      # Configure the minimum blocking event time
      # Default: 50us. Range: [0, 3600000000]us
      # Note:
      #   If set to 0, there will be no minimum value limitation.
      #   Scheduler events are still high-frequency events, as their rate may exceed 1 million events
      #   per second, so caution should still be exercised.
      #   If overhead remains an issue, you can configure the 'minblock' tunable parameter here.
      #   If the off-CPU time is less than the value configured in this item, the data will be discarded.
      #   If your goal is to trace longer blocking events, increasing this parameter can filter out shorter
      #   blocking events, further reducing overhead. Additionally, we will not collect events with a block
      #   time exceeding 1 hour.
      minblock: 50us

    # Memory profile configuration, Enterprise Edition Only.
    memory-profile:
      # eBPF memory Profile Switch
      # Default: true
      disabled: true

      # Memory trace process name
      # Default: ^java
      regex: ^java

      # Memory profile report interval
      # Default: 10s
      # Note: Memory profiler will aggregate profile data by window and report according to this setting.
      report-interval: 10s

    preprocess:
      # Stack Compression Switch
      # Default: true
      stack-compression: true

    # eBPF OOOR (Out-Of-Order-Reassembly) Cache Size
    # Default: 16. Range: [8, 1024]
    # Note: When `syscall-out-of-order-reassembly` is enabled, up to `syscall-out-of-order-cache-size` eBPF
    #   socket events (each event consuming up to `l7_log_packet_size` bytes) will be cached in each TCP/UDP
    #   flow to prevent out-of-order events from impacting application protocol parsing. Since eBPF socket
    #   events are sent to user space in batches, out-of-order scenarios mainly occur when requests and responses
    #   within a single session are processed by different CPUs, causing the response to reach user space before the request.
    syscall-out-of-order-cache-size: 16

    # eBPF OOOR (Out-Of-Order-Reassembly) Enabled Application Protocols
    # Note: When this capability is enabled for a specific application protocol, the agent will add out-of-order-reassembly
    #   processing for it. Note that the agent will consume more memory in this case, so please adjust the
    #   syscall-out-of-order-cache-size accordingly and monitor the agent's memory usage.
    # Supported Protocols:
    #   - HTTP
    #   - HTTP2 ## for both HTTP2 and gRPC
    #   - Dubbo
    #   - SofaRPC
    #   - FastCGI
    #   - bRPC
    #   - Tars
    #   - SomeIP
    #   - MySQL
    #   - PostgreSQL
    #   - Oracle
    #   - Redis
    #   - MongoDB
    #   - Memcached
    #   - Kafka
    #   - MQTT
    #   - AMQP
    #   - OpenWire
    #   - NATS
    #   - RocketMQ
    #   - DNS
    #   - TLS
    #   - Custom ## custom protocol from plugin
    syscall-out-of-order-reassembly: []

    # eBPF SR (Segmentation-Reassembly) Enabled Application Protocols
    # Note: When this capability is enabled for a specific application protocol, the agent will add
    #   segmentation-reassembly processing to merge application protocol content spread across multiple
    #   syscalls before parsing it. This enhances the success rate of application protocol parsing.
    #   Note that `syscall-out-of-order-reassembly` must also be enabled for this feature to be effective.
    # Supported Protocols:
    #   - HTTP
    #   - HTTP2 ## for both HTTP2 and gRPC
    #   - Dubbo
    #   - SofaRPC
    #   - FastCGI
    #   - bRPC
    #   - Tars
    #   - SomeIP
    #   - MySQL
    #   - PostgreSQL
    #   - Oracle
    #   - Redis
    #   - MongoDB
    #   - Memcached
    #   - Kafka
    #   - MQTT
    #   - AMQP
    #   - OpenWire
    #   - NATS
    #   - RocketMQ
    #   - DNS
    #   - TLS
    #   - Custom ## custom protocol from plugin
    syscall-segmentation-reassembly: []

    # Syscall TraceID Disabled
    # Note: When the trace_id is injected into all requests, the computation logic for all
    #   syscall_trace_id can be turned off. This will significantly reduce the impact of the
    #   eBPF hook on the CPU consumption of the application process.
    syscall-trace-id-disabled: false

    # Disable Pre-allocating Memory
    # Default: false
    # Note:
    #   When full map preallocation is too expensive, setting 'map-prealloc-disabled' to true will
    #   prevent memory pre-allocation during map definition, but it may result in some performance
    #   degradation. This configuration only applies to maps of type 'BPF_MAP_TYPE_HASH'.
    #   Currently applicable to socket trace and uprobe Golang/OpenSSL trace functionalities.
    #   Disabling memory preallocation will reduce memory usage by approximately 45MB.
    map-prealloc-disabled: false

  ######################################
  ## Agent Running in Standalone Mode ##
  ######################################
  # Data File Size
  # Default: 200. Unit: MB.
  # Note: When deepflow-agent runs in standalone mode, it will not be controlled by
  #   deepflow-server, and the collected data will only be written to the local file.
  #   Currently supported data types for writing are l4_flow_log and l7_flow_log. Each
  #   type of data is written to a separate file. This configuration can be used to
  #   specify the maximum size of the data file, and rotate when it exceeds this size.
  #   A maximum of two files are kept for each type of data.
  standalone-data-file-size: 200

  # Directory of Data File
  # Note: Directory where data files are written to.
  standalone-data-file-dir: /var/log/deepflow-agent/

  # Log File Path
  # Note: Note that this configuration is only used in standalone mode.
  log-file: /var/log/deepflow-agent/deepflow-agent.log

  #####################
  ## Proc Monitoring ##
  #####################
  # whether to sync os socket and proc info
  # Default: false
  # Note: only make sense when agent type is one of CHOST_VM, CHOST_BM, K8S_VM, K8S_BM
  os-proc-sync-enabled: false

  # To sync tagged processed only
  # Default: false
  os-proc-sync-tagged-only: false

  # The proc fs mount path
  # Default: /proc
  os-proc-root: /proc

  # Socket scan and sync interval
  # Default: 10
  # Note: Note that the value unit is second.
  os-proc-socket-sync-interval: 10

  # Socket and Process uptime threshold
  # Default: 3
  # Note: Note that the value unit is second.
  os-proc-socket-min-lifetime: 3

  # The command execute and read the yaml from stdout
  # Default: []
  # Note: Execute the command every time when scan the process, expect get the process tag from stdout in yaml format,
  # the example yaml format as follow:
  #  - pid: 1
  #    tags:
  #    - key: xxx
  #      value: xxx
  #  - pid: 2
  #    tags:
  #    - key: xxx
  #      value: xxx
  os-app-tag-exec: ["cat", "/tmp/tag.yaml"]

  # The user who should execute the `os-app-tag-exec` command
  # Default: deepflow
  os-app-tag-exec-user: deepflow

  # the regular expression use for match process and replace the process name
  # Note: will traverse over the entire array, so the previous ones will be matched first.
  #   when match-type is parent_process_name, will recursive to match parent proc name, and rewrite-name field will ignore.
  #   rewrite-name can replace by regexp capture group and windows style environment variable, for example:
  #   `$1-py-script-%HOSTNAME%` will replace regexp capture group 1 and HOSTNAME env var.
  #   if proc not match any regexp will be accepted (essentially will auto append '- match-regex: .*' at the end).
  #
  # Example:
  #   os-proc-regex:
  #     - match-regex: python3 (.*)\.py
  #       match-type: cmdline
  #       action: accept
  #       rewrite-name: $1-py-script
  #     - match-regex: (?P<PROC_NAME>nginx)
  #       match-type: process_name
  #       rewrite-name: ${PROC_NAME}-%HOSTNAME%
  #     - match-regex: "nginx"
  #       match-type: parent_process_name
  #       action: accept
  #     - match-regex: .*sleep.*
  #       match-type: process_name
  #       action: drop
  #     - match-regex: .+ # match after concatenating a tag key and value pair using colon, i.e., an regex `app:.+` can match all processes has a `app` tag
  #       match-type: tag
  #       action: accept
  os-proc-regex:

    # The regexp use for match the process
    # Default: .*
    - match-regex:

      # Regexp match field
      # Default: process_name
      # Note: Options: process_name, cmdline, parent_process_name.
      match-type:

      # Action when RegExp match
      # Default: accept
      # Note: Options: accept, drop.
      action:

      # The name will replace the process name or cmd use regexp replace
      # Default: ""
      # Note: null string will not replace.
      rewrite-name:

  ####################
  ## Guard Interval ##
  ####################
  # Time interval for the agent to detect its own state.
  # Default: 10s. Unit: s. Range: [1s, 3600s].
  # Note: The agent will detect:
  #   1. System free memory;
  #   2. Get the number of threads of the agent itself by reading the file information under the /proc directory;
  #   3. Size and number of log files generated by the agent.
  #   4. System load
  guard-interval: 10s

  ######################
  ## Max Socket Count ##
  ######################
  ## The maximum number of sockets that the agent can open.
  ## Default: 1024. Unit: count. Range: [16, 4096].
  ## Note:
  ##   Agent will restart if socket count exceeds this value.
  #max-sockets: 1024

  ####################################
  ## Max Socket Tolerate Interval   ##
  ####################################
  ## The interval to tolerate socket count exceeding max-sockets before restarting.
  ## Default: 60s. Unit: s. Range: [0, 3600s].
  ## Note:
  ##   Agent will only restart if socket count exceeds max-sockets for this duration.
  ##   Restarts are triggered by guard module, so setting this value lower than guard-interval
  ##   will cause agent to restart immediately.
  #max-sockets-tolerate-interval: 60s

  ####################################
  ## Page Cache Reclaim Percentage  ##
  ####################################
  ## The percentage threshold of page cache to cgroup memory limit that triggers reclaim.
  ## Default: 100. Unit: percentage. Range: [0, 100].
  ## Note:
  ##   A page cache reclaim is triggered when the percentage of page cache and
  ##   cgroups memory.limit_in_bytes exceeds this value.
  ##   Both anonymous memory and file page cache are accounted for in cgroup's memory usage.
  ##   Under some circumstances, page cache alone can cause cgroup to OOM kill agent process.
  ##   To avoid this, agent can reclaim page cache periodically. Although reclaming may not
  ##   cause performance issues for agent who doesn't have much I/O, other processes in
  ##   the same cgroup may be affected. Very low values are not recommended.
  ##   Note:
  ##     - This feature is available for cgroups v1 only.
  ##     - This feature is disabled if agent memory cgroup path is "/".
  ##     - The minimal interval of reclaims is 1 minute.
  #page-cache-reclaim-percentage: 100

  #####################
  ## Check core file ##
  #####################
  # Note:
  #   When the host has an invalid NFS file system or a docker is running, Sometime program hang when checking the
  #   core file, so the core file check provides a switch to prevent the process hang.
  #   Additional links:
  #     https://serverfault.com/questions/367438/ls-hangs-for-a-certain-directory
  #     https://unix.stackexchange.com/questions/495854/processes-hanging-when-trying-to-access-a-file
  check-core-file-disabled: false

  ##################
  ## CPU Affinity ##
  ##################
  # Note:
  #   CPU affinity is the tendency of a process to run on a given CPU for as long as possible without being migrated
  #   to other processors
  # Example:
  #   cpu-affinity: 1,3,5,7,9,11,13,15
  cpu-affinity:

  #################################
  ## Process scheduling priority ##
  #################################
  # Note:
  #   The smaller the value of process scheduling priority, the higher the priority of the process,
  #   and the larger the priority, the lower the priority
  #   Default: 0. Range: [-20, 19].
  # Example:
  #   process-scheduling-priority: -15
  process-scheduling-priority: 0

  ######################
  ## External Signals ##
  ######################
  # Note:
  #   When it is false, it supports the integration of java-async-profiler, golang-pprof and other profile data by instrumentation
  external-profile-integration-disabled: false

  # Note:
  #   When it is false, it supports the integration of OpenTelemetry, SkyWalking and other tracking data in accordance with the OTLP protocol
  external-trace-integration-disabled: false

  # Note:
  #   When it is false, it supports the integration of metrics data of Prometheus, InfluxDB and other protocols
  external-metric-integration-disabled: false

  # Note:
  #   When it is false, it supports the integration of log data from Vector
  external-log-integration-disabled: false

  #######################
  ## NTP Configuration ##
  #######################
  # Note:
  #   When the timestamp fallback exceeds this value, the agent will restart.
  # Default: 300s
  ntp-max-interval: 300s

  # Note:
  #   When the time difference exceeds this value, the timestamp will be corrected.
  # Default: 10s
  ntp-min-interval: 10s

  #################
  ## FeatureFlag ##
  #################
  # Note: Unreleased deepflow-agent features can be turned on by setting this switch.
  feature-flags:

