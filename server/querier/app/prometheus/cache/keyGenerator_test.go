/*
 * Copyright (c) 2024 Yunshan Networks
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cache

import (
	"testing"

	"github.com/prometheus/prometheus/model/labels"
)

func TestGenerateMatcherKey(t *testing.T) {
	matchers := []*labels.Matcher{
		{Name: "name1", Type: labels.MatchEqual, Value: "value1"},
		{Name: "name2", Type: labels.MatchNotEqual, Value: "value2"},
	}

	expected := "name1=value1,name2!=value2"
	result := generateMatcherKey(matchers, ",")

	if result != expected {
		t.Errorf("generateMatcherKey() = %s, want %s", result, expected)
	}
}
