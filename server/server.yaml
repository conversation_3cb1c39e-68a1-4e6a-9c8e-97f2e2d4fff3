# logfile path
log-file: /var/log/deepflow/server.log
# loglevel: "debug/info/warn/error"
log-level: info

## open pprof serves via HTTP server port 9526. ref: https://pkg.go.dev/net/http/pprof
#profiler: false

## maximum usage of cpu cores, 0 means no limit
#max-cpus: 0

#continuous-profile:
#  enabled: false
#  server-addr: http://deepflow-agent/api/v1/profile
#  # support profile types: "cpu", "inuse_objects", "alloc_objects", "inuse_space", "alloc_space", "goroutines", "mutex_count", "mutex_duration", "block_count", "block_duration"
#  profile-types: ["cpu", "inuse_objects", "alloc_objects", "inuse_space", "alloc_space"]
#  mutex-rate: 5 # valid when ProfileTypes contains 'mutex_count' or 'mutex_duration'
#  block-rate: 5 # valid when ProfileTypes contains 'block_count' or 'block_duration'
#  log-enabled: true # whether record profile debug logs

## every `interval`, call debug.FreeOSMemory()(https://pkg.go.dev/runtime/debug#FreeOSMemory) to release OS memory.
#free-os-memory-manager:
#  enabled: false
#  interval: 3600  # uint: second

## extract an integer (generally used timestamp) from traceId as an additional index to speed up traceId queries.
#trace-id-with-index:
#  disabled: false
#  type: hash           # hash/incremental-id, default: hash
#  #eg. traceId: 'abcdef1234',  if want to match index '1234', 'format' must set 'decimal', `start` can be set as 6 or -4 , and `length` must set: 4
#  incremental-id-location:      # it is valid when 'type' is 'incremental-id'
#    start: 0           # >= 0 means counting from the beginning, < 0 means counting from the end.
#    length: 13         # when 'format' is 'decimal' 'length' range is (0, 20], 'format' is 'hex' 'length' range is (0, 16].
#    format: decimal    # hex/decimal

## monitor the disk usage of the paths
#monitor-paths: [/,/mnt,/var/log]

controller:
  ## controller http listenport
  #listen-port: 20417
  #listen-node-port: 30417
  ## grpc server port
  #grpc-port: 20035
  #ssl-grpc-port: 20135
  #agent_ssl_cert_file: /etc/ssl/server.key
  #agent_ssl_key_file: /etc/ssl/server.pem
  #ingester-port: 20033
  #grpc-node-port: 30035
  # grpc max message lenth default 100M
  grpc-max-message-length: 104857600
  # kubeconfig
  kubeconfig:
  # election
  election-name: deepflow-server
  # Once every 24 hours DeepFlow will report usage data to usage.deepflow.yunshan.net
  # The data includes a random ID, version, number of deepflow server and agent.
  # No data from user databases is ever transmitted.
  # Change this option to true to disable reporting.
  reporting-disabled: false
  # Deepflow billing mode  license/voucher
  billing-method: license
  ## If no-teamid-refused is set to true, agent must report team_id to server
  #no-teamid-refused: false
  ## If all-agent-connect-to-nat-ip is set to true, All agents connect to the server via nat-ip 
  #all-agent-connect-to-nat-ip: false
  ## If no-ip-overlapping is set to true, All IP and cidr issued by the controller to agent are wan
  #no-ip-overlapping: false
  ## exec agent command timeout
  # agent-cmd-timeout: 30

  # ingester plaform data, default: 0
  # 0 (All K8s Cluster)
  # 1 (K8s Cluster in local Region)
  # 2 (K8s Cluster in local AZs)
  pod-cluster-internal-ip-to-ingester: 0

  swagger:
    enabled: true

  http:
    # resource api redis cache refresh interval, unit:s
    redis_refresh_interval: 3600
    # whether enable redis cache when get resource api via page
    resource_api_page_get_redis_enabled: false
    # additional domains
    additional_domains:

  # deepflow web service config
  df-web-service:
    enabled: false
    host: df-web
    port: 20825
    timeout: 30

  # deepflow fpermit service config
  fpermit:
    enabled: false
    host: fpermit
    port: 20823
    timeout: 30

  # mysql相关配置
  mysql:
    enabled: true
    database: deepflow
    user-name: root
    user-password: deepflow
    host: mysql
    port: 30130
    proxy-host:
    proxy-port:
    timeout: 30
    
    auto_increment_increment: 1
    
    # whether drop database when init failed
    drop-database-enabled: false
    max_open_conns: 100
    max_idle_conns: 50
    # unit: minute
    conn_max_life_time: 60
    # limit the size of batch operation for small tables like ch_process
    batch-size-0: 100000
    # limit the size of batch operation for large tables like pod
    batch-size-1: 2500

  # postgresql相关配置
  postgresql:
    enabled: false
    database: deepflow
    schema: public
    user-name: postgres
    user-password: deepflow
    host: postgresql
    port: 5432
    timeout: 30
    
    # whether drop meta database when init failed
    drop-database-enabled: false
    max-open-conns: 100
    max-idle-conns: 50
    # unit: minute
    conn-max-life-time: 60
    # limit the size of batch operation for small tables like ch_process
    batch-size-0: 100000
    # limit the size of batch operation for large tables like pod
    batch-size-1: 2500

  # redis相关配置
  redis:
    enabled: true
    cluster_enabled: false
    resource_api_database: 1
    resource_api_expire_interval: 3600
    dimension_resource_database: 2
    password: deepflow
    host:
      - redis
    port: 6379
    timeout: 30

  # clickhouse相关配置
  clickhouse:
    database: flow_tag
    user-name: default
    host: clickhouse
    port: 9000
    # user-password:
    endpoint-tcp-port-name: tcp-port

  # datasource-api from ingester
  ingester-api:
    port: 20106
    node-port: 30106
    timeout: 60

  # 规格相关定义
  spec:
    vtap_group_max: 1000
    vtap_max_per_group: 10000
    az_max_per_server: 10
    data_source_max: 25
    data_source_retention_time_max: 24000
    # unit: s
    data_source_ext_metrics_interval: 10
    # unit: s
    data_source_prometheus_interval: 10

  # monitor module config
  monitor:
    # controller/analyzer health_check interval, unit:s
    health_check_interval: 60
    # 健康检查异常/控制器切换处理channel的长度
    health_check_handle_channel_len: 1000
    # License检查的时间间隔，单位: 秒
    license_check_interval: 60
    # vtap检查的时间间隔，单位: 秒
    vtap_check_interval: 60
    # exception_time_frame, unit:s
    exception_time_frame: 3600
    # vtap rebalance config, interval uint:s
    auto_rebalance_vtap: true
    rebalance_check_interval: 300
    ingester-load-balancing-strategy:
      # options: by-ingested-data, by-agent-count
      algorithm: by-ingested-data 
      # use the data in data-duration as the basis for balancing, default: 1d, uint: s
      data-duration: 86400
      # rebalance vtap interval, default: 1h, uint: s
      rebalance-interval: 3600
    ## automatically delete lost vtaps
    # vtap_auto_delete:
    #   enabled: true
    #   # if current time - vtap lost time >= lost_time_max, vtap will be deleted
    #   # uint: s
    #   lost_time_max: 3600
    # warrant
    warrant:
      enabled: false
      host: warrant
      port: 20413
      timeout: 30

  # manager module config
  manager:
    # 云平台增加/删除/配置变化检测的时间间隔，单位：秒
    cloud_config_check_interval: 60
    task:
      # recorder更新数据库的时间间隔，单位：秒
      resource_recorder_interval: 60
      cloud:
        # Kubernetes数据获取的时间间隔，单位：秒
        kubernetes_gather_interval: 30
        # 阿里公有云API获取区域列表时，需要指定一个区域
        aliyun_region_name: cn-beijing
        # AWS API获取区域列表时，需要指定一个区域，并通过这个区域区分国际版和国内版
        aws_region_name: cn-north-1
        # 配置宿主机IP文件
        hostname_to_ip_file: /etc/hostname_to_ip.csv
        # 开启debug模式，支持debug云平台API/SDK原始数据
        debug_enabled: false
        # 云平台同步api调用超时时间，单位：秒
        http_timeout: 30
        # custom tag value 最大长度限制
        custom_tag_len_max: 256
        # process name 最大长度限制
        process_name_len_max: 256
        # 青云配置，对公有云和私有云同时生效
        qingcloud_config:
          # 对接青云 api 错误最大重试次数
          max_retries: 4
          # 对接青云 api 错误重试间隔，单位：秒
          retry_duration: 60
          # 对接青云定时执行，根据配置的时间点每日执行一次，配置后循环执行失效，格式：%H:%M ，例：05:30 ，每日5点30分执行一次对接
          daily_trigger_time: ""
          # 关闭 lb 监听器及后端主机对接
          disable_sync_lb_listener: false
        # fc配置
        fusioncompute_config:
          # 对接 fusioncompute 定时执行，根据配置的时间点每日执行一次，配置后循环执行失效，格式：%H:%M ，例：05:30 ，每日5点30分执行一次对接
          daily_trigger_time: ""
      recorder:
        # recorder模块缓存自愈刷新时间间隔，单位：分钟，默认：60 * 24
        cache_refresh_interval: 1440
        # 软删除资源数据清理时间间隔，单位：小时
        # 此值应小于 soft_deleted_resource_expire_interval
        deleted_resource_clean_interval: 24
        # 软删除资源数据保留时间，单位：小时，默认：7 * 24
        deleted_resource_retention_time: 168
        # 脏数据清理时间间隔，单位：分钟，默认：1500
        dirty_resource_clean_interval: 1500
        # 资源ID限制：区域、可用区、宿主机、VPC、网络、容器集群、命名空间
        resource_max_id_0: 64000
        # 资源ID限制：所有设备ID（除宿主机外）、容器节点、Ingress、工作负载、ReplicaSet、POD
        resource_max_id_1: 499999
        # local debug log
        log_debug: 
          enabled: false
          detail_enabled: false
          resource_type:
          #  - all
          #  - vpc
        mysql_batch_size: 2500
        event:
          # context lines count for config diff
          config_diff_context: 3
  tagrecorder:
    # size of data in batch operation for MySQL
    mysql_batch_size: 1000
    live_view_refresh_second: 60
    # unit s
    dictionary_reload_interval: 3600

  trisolaris:
    tsdb_ip:
    chrony:
      host: 127.0.0.1
      port: 123
      timeout: 1

    trident-type-for-unknow-vtap: 0

    platform-vips:
    #  - ************

    # master/slave 区域标识, 默认为主区域，部署时会自动修改
    node-type: master

    # 区域服务域名前缀
    region-domain-prefix: ""

    # 采集器是否自动注册
    vtap-auto-register: True

    # agent upgrade image cache time，unit: second
    image_expire: 300

    default-tap-mode:

    # whether to register domain automatically
    domain-auto-register: True

    # clean up the data and cache of the table kubernetes_cluster according to the data 
    # that was not synchronized before a certain period of time 
    clear-kubernetes-time: 600

    ## plaform data real refresh delay time
    ## uint: s, default: 1
    #platform-data-refresh-delay-time: 1

    ## org data refresh interval
    ## uint: s, default: 60
    #org-data-refresh-interval: 60

    ## tmp config: whether to log agent cpu/memory
    log-agent-config: False

  genesis:
    # 平台数据老化时间，单位：秒
    aging_time: 86400
    # 采集器接口数据老化时间，单位：秒
    vinterface_aging_time: 300
    # 无子网IP的最大掩码长度
    ipv4_cidr_max_mask: 24
    ipv6_cidr_max_mask: 64

    # 采集器同步默认vpc名称
    default_vpc_name: default-public-vpc

    # 采集器上报数据处理的队列长度
    queue_length: 1000

    # 数据持久化检测间隔，单位：秒
    data_persistence_interval: 60
    # 采集器消息心跳时长，单位：秒
    agent_heart_beat: 60

    # 采集器同步KVM时，配置的采集器IP所上报的内容会被解析
    host_ips:
    # - x.x.x.x
    # - x.x.x.x/x
    # - x.x.x.x-x.x.x.x

    # 内网IP网段范围配置，配置的IP所在网络会被作为内网进行学习
    local_ip_ranges:
    # - x.x.x.x/x
    # - x.x.x.x-x.x.x.x

    # 排除IP范围
    exclude_ip_ranges: 
    # - x.x.x.x/x
    # - x.x.x.x-x.x.x.x

    # 数据存储选择，默认 mysql，可选 redis
    database: mysql
    # 多namespace模式开关，默认false
    multi_ns_mode:
    # 单独vpc模式开关，默认false
    single_vpc_mode:
    # 忽略网卡正则表达式配置，匹配到会忽略该网卡，默认为 ^(kube-ipvs) ，增加其他的网卡名称需要在此基础上新增
    ignore_nic_regex:

    ## 采集器同步 KVM ，解析虚拟机的 XML 时，默认为 metadata 即使用 domian.metadata.instance.name 字段作为虚拟机的名称
    ## 可通过此配置将虚拟机名称指定为 domain.name、domain.title、domain.uuid 之一
    ## 当使用此配置指定的字段取不到虚拟机名称或虚拟机名称为空时，则将使用 domain.name 的值作为虚拟机名称
    ## 例：vm_name_field: title ，指定 domain.title 作为虚拟机的名称
    #vm_name_field: metadata

  prometheus:
    # synchronizer cache refresh interval, unit: second
    synchronizer_cache_refresh_interval: 60
    # encoder cache refresh interval, unit: second
    encoder_cache_refresh_interval: 3600
    # time interval for regularly clearing prometheus expired data, unit: minute, default: 60 * 24
    # time interval should be greater than or equal to ingester: prometheus-label-cache-expiration configuration
    data_clean_interval: 1440
    # limit the total number of querier queried at a time
    querier_query_limit: 1000000

querier:
  # querier http listenport
  listen-port: 20416
  language: en

  # clickhouse相关配置
  clickhouse:
    database: flow_tag
    user-name: default
    host: clickhouse
    port: 9000
    timeout: 60
    max-connection: 20
    # user-password:
    use-query-cache: true
    # unit: s
    query-cache-ttl: 600

  # profile相关配置
  profile:
    flame_query_limit: 1000000
  
  # trace-map 相关配置
  trace-map:
    # 每次迭代查询上限
    max_trace_per_iteration: 100000
    # 每次流返回的上限
    batch_traces_count_max: 1000
    # 迭代的次数，会自动向上取整为2的次方数
    trace_id_query_iterations: 8
    # 查询额外扩大的查询范围，单位：秒
    trace_query_delta: 300
    # Trace Tree 写入间隔，单位：秒
    write_interval: 60
    # Trace Tree 批次写入大小
    write_batch_size: 1000
    debug_sql_len_max: 1000

  # deepflow-app相关配置
  deepflow-app:
    host: deepflow-app
    port: 20418

  otel-endpoint: http://deepflow-agent/api/v1/otel/trace
  limit: 10000
  time-fill-limit: 20

  prometheus:
    limit: 1000000
    qps-limit: 100 # setting to 0 means no limit
    series-limit: 500
    max-samples: 50000000
    auto-tagging-prefix: df_
    request-query-with-debug: true
    external-tag-cache-size: 1024
    external-tag-load-interval: 300
    thanos-replica-labels: [] # remove duplicate replica labels when query data
    cache:
      remote-read-cache: true
      response-cache: false
      cache-item-size: 512000 # max size of cache item, unit: byte
      cache-max-count: 1024 # max capacity of cache list
      cache-first-timeout: 10 # time out for first cache item load, uint: s
      cache-clean-interval: 3600 # clean interval for cache, unit: s
      cache-allow-time-gap: 1 # when query end - cache end < gap, not update cache, unit: s

  auto-custom-tag:
    tag-name: 
    tag-values: 
  # external-apm:
  # - name: skywalking
  #   addr: 127.0.0.1:12800

mcp:
  listen-port: 20080

ingester:
  ## whether Ingester store metrics/flow_log... to database
  #storage-disabled: false

  #ckdb:
  #  # use internal or external ckdb
  #  external: false
  #  # database type: suport 'clickhouse', 'byconity', default is 'clickhouse'
  #  type: clickhouse
  #  # if type is 'byconity', default host is 'deepflow-byconity-server', else 'deepflow-clickhouse'
  #  host: deepflow-clickhouse
  #  port: 9000
  #  # for get clickchouse endpoints tcp port value
  #  endpoint-tcp-port-name: tcp-port
  #  # if `external` is 'true', default value is 'default', else 'df_cluster'
  #  # if type is 'byconity', this setting is invalid
  #  cluster-name:
  #  # if `external` is 'true', default value is 'default', else 'df_storage'
  #  # if type is 'byconity', default value is 'cnch_default_s3'
  #  storage-policy:
  #  # A list of supported time zones can be found in https://www.iana.org/time-zones and also can be queried by SELECT * FROM system.time_zones
  #  # if type is 'byconity', this setting is ignored
  #  time-zone: Asia/Shanghai

  ## This configuration is only valid when the ClickHouse tables have not yet been created. If the tables have been created, you need to delete the databases/tables and restart to create the tables according to the configuration
  #ckdb-cold-storage:
  #  enabled: false
  #  cold-disk: # have configured in clickhouse
  #    type: volume  # 'volume' or 'disk'
  #    name: xxx
  #  settings:
  #  - db: flow_log
  #    # if 'tables' is empty, will set all tables under the DB
  #    tables:
  #    - l4_flow_log
  #    - l7_flow_log
  #    # uint: hour, move data to cold disk after 'ttl-hour-to-move'
  #    ttl-hour-to-move: 24
  #  - db: flow_metrics
  #    tables:
  #    - vtap_flow_port.1m
  #    - vtap_flow_edge_port.1m
  #    ttl-hour-to-move: 168

  #ckdb-auth:
  #  username: default
  #  password:

  # local node ip, if not set will get from environment variable 'NODE_IP', dafault: ""
  #node-ip:

  ## trisolaris的ips, 默认值为空
  #controller-ips:
  #  - x.x.x.x

  ## controller listening port
  #controller-port: 20035

  ## stats collect interval(unit: s)
  # stats-interval: 10

  ## The listening port used by Ingester to receive data
  #listen-port: 20033
  #
  ## The listening port used by Ingester to handle datasource API
  #datasource-listen-port: 20106

  ## 遥测数据写入配置
  #metrics-ck-writer:
  #  queue-count: 1      # 每个表并行写数量
  #  queue-size: 256000  # 数据队列长度
  #  batch-size: 128000  # 多少行数据同时写入
  #  flush-timeout: 10   # 超时写入时间

  ## 流日志写入配置
  #flowlog-ck-writer:
  #  queue-count: 1      # 每个表并行写数量
  #  queue-size: 256000  # 数据队列长度
  #  batch-size: 128000  # 多少行数据同时写入
  #  flush-timeout: 10   # 超时写入时间

  ## ext metrics写入配置
  #ext-metrics-ck-writer:
  #  queue-count: 1      # 每个表并行写数量
  #  queue-size: 100000  # 数据队列长度
  #  batch-size: 51200   # 多少行数据同时写入
  #  flush-timeout: 10   # 超时写入时间

  ## ext_metrics database data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #ext-metrics-ttl-hour: 168

  ## flow_metrics database data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #flow-metrics-ttl-hour:
  #  vtap-flow-1m: 168     # vtap_flow[_edge]_port.1m
  #  vtap-flow-1s: 24     # vtap_flow[_edge]_port.1s
  #  vtap-app-1m: 168      # vtap_app[_edge]_port.1m
  #  vtap-app-1s: 24      # vtap_app[_edge]_port.1s

  ## flow_log database data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #flow-log-ttl-hour:
  #  l4-flow-log: 72
  #  l7-flow-log: 72
  #  l4-packet: 72

  ## whether to store trace tree information
  #flow-log-trace-tree-enabled: false

  ## resource event data write config
  #event-ck-writer:
  #  queue-count: 1      # 每个表并行写数量
  #  queue-size: 50000  # 数据队列长度
  #  batch-size: 25600   # 多少行数据同时写入
  #  flush-timeout: 5   # 超时写入时间

  ## k8s event data write config
  #k8s-event-ck-writer:
  #  queue-count: 1      # parallelism of table writing
  #  queue-size: 50000  # size of writing queue
  #  batch-size: 25600   # size of batch writing
  #  flush-timeout: 5    # timeout of table writing

  ## resource/k8s event table data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #event-ttl-hour: 720

  ## alert event table data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #alert-event-ttl-hour: 720

  ## file event data write config
  #file-event-ck-writer:
  #  queue-count: 1      # 每个表并行写数量
  #  queue-size: 50000  # 数据队列长度
  #  batch-size: 25600   # 多少行数据同时写入
  #  flush-timeout: 5    # 超时写入时间

  ## file event table data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #file-event-ttl-hour: 168

  ## pcap data write config
  #pcap-ck-writer:
  #  queue-count: 1     # 每个表并行写数量
  #  queue-size: 50000  # 数据队列长度
  #  batch-size: 2048   # 多少行数据同时写入
  #  flush-timeout: 5   # 超时写入时间

  ## pcap database data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #pcap-ttl-hour: 72

  ## pcap decoder queue count/size
  #pcap-queue-count: 2
  #pcap-queue-size: 4096

  ## profile process data write config
  #profile-ck-writer:
  #  queue-count: 2      # parallelism of table writing
  #  queue-size: 100000  # size of writing queue
  #  batch-size: 51200   # size of batch writing
  #  flush-timeout: 5    # timeout of table writing

  ## profile process database data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #profile-ttl-hour: 72

  ## profile compression algorithm, default is zstd, empty string for not compress
  #profile-compression-algorithm: "zstd"

  ## off-cpu pofile splitting granularity, 0 mean disable splitting (unit: second)
  #profile-off-cpu-splitting-granularity: 1

  ## 默认读超时，修改数据保留时长时使用
  #ck-read-timeout: 300

  ## prometheus data writer config
  #prometheus-ck-writer:
  #  queue-count: 1      # parallelism of table writing
  #  queue-size: 524288  # size of writing queue
  #  batch-size: 262144  # size of batch writing
  #  flush-timeout: 10   # timeout of table writing

  ## prometheus decoder queue count/size
  #prometheus-decoder-queue-count: 1
  #prometheus-decoder-queue-size: 4096

  ## prometheus database data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #prometheus-ttl-hour: 168

  ## prometheus label request/response msg max size (unit: bytes)
  #prometheus-label-msg-max-size: 104857600

  ## when prometheus requests labels, how many metric batch requests
  #prometheus-label-request-metric-batch-count: 128
  #prometheus-app-label-column-increment: 8
  #prometheus-app-label-column-min-count: 8

  ## Whether to ignore the writing of Universal Tag, the default is false, which means writing
  #prometheus-sample-ignore-universal-tag: false

  ## prometheus cache expiration of label ids. uint: s
  #prometheus-label-cache-expiration: 86400

  ## application log data writer config
  #application-log-ck-writer:
  #  queue-count: 2      # parallelism of table writing
  #  queue-size: 25600   # size of writing queue
  #  batch-size: 12800   # size of batch writing
  #  flush-timeout: 5   # timeout of table writing

  ## app log decoder queue count/size
  #application-log-decoder-queue-count: 2
  #application-log-decoder-queue-size: 4096

  ## app log database data retention time(unit: hour)
  ## Note: This configuration is only valid when DeepFlow is run for the first time or the ClickHouse tables have not yet been created
  #application-log-ttl-hour: 720

  #ck-disk-monitor:
  #  check-interval: 180 # check time interval (unit: seconds)
  #  ttl-check-disabled: false # whether to not check TTL expired data
  ## 磁盘空间不足时，同时满足磁盘占用率>used-percent和磁盘空闲<free-space, 或磁盘占用大于used-space, 开始清理数据
  ## When the disk space is insufficient, the disk occupancy > 'used-percent' and the disk idle < 'free-space' are met at the same time, or the disk occupancy > 'used-space', then the data is cleaned up
  #  disk-cleanups:
  #  - disk-name-prefix: default  # monitor disks starting with 'disk-name-prefix', check the disks 'select * from system.disks'
  #    used-percent: 80         #  disk usage threshold, ranges: 0-100
  #    free-space: 300          #  uint: GB, disk minimum free threshold
  #    used-space: 0            #  uint: GB, disk maximum usage space threshold. (If it is 0, it means ignore the condition)
  #  - disk-name-prefix: path_  # monitor disks starting with 'disk-name-prefix', check the disks 'select * from system.disks'
  #    used-percent: 80         #  disk usage threshold, ranges: 0-100
  #    free-space: 300          #  uint: GB, disk minimum free threshold
  #    used-space: 0            #  uint: GB, disk maximum usage space threshold. (If it is 0, it means ignore the condition)
  #  - disk-name-prefix: server_local_  # only when ckdb type is byconity
  #  - disk-name-prefix: server_s3_disk_ # only when ckdb type is byconity
  #  priority-drops:     # set which database and table data will be deleted first when disk is full
  #  - database: flow_log   # databases under all organizations
  #    tables-contain:      # tables name containing the string will be priority-dropped. If it is empty, it means all the tables under the database
  #  - database: flow_metrics
  #    tables-contain: 1s_local
  #  - database: profile
  #  - database: application_log

  ## ingester模块是否启用，默认启用, 若不启用(表示处于单独的控制器)
  #ingester-enabled: true

  # whether to enable deepflow-aget syslog to write to files in syslog-directory
  #agent-log-to-file: false

  ## udp socket receiver buffer: 64M
  #udp-read-buffer: 67108864

  ## tcp socket receiver buffer: 4M
  #tcp-read-buffer: 4194304

  ## tcp socket reader buffer: 1M
  #tcp-reader-buffer: 1048576

  ## Rpc synchronization recv/send msg buffer(unit: Byte)
  #grpc-buffer-size: 104857600

  ## query platformData service, port filter fastmap LRU capacity(unit: count)
  #service-labber-lru-cap: 4194304

  ## ########################### flow metrics config #############################################

  ## 是否不写入秒级数据: 默认: false(写入)
  #disable-second-write: false

  ## parallelism of unmarshall, defaults to 4
  #unmarshall-queue-count: 4

  ## size of unmarshall queue, defaults to 10240
  #unmarshall-queue-size: 10240

  ## the maximum threshold for processing l4/l7 flow logs per second.(threshold for each flow log). If set to 0, the threshold for processing is not limited
  #throttle: 50000
  ## Sampling bucket count. The larger this value is, the more accurate the sampling current limit is, and the more memory it takes up.
  #throttle-bucket: 3

  ## 分别控制l4流日志, l7流日志，默认值为0，表示使用throttle的设置值.若非0，则使用设置值
  #l4-throttle: 0
  #l7-throttle: 0

  #flow-log-decoder-queue-count: 2
  #flow-log-decoder-queue-size: 4096

  #ext-metrics-decoder-queue-count: 2
  #ext-metrics-decoder-queue-size: 4096

  #profile-decoder-queue-count: 2
  #profile-decoder-queue-size: 4096

  #event-decoder-queue-count: 1
  #event-decoder-queue-size: 4096

  #k8s-event-decoder-queue-count: 1
  #k8s-event-decoder-queue-size: 4096

  #file-event-decoder-queue-count: 2
  #file-event-decoder-queue-size: 4096

  ## unit: byte
  #flow-tag-cache-max-size: 262144

  ## unit: s
  #flow-tag-cache-flush-timeout: 1800

  #exporters:
  #- protocol: kafka
  #  enabled: true
  #  # randomly select an address that can be sent successfully. Kafka address format as: 'broker1.example.com:9092'
  #  endpoints: [broker1.example.com:9092, broker2.example.com:9092]
  #  # the data source that needs to be exported format as $db_name.$table_name, is also the topic name of Kafka
  #  data-sources: # currently only supports 'flow_metrics.*', 'flow_log.l4/l7_flow_log', 'event.file_event'
  #  - flow_log.l7_flow_log
  #  # - flow_log.l4_flow_log
  #  # - flow_metrics.application_map.1s
  #  # - flow_metrics.application_map.1m
  #  # - flow_metrics.application.1s
  #  # - flow_metrics.application.1m
  #  # - flow_metrics.network_map.1s
  #  # - flow_metrics.network_map.1m
  #  # - flow_metrics.network.1s
  #  # - flow_metrics.network.1m
  #  # - event.file_event
  #  # number of queues exported in parallel
  #  queue-count: 4
  #  # size of exporting queue
  #  queue-size: 100000
  #  # the number of items exported in each batch
  #  batch-size: 1024
  #  flush-timeout: 10
  #  # the value of tag is "", whether to export
  #  export-empty-tag: false
  #  # the value of metrics is 0, whether to export
  #  export-empty-metrics-disabled: false
  #  # whether the id value of the enumeration type is not converted into a string for output
  #  enum-translate-to-name-disabled: false
  #  # whether the id value of universal-tag is not converted into a resource name for output
  #  universal-tag-translate-to-name-disabled: false
  #  tag-filters-groups: # 'OR' relationship between all 'tag-filters-groups'
  #  #- tag-filter-condition: # relationship between all 'tag-filters'
  #       # Condition type, default is "and", "and": Logical AND between 'tag-filters' , "or": Logical OR between 'tag-filters'
  #  #    type: and #  Condition type, default is "and"
  #  #  tag-filters:
  #  #  - field-name: region_id_0
  #  #    operator: "="             # can be '=', '!=', 'in', 'not in', ':', '!:', "~", "!~"
  #  #    field-values: [3, 4]         # vlaues
  #  #  - field-name: az_id_0
  #  #    operator: "="             # can be '=', '!=', 'in', 'not in', ':', '!:', "~", "!~"
  #  #    field-values: [3]         # vlaues
  #  #- tag-filter-condition: # relationship between all 'tag-filters'
  #       # Condition type, default is "and", "and": Logical AND between 'tag-filters' , "or": Logical OR between 'tag-filters'
  #  #    type: and
  #  #  tag-filters:
  #  #  - field-name: region_id_1
  #  #    operator: "="             # can be '=', '!=', 'in', 'not in', ':', '!:', "~", "!~"
  #  #    field-values: [3]         # vlaues
  #  #  - field-name: az_id_1
  #  #    operator: "="             # can be '=', '!=', 'in', 'not in', ':', '!:', "~", "!~"
  #  #    field-values: [3]         # vlaues
  #  export-fields: # field_name or $category
  #  - $tag
  #  - $metrics
  #  sasl:
  #    enabled: false # default: false
  #    security-protocol: SASL_SSL  # currently only supports: SASL_SSL
  #    sasl-mechanism: PLAIN # currently only supports: PLAIN
  #    username: aaa
  #    password: bbb
  #  topic:  # If the value is empty, use the value of `deepflow.$data-source` as the kafka topic (eg, `deepflow.flow_log.l7_flow_log`). If it is not empty, use the value as the kafka topic.
  #- protocol: prometheus
  #  enabled: true
  #  # randomly select an address that can be sent successfully, prometheus address format as: http://127.0.0.1:9091/receive
  #  endpoints: [http://127.0.0.1:9091/receive, http://*******:9091/receive]
  #  data-sources: # currently only supports 'flow_metrics.*'
  #  - flow_metrics.application_map.1s
  #  # - flow_metrics.application_map.1m
  #  # - flow_metrics.application.1s
  #  # - flow_metrics.application.1m
  #  # - flow_metrics.network_map.1s
  #  # - flow_metrics.network_map.1m
  #  # - flow_metrics.network.1s
  #  # - flow_metrics.network.1m
  #  queue-count: 4
  #  queue-size: 100000
  #  batch-size: 1024
  #  flush-timeout: 10
  #  tag-filters-groups: # 'OR' relationship between all 'tag-filters-groups'
  #  export-fields:
  #  - $tag
  #  - $metrics
  #  extra-headers:  # type: map[string]string, extra http request headers
  #    key1: value1
  #    key2: value2
  #  export-empty-tag: false
  #  export-empty-metrics-disabled: false
  #  enum-translate-to-name-disabled: false
  #  universal-tag-translate-to-name-disabled: false
  #- protocol: opentelemetry
  #  enabled: true
  #  # Randomly select an address that can be sent successfully, otlp address format as: 127.0.0.1:4317, only supports grpc protocol
  #  endpoints: [127.0.0.1:4317, *******:4317]
  #  data-sources: # currently only supports 'flow_log.l7_flow_log'
  #  - flow_log.l7_flow_log
  #  queue-count: 4
  #  queue-size: 100000
  #  batch-size: 32
  #  flush-timeout: 10
  #  tag-filters-groups: # 'OR' relationship between all 'tag-filters-groups'
  #  export-fields:
  #  - $tag
  #  - $metrics
  #  - $k8s.label
  #  export-empty-tag: false
  #  export-empty-metrics-disabled: false
  #  enum-translate-to-name-disabled: false
  #  universal-tag-translate-to-name-disabled: false
  #  extra-headers:  # type: map[string]string, extra http request headers
  #    key1: value1
  #    key2: value2
