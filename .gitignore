# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# IDEA
.idea/
target/

# Dependency directories (remove the comment below to include it)
/server/vendor/
/server/bin/
/server/controller/cloud/aliyun/api_response.gen.go
/server/controller/recorder/cache/cache_test.db
/server/libs/geo/ip_info.go
/server/libs/kubernetes/watcher.gen.go
/cli/vendor/
/cli/bin/
/server/libs/flow-metrics/pb/metric.proto
/server/libs/flow-metrics/pb/metric.pb.go
/server/libs/datatype/pb/flow_log.proto
/server/libs/stats/pb/stats.proto
/server/libs/stats/pb/stats.pb.go
/agent/src/ebpf/kernel/*.ll
/agent/src/ebpf/kernel/*.objdump
/agent/src/ebpf/kernel/*.elf
/agent/src/ebpf/user/socket_trace_bpf_5_2.c
/agent/src/ebpf/user/socket_trace_bpf_core.c
/agent/src/ebpf/user/socket_trace_bpf_common.c
/agent/src/ebpf/user/socket_trace_bpf_5_2_plus.c
/agent/src/ebpf/user/socket_trace_bpf_kylin.c
/agent/src/ebpf/user/perf_profiler_bpf_common.c
/agent/src/ebpf/user/af_packet_fanout_bpf_common.c
/agent/src/ebpf/user/profile/java_agent_so_gnu.c
/agent/src/ebpf/user/profile/java_agent_so_musl.c
/agent/src/ebpf/tools/bintobuffer
/agent/src/ebpf/deepflow-jattach
/agent/src/ebpf/user/profile/deepflow_jattach_bin.c
/agent/src/ebpf/.profiler
/agent/src/ebpf/.socket-tracer
/agent/src/ebpf/samples/rust/profiler/target
/agent/src/ebpf/samples/rust/profiler/src/ebpf.rs
/agent/src/ebpf/samples/rust/socket-tracer/build.rs
/agent/src/ebpf/samples/rust/socket-tracer/target
/agent/src/ebpf/samples/rust/socket-tracer/src/ebpf.rs
/agent/src/ebpf/.flamegraph.pl
/agent/src/ebpf/kernel/*.tmp



