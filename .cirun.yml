runners:
  - name: "aws-amd64-32c"
    cloud: "aws"
    instance_type:
      - "c5a.8xlarge"
      - "c5ad.8xlarge"
      - "c6a.8xlarge"
      - "c6i.8xlarge"
      - "c6id.8xlarge"
      - "cc2.8xlarge"

      - "c5a.8xlarge"
      - "c5ad.8xlarge"
      - "c6a.8xlarge"
      - "c6i.8xlarge"
      - "c6id.8xlarge"
      - "cc2.8xlarge"

      - "c5a.8xlarge"
      - "c5ad.8xlarge"
      - "c6a.8xlarge"
      - "c6i.8xlarge"
      - "c6id.8xlarge"
      - "cc2.8xlarge"

      - "c5a.8xlarge"
      - "c5ad.8xlarge"
      - "c6a.8xlarge"
      - "c6i.8xlarge"
      - "c6id.8xlarge"
      - "cc2.8xlarge"
    machine_image: 
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"

      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"

      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"

      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
    region:
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"

      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"

      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"

      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
    preemptible: false
    labels:
      - "cirun-aws-amd64-32c"
  - name: "aws-arm64-32c"
    cloud: "aws"
    instance_type: 
      - "c6g.8xlarge"
      - "c6gd.8xlarge"
      - "c6gn.8xlarge"
      - "c7g.8xlarge"

      - "c6g.8xlarge"
      - "c6gd.8xlarge"
      - "c6gn.8xlarge"
      - "c7g.8xlarge"

      - "c6g.8xlarge"
      - "c6gd.8xlarge"
      - "c6gn.8xlarge"
      - "c7g.8xlarge"

      - "c6g.8xlarge"
      - "c6gd.8xlarge"
      - "c6gn.8xlarge"
      - "c7g.8xlarge"
    machine_image: 
      - "ami-0f03f0478052da1c5"
      - "ami-0f03f0478052da1c5"
      - "ami-0f03f0478052da1c5"
      - "ami-0f03f0478052da1c5"

      - "ami-06bbad93bd1d7778d"
      - "ami-06bbad93bd1d7778d"
      - "ami-06bbad93bd1d7778d"
      - "ami-06bbad93bd1d7778d"

      - "ami-068535376185489d8"
      - "ami-068535376185489d8"
      - "ami-068535376185489d8"
      - "ami-068535376185489d8"

      - "ami-002a735105deae7f8"
      - "ami-002a735105deae7f8"
      - "ami-002a735105deae7f8"
      - "ami-002a735105deae7f8"
    region:
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"

      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"

      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"

      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
    # arm64 architecture spot instances may not be created smoothly
    preemptible: false
    labels:
      - "cirun-aws-arm64-32c"
  - name: "aws-amd64-16c"
    cloud: "aws"
    instance_type:
      - "c4.4xlarge"
      - "c3.4xlarge"
      - "c6i.4xlarge"
      - "c5ad.4xlarge"
      - "c5.4xlarge"
      - "c5a.4xlarge"
      - "c5d.4xlarge"
      - "c6a.4xlarge"
      - "c6id.4xlarge"

      - "c4.4xlarge"
      - "c3.4xlarge"
      - "c6i.4xlarge"
      - "c5ad.4xlarge"
      - "c5.4xlarge"
      - "c5a.4xlarge"
      - "c5d.4xlarge"
      - "c6a.4xlarge"
      - "c6id.4xlarge"

      - "c4.4xlarge"
      - "c3.4xlarge"
      - "c6i.4xlarge"
      - "c5ad.4xlarge"
      - "c5.4xlarge"
      - "c5a.4xlarge"
      - "c5d.4xlarge"
      - "c6a.4xlarge"
      - "c6id.4xlarge"

      - "c4.4xlarge"
      - "c3.4xlarge"
      - "c6i.4xlarge"
      - "c5ad.4xlarge"
      - "c5.4xlarge"
      - "c5a.4xlarge"
      - "c5d.4xlarge"
      - "c6a.4xlarge"
      - "c6id.4xlarge"
    machine_image: 
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"
      - "ami-080f95b5d948cd7bb"

      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"
      - "ami-07afaa60ce0fcab74"

      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"
      - "ami-05ffc6a38b3556c8d"

      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
      - "ami-01b5d4f8ba101c2fe"
    region:
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"
      - "us-east-2"

      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"
      - "us-west-1"

      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"
      - "us-west-2"

      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
      - "en-west-1"
    preemptible: false
    labels:
      - "cirun-aws-amd64-16c"
